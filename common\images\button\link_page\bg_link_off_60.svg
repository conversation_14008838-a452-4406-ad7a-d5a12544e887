
<svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" preserveAspectRatio="none" x="0px" y="0px" width="124px" height="90px" viewBox="0 0 124 90">
<defs>
<linearGradient id="Gradient_1" gradientUnits="userSpaceOnUse" x1="991.35" y1="67.45" x2="991.35" y2="95.05" spreadMethod="pad">
<stop  offset="5.098039215686274%" stop-color="#E7EFFB"/>

<stop  offset="100%" stop-color="#CBD3E0"/>
</linearGradient>

<filter id="Filter_1" x="-20%" y="-20%" width="140%" height="140%" color-interpolation-filters="sRGB">
<feGaussianBlur in="SourceGraphic" stdDeviation="1.3333333333333333,1.3333333333333333" result="result1"/>
</filter>

<g id="Symbol_68_copy_0_Layer0_0_FILL">
<path fill="#000000" fill-opacity="0.09803921568627451" stroke="none" d="
M 997.65 59.4
Q 993.25 55 987.05 55
L 957 55
Q 950.8 55 946.4 59.4 942 63.8 942 70
L 942 80
Q 942 86.2 946.4 90.6 950.8 95 957 95
L 987.05 95
Q 993.25 95 997.65 90.6 1002.05 86.2 1002.05 80
L 1002.05 70
Q 1002.05 63.8 997.65 59.4 Z"/>
</g>

<g id="Layer6_1_FILL">
<path fill="url(#Gradient_1)" stroke="none" d="
M 997.65 59.4
Q 993.25 55 987.05 55
L 957 55
Q 950.8 55 946.4 59.4 942 63.8 942 70
L 942 80
Q 942 86.2 946.4 90.6 950.8 95 957 95
L 987.05 95
Q 993.25 95 997.65 90.6 1002.05 86.2 1002.05 80
L 1002.05 70
Q 1002.05 63.8 997.65 59.4 Z"/>
</g>

<g id="Layer6_2_FILL">
<path fill="#000000" fill-opacity="0.047058823529411764" stroke="none" d="
M 55.6 35.6
Q 60 31.2 60 25
L 60 23.15
Q 59.8 28.9 55.6 33.1 51.2 37.5 45 37.5
L 15 37.5
Q 8.8 37.5 4.4 33.1 0.111328125 28.811328125 0 22.8
L 0 25
Q 0 31.2 4.4 35.6 8.8 40 15 40
L 45 40
Q 51.2 40 55.6 35.6 Z"/>
</g>
</defs>

<g filter="url(#Filter_1)" transform="matrix( 1, 0, 0, 1, 0,0) ">
<g transform="matrix( 2, 0, 0, 2, 2,7.5) ">
<g transform="matrix( 0.99884033203125, 0, 0, 1, -940.9,-55) ">
<use xlink:href="#Symbol_68_copy_0_Layer0_0_FILL"/>
</g>
</g>
</g>

<g transform="matrix( 1.9976806640625, 0, 0, 2, -1879.8,-107.5) ">
<use xlink:href="#Layer6_1_FILL"/>
</g>

<g transform="matrix( 2, 0, 0, 2, 2,2.5) ">
<use xlink:href="#Layer6_2_FILL"/>
</g>
</svg>
