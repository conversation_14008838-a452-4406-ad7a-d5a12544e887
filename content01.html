<!doctype html>
<html lang="ko">
  <head>
    <meta charset="UTF-8">
    <title>content01</title>
  <meta name="viewport" content="width=device-width,initial-scale=0"><script defer="defer" src="./js/content01.bundle.js"></script><link href="./css/content01.bundle.css" rel="stylesheet"></head>
  <body class="learning-expression activity">
    <div class="root">
      <header class="main-header">
        <h1 class="title"><span class="deco">표현</span>익히기</h1>
        <div class="direction-container">
          <h2 class="direction">잘 듣고, 알맞은 그림을 박스에 넣어 보세요.</h2>
        </div>
        <button class="btn-dir" type="button" aria-label="지시문 오디오" data-hover="true"></button>
      </header>
      <main class="content">
        <nav class="nav-top">
          <a class="btn-link" href="./index.html" data-hover="true">
            <span>듣기</span>
          </a>
          <div class="btn-link current">
            <span>활동하기</span>
          </div>
          <a class="btn-link" href="./content02.html" data-hover="true">
            <span>따라 말하기</span>
          </a>
        </nav>

        <div class="content">
          <div class="bg-0"></div>

          <!-- 드래그 아이템 -->
          <div class="drag-item drag-item-0" aria-label="졸린 남자아이 사진에 가위 표시" role="img"></div>
          <div class="drag-item drag-item-1" aria-label="행복한 Sam의 사진에 동그라미 표시" role="img"></div>
          <div class="drag-item drag-item-2" aria-label="목이 마른 여자 아이 사진에 가위 표시" role="img"></div>
          <div class="drag-item drag-item-3" aria-label="지친 여자 아이 사진에 동그라미 표시" role="img"></div>
          <div class="drag-item drag-item-4" aria-label="울고 있는 남자아이 사진에 동그라미 표시" role="img"></div>
          <div class="drag-item drag-item-5" aria-label="졸린 남자아이 사진에 동그라미 표시" role="img"></div>
          <div class="drag-item drag-item-6" aria-label="화난 여자아이 사진에 동그라미 표시" role="img"></div>
          <div class="drag-item drag-item-7" aria-label="목이 마른 여자 아이 사진에 동그라미 표시" role="img"></div>
          <div class="drag-item drag-item-8" aria-label="행복한 Sam의 사진에 가위 표시" role="img"></div>

          <!-- 드래그 영역 -->
          <div class="drag-area drag-area-0" data-fill=""></div>
          <div class="drag-area drag-area-1" data-fill=""></div>
          <div class="drag-area drag-area-2" data-fill=""></div>
          <div class="drag-area drag-area-3" data-fill=""></div>
          <div class="drag-area drag-area-4" data-fill=""></div>
          <div class="drag-area drag-area-5" data-fill=""></div>
          <div class="drag-area drag-area-6" data-fill=""></div>
          <div class="drag-area drag-area-7" data-fill=""></div>
          <div class="drag-area drag-area-8" data-fill=""></div>

          <!-- Sound Button -->
          <input type="checkbox" class="btn-sound btn-sound-0" data-index="0">
          <input type="checkbox" class="btn-sound btn-sound-1" data-index="1">
          <input type="checkbox" class="btn-sound btn-sound-2" data-index="2">
          <input type="checkbox" class="btn-sound btn-sound-3" data-index="3">
          <input type="checkbox" class="btn-sound btn-sound-4" data-index="4">
          <input type="checkbox" class="btn-sound btn-sound-5" data-index="5">
          <input type="checkbox" class="btn-sound btn-sound-6" data-index="6">
          <input type="checkbox" class="btn-sound btn-sound-7" data-index="7">
          <input type="checkbox" class="btn-sound btn-sound-8" data-index="8">

          <div class="main-button">
            <div class="l-side">
              <button class="btn-script" type="button" data-hover="true">
                <span>대본</span>
              </button>
            </div>
            <div class="r-side">
              <div class="checkbox-container">
                <input class="btn-answer" type="checkbox" id="btn-answer-input" data-hover="true">
                <label for="btn-answer-input">
                  <span class="off">정답</span>
                  <span class="on">다시 하기</span>
                </label>
              </div>
            </div>
          </div>

          <div class="popup popup-alert hide"></div>
        </div>
      </main>

      <div class="popup-script hide"></div>
    </div>
    <script src="https://aidtcdn-dev.aitextbook.co.kr/content/js/dtext_api_wrapper_opr.js" defer="defer"></script>
  </body>
</html>
