(function (cjs, an) {

var p; // shortcut to reference prototypes
var lib={};var ss={};var img={};
lib.ssMetadata = [
		{name:"animation_atlas_1", frames: [[359,0,132,196],[0,363,136,124],[282,380,142,113],[138,380,142,114],[196,0,161,209],[210,211,173,167],[0,0,194,198],[0,211,208,150],[385,198,97,168]]},
		{name:"animation_atlas_2", frames: [[241,122,30,7],[241,92,32,15],[493,42,14,19],[0,409,39,19],[247,394,64,53],[0,352,64,55],[493,0,16,40],[0,297,68,53],[313,420,62,6],[241,176,106,77],[0,131,112,76],[0,0,117,129],[493,63,14,19],[493,84,14,19],[119,0,159,90],[241,131,26,8],[241,109,32,11],[136,365,39,21],[443,405,40,20],[136,272,40,91],[92,272,42,152],[202,395,43,22],[359,166,44,13],[428,324,45,79],[377,370,46,21],[328,286,47,132],[377,286,49,82],[432,185,53,137],[273,255,53,137],[180,92,59,147],[119,92,59,178],[430,0,61,183],[377,405,64,44],[136,395,64,53],[359,0,69,164],[280,0,77,174],[349,185,81,99],[0,209,90,86],[180,255,91,83],[178,340,67,53]]}
];


(lib.AnMovieClip = function(){
	this.actionFrames = [];
	this.ignorePause = false;
	this.gotoAndPlay = function(positionOrLabel){
		cjs.MovieClip.prototype.gotoAndPlay.call(this,positionOrLabel);
	}
	this.play = function(){
		cjs.MovieClip.prototype.play.call(this);
	}
	this.gotoAndStop = function(positionOrLabel){
		cjs.MovieClip.prototype.gotoAndStop.call(this,positionOrLabel);
	}
	this.stop = function(){
		cjs.MovieClip.prototype.stop.call(this);
	}
}).prototype = p = new cjs.MovieClip();
// symbols:



(lib.Bitmap37_n_117 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(0);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap38_n_118 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(1);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap39_n_119 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(2);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap40_n_120 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(3);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap42_n_121 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(4);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap43_n_122 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(5);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap44_n_123 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(6);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap49_n_124 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(7);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap50_n_125 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(8);
}).prototype = p = new cjs.Sprite();



(lib.bitmap106x77_7_n_90_n_126 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(9);
}).prototype = p = new cjs.Sprite();



(lib.bitmap112x76_11_n_91_n_127 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(10);
}).prototype = p = new cjs.Sprite();



(lib.bitmap117x129_33_n_92_n_128 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(11);
}).prototype = p = new cjs.Sprite();



(lib.bitmap132x196_24_n_93_n_129 = function() {
	this.initialize(ss["animation_atlas_1"]);
	this.gotoAndStop(0);
}).prototype = p = new cjs.Sprite();



(lib.bitmap136x124_28_n_94_n_130 = function() {
	this.initialize(ss["animation_atlas_1"]);
	this.gotoAndStop(1);
}).prototype = p = new cjs.Sprite();



(lib.bitmap142x113_31_n_95_n_131 = function() {
	this.initialize(ss["animation_atlas_1"]);
	this.gotoAndStop(2);
}).prototype = p = new cjs.Sprite();



(lib.bitmap142x114_27_n_96_n_132 = function() {
	this.initialize(ss["animation_atlas_1"]);
	this.gotoAndStop(3);
}).prototype = p = new cjs.Sprite();



(lib.bitmap14x19_21_n_98_n_133 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(12);
}).prototype = p = new cjs.Sprite();



(lib.bitmap14x19_2_n_97_n_134 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(13);
}).prototype = p = new cjs.Sprite();



(lib.bitmap159x90_32_n_99_n_135 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(14);
}).prototype = p = new cjs.Sprite();



(lib.bitmap161x209_17_n_100_n_136 = function() {
	this.initialize(ss["animation_atlas_1"]);
	this.gotoAndStop(4);
}).prototype = p = new cjs.Sprite();



(lib.bitmap173x167_23_n_101_n_137 = function() {
	this.initialize(ss["animation_atlas_1"]);
	this.gotoAndStop(5);
}).prototype = p = new cjs.Sprite();



(lib.bitmap194x198_36_n_102_n_138 = function() {
	this.initialize(ss["animation_atlas_1"]);
	this.gotoAndStop(6);
}).prototype = p = new cjs.Sprite();



(lib.bitmap208x150_4_n_103_n_139 = function() {
	this.initialize(ss["animation_atlas_1"]);
	this.gotoAndStop(7);
}).prototype = p = new cjs.Sprite();



(lib.bitmap26x8_1_n_104_n_140 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(15);
}).prototype = p = new cjs.Sprite();



(lib.bitmap32x11_38_n_105_n_141 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(16);
}).prototype = p = new cjs.Sprite();



(lib.bitmap39x21_0_n_106_n_142 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(17);
}).prototype = p = new cjs.Sprite();



(lib.bitmap40x20_3_n_107_n_143 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(18);
}).prototype = p = new cjs.Sprite();



(lib.bitmap40x91_14_n_108_n_144 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(19);
}).prototype = p = new cjs.Sprite();



(lib.bitmap42x152_5_n_109_n_145 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(20);
}).prototype = p = new cjs.Sprite();



(lib.bitmap43x22_20_n_110_n_146 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(21);
}).prototype = p = new cjs.Sprite();



(lib.bitmap44x13_19_n_111_n_147 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(22);
}).prototype = p = new cjs.Sprite();



(lib.bitmap45x79_34_n_112_n_148 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(23);
}).prototype = p = new cjs.Sprite();



(lib.bitmap46x21_22_n_113_n_149 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(24);
}).prototype = p = new cjs.Sprite();



(lib.bitmap47x132_35_n_114_n_150 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(25);
}).prototype = p = new cjs.Sprite();



(lib.bitmap49x82_15_n_115_n_151 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(26);
}).prototype = p = new cjs.Sprite();



(lib.bitmap53x137_10_n_116_n_152 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(27);
}).prototype = p = new cjs.Sprite();



(lib.bitmap53x137_6_n_117_n_153 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(28);
}).prototype = p = new cjs.Sprite();



(lib.bitmap59x147_9_n_118_n_154 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(29);
}).prototype = p = new cjs.Sprite();



(lib.bitmap59x178_29_n_119_n_155 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(30);
}).prototype = p = new cjs.Sprite();



(lib.bitmap61x183_25_n_120_n_156 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(31);
}).prototype = p = new cjs.Sprite();



(lib.bitmap64x44_37_n_121_n_157 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(32);
}).prototype = p = new cjs.Sprite();



(lib.bitmap64x53_18_n_122_n_158 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(33);
}).prototype = p = new cjs.Sprite();



(lib.bitmap69x164_30_n_123_n_159 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(34);
}).prototype = p = new cjs.Sprite();



(lib.bitmap77x174_26_n_124_n_160 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(35);
}).prototype = p = new cjs.Sprite();



(lib.bitmap81x99_12_n_125_n_161 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(36);
}).prototype = p = new cjs.Sprite();



(lib.bitmap90x86_8_n_126_n_162 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(37);
}).prototype = p = new cjs.Sprite();



(lib.bitmap91x83_13_n_127_n_163 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(38);
}).prototype = p = new cjs.Sprite();



(lib.bitmap97x168_16_n_128_n_164 = function() {
	this.initialize(ss["animation_atlas_1"]);
	this.gotoAndStop(8);
}).prototype = p = new cjs.Sprite();



(lib.비트맵3_n_165 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(39);
}).prototype = p = new cjs.Sprite();



(lib.샘머리_n_54_n_14 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.bitmap173x167_23_n_101_n_137();
	this.instance.setTransform(1.5,5.6);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(1.5,5.6,173,167);


(lib.샘_다리_n_72_n_32 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.bitmap194x198_36_n_102_n_138();
	this.instance.setTransform(0.35,0);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(0.4,0,194,198);


(lib.샘_눈_n_43_n_3 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.bitmap64x53_18_n_122_n_158();
	this.instance.setTransform(0.55,5.4);

	this.instance_1 = new lib.Bitmap42_n_121();
	this.instance_1.setTransform(0.15,5.4);

	this.instance_2 = new lib.Bitmap43_n_122();
	this.instance_2.setTransform(0.55,3.2);

	this.instance_3 = new lib.비트맵3_n_165();
	this.instance_3.setTransform(-1,5);

	this.instance_4 = new lib.Bitmap50_n_125();
	this.instance_4.setTransform(1.95,47.4);

	this.instance_5 = new lib.Bitmap49_n_124();
	this.instance_5.setTransform(-1.75,5.4);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.instance}]}).to({state:[{t:this.instance_1}]},1).to({state:[{t:this.instance_2}]},1).to({state:[{t:this.instance_3}]},1).to({state:[{t:this.instance_5},{t:this.instance_4}]},1).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-1.7,3.2,68,55.199999999999996);


(lib.보라_다리_n_35_n_102 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.bitmap161x209_17_n_100_n_136();
	this.instance.setTransform(-0.05,-1);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(0,-1,161,209);


(lib.보라_눈_n_1_n_68 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.bitmap64x44_37_n_121_n_157();
	this.instance.setTransform(0.25,1.1);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(0.3,1.1,64,44);


(lib.vgfgf_n_69_n_29 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.bitmap132x196_24_n_93_n_129();
	this.instance.setTransform(-1,0);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-1,0,132,196);


(lib.vgffxw_n_20_n_87 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.bitmap208x150_4_n_103_n_139();
	this.instance.setTransform(1.6,-0.3);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(1.6,-0.3,208,150);


(lib.vdegg_n_23_n_90 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.bitmap97x168_16_n_128_n_164();
	this.instance.setTransform(1.2,0.3);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(1.2,0.3,97,168);


(lib.샘입copy2_n_53_n_13 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.bitmap44x13_19_n_111_n_147();
	this.instance.setTransform(-1,-1);

	this.instance_1 = new lib.bitmap43x22_20_n_110_n_146();
	this.instance_1.setTransform(0.5,-1.5);

	this.instance_2 = new lib.bitmap14x19_21_n_98_n_133();
	this.instance_2.setTransform(14.75,2.9);

	this.instance_3 = new lib.bitmap46x21_22_n_113_n_149();
	this.instance_3.setTransform(-1.9,-1.2);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.instance}]}).to({state:[{t:this.instance_1}]},5).to({state:[{t:this.instance}]},5).to({state:[{t:this.instance_2}]},5).to({state:[{t:this.instance_3}]},5).wait(5));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-1.9,-1.5,46,23.4);


(lib.샘입_우울_n_52 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_3
	this.instance = new lib.Bitmap37_n_117();
	this.instance.setTransform(8.1,8.4);

	this.instance_1 = new lib.Bitmap38_n_118();
	this.instance_1.setTransform(8.85,4.65);

	this.instance_2 = new lib.Bitmap39_n_119();
	this.instance_2.setTransform(14.75,2.9);

	this.instance_3 = new lib.Bitmap40_n_120();
	this.instance_3.setTransform(3.85,1.15);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.instance}]}).to({state:[{t:this.instance_1}]},5).to({state:[{t:this.instance}]},5).to({state:[{t:this.instance_2}]},5).to({state:[{t:this.instance_3}]},5).wait(5));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(3.9,1.2,39,20.7);


(lib.Symbol149_n_26_n_93 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.bitmap53x137_10_n_116_n_152();
	this.instance.setTransform(1.35,-0.4);

	this.instance_1 = new lib.bitmap112x76_11_n_91_n_127();
	this.instance_1.setTransform(2.05,-0.5);

	this.instance_2 = new lib.bitmap81x99_12_n_125_n_161();
	this.instance_2.setTransform(2.05,-26.05);

	this.instance_3 = new lib.bitmap91x83_13_n_127_n_163();
	this.instance_3.setTransform(2.05,-10.1);

	this.instance_4 = new lib.bitmap40x91_14_n_108_n_144();

	this.instance_5 = new lib.bitmap49x82_15_n_115_n_151();
	this.instance_5.setTransform(10.6,65.5);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[]}).to({state:[{t:this.instance}]},1).to({state:[{t:this.instance_1}]},1).to({state:[{t:this.instance_2}]},1).to({state:[{t:this.instance_3}]},1).to({state:[{t:this.instance_4}]},1).to({state:[{t:this.instance_5}]},1).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(0,-26,114.1,173.5);


(lib.Symbol148_n_10_n_77 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.bitmap42x152_5_n_109_n_145();
	this.instance.setTransform(0,0.35);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({_off:true},1).wait(3));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(0,0,42,152.4);


(lib.Symbol140copy2_n_78_n_38 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.bitmap69x164_30_n_123_n_159();
	this.instance.setTransform(-29.6,0.05);

	this.instance_1 = new lib.bitmap142x113_31_n_95_n_131();
	this.instance_1.setTransform(-101.9,-32.95);

	this.instance_2 = new lib.bitmap159x90_32_n_99_n_135();
	this.instance_2.setTransform(-120.2,-9.15);

	this.instance_3 = new lib.bitmap117x129_33_n_92_n_128();
	this.instance_3.setTransform(-76.85,-48.9);

	this.instance_4 = new lib.bitmap45x79_34_n_112_n_148();
	this.instance_4.setTransform(5.95,0.6);

	this.instance_5 = new lib.bitmap47x132_35_n_114_n_150();
	this.instance_5.setTransform(5.9,39.55);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[]}).to({state:[{t:this.instance}]},1).to({state:[{t:this.instance_1}]},1).to({state:[{t:this.instance_2}]},1).to({state:[{t:this.instance_3}]},1).to({state:[{t:this.instance_4}]},1).to({state:[{t:this.instance_5}]},1).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-120.2,-48.9,173.1,220.5);


(lib.Symbol135copy_n_59_n_19 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.bitmap61x183_25_n_120_n_156();
	this.instance.setTransform(-1,-1);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({_off:true},1).wait(3));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-1,-1,61,183);


(lib.Symbol31copy_n_9_n_76 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.bitmap32x11_38_n_105_n_141();
	this.instance.setTransform(-1,-1);

	this.instance_1 = new lib.bitmap39x21_0_n_106_n_142();
	this.instance_1.setTransform(-1.65,-2.65);

	this.instance_2 = new lib.bitmap26x8_1_n_104_n_140();
	this.instance_2.setTransform(4.55,4.4);

	this.instance_3 = new lib.bitmap14x19_2_n_97_n_134();
	this.instance_3.setTransform(13.8,1.25);

	this.instance_4 = new lib.bitmap40x20_3_n_107_n_143();
	this.instance_4.setTransform(0.25,-1.5);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.instance}]}).to({state:[{t:this.instance_1}]},5).to({state:[{t:this.instance_2}]},5).to({state:[{t:this.instance_3}]},5).to({state:[{t:this.instance_4}]},5).wait(5));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-1.6,-2.6,41.9,22.900000000000002);


(lib.brgt_n_60 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.Bitmap44_n_123();

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(0,0,16,40);


(lib.Symbol87_n_0_n_67 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// 보라_눈
	this.instance = new lib.보라_눈_n_1_n_68("single",0);
	this.instance.setTransform(67.05,83.45,1,1,0,0,0,32.1,22.1);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(7).to({startPosition:0},0).to({rotation:-2.9695,x:64.15,y:87.75},7).to({rotation:3.4379,x:78.2,y:82.5},7).wait(20).to({startPosition:0},0).to({rotation:-2.741,x:64.1,y:87.55},9).to({rotation:-1.0273,x:65.9,y:84.95},5).to({_off:true},1).wait(30));

	// Symbol_31_copy
	this.instance_1 = new lib.Symbol31copy_n_9_n_76("single",0);
	this.instance_1.setTransform(70.05,114.75,0.8958,0.8958,0,3.9361,-176.0639,15,4.2);

	this.timeline.addTween(cjs.Tween.get(this.instance_1).wait(7).to({mode:"synched",startPosition:6},0).to({skewX:0.9662,skewY:-179.0338,x:68.75,y:118.8,startPosition:13},7).to({skewX:7.3741,skewY:-172.6259,x:79.2,y:114,startPosition:20},7).wait(16).to({mode:"single",startPosition:0},0).wait(4).to({startPosition:0},0).to({skewX:1.1946,skewY:-178.8054,x:68.6,y:118.65,startPosition:4},9).to({skewX:2.9067,skewY:-177.0933,x:69.6,y:116.25},5).to({_off:true},1).wait(30));

	// Symbol_148
	this.instance_2 = new lib.Symbol148_n_10_n_77("single",0);
	this.instance_2.setTransform(126.4,160.5,1.0447,1.0447,0,1.0461,-178.9539,34.3,3.8);

	this.timeline.addTween(cjs.Tween.get(this.instance_2).wait(7).to({startPosition:0},0).to({skewX:5.5006,skewY:-174.4994,y:163.9},7).to({regX:34.2,skewX:-3.4783,skewY:-183.4783,x:132.9,y:163},7).wait(20).to({skewX:-3.4783},0).to({skewX:3.5379,skewY:-176.4621,x:126.5,y:163.75},9).to({skewX:1.9813,skewY:-178.0187,y:161.65},5).to({_off:true},1).wait(30));

	// Layer_22
	this.instance_3 = new lib.vgffxw_n_20_n_87("synched",0);
	this.instance_3.setTransform(85.65,146.8,1,1,0,0,0,85.8,147.2);

	this.timeline.addTween(cjs.Tween.get(this.instance_3).wait(7).to({startPosition:0},0).to({regX:85.7,rotation:-2.741,x:85.6,y:150.45},7).to({rotation:3.4888,x:92.85,y:146.75},7).wait(20).to({startPosition:0},0).to({regX:85.8,rotation:-2.741,x:85.8,y:150},9).to({regX:85.7,regY:147.3,rotation:-1.0273,x:85.7,y:148},5).to({_off:true},1).wait(30));

	// vdegg
	this.instance_4 = new lib.vdegg_n_23_n_90("synched",0);
	this.instance_4.setTransform(93.1,207.1,1,1,0,0,0,50,84.8);

	this.timeline.addTween(cjs.Tween.get(this.instance_4).wait(7).to({startPosition:0},0).to({y:210.5},7).to({regX:50.1,regY:84.9,rotation:3.4379,x:96.8,y:207.65},7).wait(20).to({startPosition:0},0).to({regX:50,regY:84.8,rotation:0,x:93.1,y:210.3},9).to({y:208.3},5).to({_off:true},1).wait(30));

	// Symbol_149
	this.instance_5 = new lib.Symbol149_n_26_n_93("single",5);
	this.instance_5.setTransform(56.8,162.05,1.0447,1.0447,0,-5.4159,174.5841,6.1,4.9);

	this.timeline.addTween(cjs.Tween.get(this.instance_5).wait(7).to({startPosition:5},0).to({regX:6,skewX:-8.8966,skewY:171.1034,x:56.9,y:165.45},7).to({regY:5,skewX:19.9682,skewY:199.9682,x:63.2,y:160.5},7).wait(20).to({startPosition:5},0).to({skewX:-9.6274,skewY:170.3726,x:56.5,y:165.1},9).to({skewX:-6.9962,skewY:173.0038,x:56.75,y:163.3},5).to({_off:true},1).wait(30));

	// 보라_다리
	this.instance_6 = new lib.보라_다리_n_35_n_102("single",0);
	this.instance_6.setTransform(87.4,371.55,1,1,0,0,0,80.6,103.9);

	this.timeline.addTween(cjs.Tween.get(this.instance_6).wait(7).to({startPosition:0},0).to({startPosition:0},7).to({startPosition:0},7).wait(20).to({startPosition:0},0).to({startPosition:0},9).to({startPosition:0},5).to({_off:true},1).wait(30));

	// Symbol_149
	this.instance_7 = new lib.Symbol149_n_26_n_93("single",6);
	this.instance_7.setTransform(43.75,230.65,1.0447,1.0447,0,-5.4159,174.5841,24.7,69.1);

	this.timeline.addTween(cjs.Tween.get(this.instance_7).wait(7).to({startPosition:6},0).to({skewX:-8.8966,skewY:171.1034,x:47.95,y:234.75},7).to({regX:24.6,regY:69,scaleX:1.0446,scaleY:1.0446,skewX:103.9912,skewY:283.9912,x:21.6,y:223},7).wait(20).to({skewX:103.9912,skewY:283.9912},0).to({regX:24.7,regY:69.1,scaleX:1.0447,scaleY:1.0447,skewX:-9.6274,skewY:170.3726,x:48.45,y:234.35},9).to({regY:69.2,skewX:-6.9962,skewY:173.0038,x:45.55,y:232.2},5).to({_off:true},1).wait(30));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-61.5,-5.6,287,481.3);


(lib.Symbol85_n_42_n_2 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// 샘_눈
	this.instance = new lib.샘_눈_n_43_n_3("single",0);
	this.instance.setTransform(81.5,110.05,1,1,0,0,0,32.5,29.1);
	this.instance._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1).to({_off:false},0).to({startPosition:0},58).to({regX:32.6,rotation:9.3844,x:100.1,y:103.65},6).to({y:106.65},5).to({startPosition:0},34).to({regX:32.5,rotation:0,x:81.5,y:113.05},6).to({y:110.05},5).to({startPosition:0},3).to({_off:true},1).wait(7));

	// 샘_입_copy_2
	this.instance_1 = new lib.샘입copy2_n_53_n_13("single",0);
	this.instance_1.setTransform(85.4,146.3,1.1229,1.1229,1.467,0,0,21.6,6);
	this.instance_1._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_1).wait(1).to({_off:false},0).wait(58).to({mode:"synched",startPosition:4},0).to({rotation:10.8515,x:98,y:140,startPosition:10},6).to({y:143},5).wait(26).to({mode:"single",startPosition:11},0).wait(8).to({startPosition:0},0).to({rotation:1.467,x:85.4,y:149.3},6).to({rotation:1.467,y:146.3},5).to({startPosition:0},3).to({_off:true},1).wait(7));

	// 샘머리
	this.instance_2 = new lib.샘머리_n_54_n_14("synched",0);
	this.instance_2.setTransform(100.4,171.25,1,1,0,0,0,88.7,171.5);
	this.instance_2._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_2).wait(1).to({_off:false},0).wait(58).to({startPosition:0},0).to({regX:88.8,regY:171.4,rotation:9.3844,x:108.85,y:167.15},6).to({y:170.15},5).wait(34).to({startPosition:0},0).to({regX:88.7,regY:171.5,rotation:0,x:100.4,y:174.25},6).to({y:171.25},5).wait(3).to({startPosition:0},0).to({_off:true},1).wait(7));

	// Symbol_135_copy
	this.instance_3 = new lib.Symbol135copy_n_59_n_19("single",0);
	this.instance_3.setTransform(161.05,190.85,0.995,0.995,0,0,0,32,2.1);
	this.instance_3._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_3).wait(1).to({_off:false},0).wait(58).to({startPosition:0},0).to({regX:32.2,regY:2.3,rotation:-16.4592,x:168.5,y:191.65},6).to({regX:32.1,regY:2.2,rotation:-10.9937,x:168.35,y:194.55},5).wait(34).to({startPosition:0},0).to({rotation:1.4834,x:161.15,y:193.95},6).to({regX:32,regY:2.1,rotation:0,x:161.05,y:190.85},5).wait(3).to({startPosition:0},0).to({_off:true},1).wait(7));

	// 샘_후드1
	this.instance_4 = new lib.vgfgf_n_69_n_29("single",0);
	this.instance_4.setTransform(110.3,242.45,1,1,0,0,0,65,97.8);
	this.instance_4._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_4).wait(1).to({_off:false},0).wait(58).to({startPosition:0},0).to({regX:65.1,rotation:3.1962,x:114.85,y:240.15},6).to({y:243.15},5).wait(34).to({startPosition:0},0).to({regX:65,rotation:0,x:110.3,y:245.45},6).to({y:242.45},5).wait(3).to({startPosition:0},0).to({_off:true},1).wait(7));

	// 샘_다리
	this.instance_5 = new lib.샘_다리_n_72_n_32("single",0);
	this.instance_5.setTransform(92.9,306.25,1,1,0,0,0,93.2,8.4);
	this.instance_5._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_5).wait(1).to({_off:false},0).wait(58).to({startPosition:0},0).to({startPosition:0},6).to({startPosition:0},5).wait(34).to({startPosition:0},0).to({startPosition:0},6).to({startPosition:0},5).wait(3).to({startPosition:0},0).to({_off:true},1).wait(7));

	// Symbol_140_copy_2
	this.instance_6 = new lib.Symbol140copy2_n_78_n_38("single",6);
	this.instance_6.setTransform(60.85,229.4,1.0471,1.0471,3.9967,0,0,28.8,44.2);
	this.instance_6._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_6).wait(1).to({_off:false},0).wait(58).to({startPosition:6},0).to({regY:44.1,rotation:115.4649,x:62.25,y:248.85},6).to({rotation:111.2428,x:67,y:252.6},5).wait(34).to({startPosition:6},0).to({regY:44.2,rotation:2.0201,x:62.25,y:232.5},6).to({rotation:3.9967,x:60.85,y:229.4},5).wait(3).to({startPosition:6},0).to({_off:true},1).wait(7));

	// Symbol_140_copy_2
	this.instance_7 = new lib.Symbol140copy2_n_78_n_38("single",5);
	this.instance_7.setTransform(65.5,185.35,1.0471,1.0471,3.9967,0,0,30.3,1.9);
	this.instance_7._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_7).wait(1).to({_off:false},0).wait(58).to({startPosition:5},0).to({regX:30.4,regY:2,rotation:21.6315,x:73.6,y:180.55},6).to({regX:30.3,regY:1.9,rotation:17.4081,x:73.2,y:183.6},5).wait(34).to({startPosition:5},0).to({regY:2,rotation:2.0201,x:65.4,y:188.4},6).to({regY:1.9,rotation:3.9967,x:65.5,y:185.35},5).wait(3).to({startPosition:5},0).to({_off:true},1).wait(7));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-73.8,-10.6,319.5,506.5);


(lib.Symbol85_n_42_5_n_62 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// 샘_눈
	this.instance = new lib.샘_눈_n_43_n_3("single",4);
	this.instance.setTransform(81.5,110.05,1,1,0,0,0,32.5,29.1);
	this.instance._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1).to({_off:false},0).to({startPosition:4},58).to({regX:32.6,rotation:9.3844,x:100.1,y:103.65},6).to({y:106.65},5).to({startPosition:4},34).to({regX:32.5,rotation:0,x:81.5,y:113.05},6).to({y:110.05},5).wait(11));

	// 샘_입_copy_2
	this.instance_1 = new lib.샘입_우울_n_52("single",0);
	this.instance_1.setTransform(85.4,146.3,1.1229,1.1229,1.467,0,0,21.6,6);
	this.instance_1._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_1).wait(1).to({_off:false},0).wait(58).to({mode:"synched",startPosition:4,loop:false},0).to({rotation:10.8515,x:98,y:140,startPosition:10},6).to({y:143},5).wait(26).to({mode:"single",startPosition:11},0).wait(8).to({startPosition:0},0).to({rotation:1.467,x:85.4,y:149.3},6).to({rotation:1.467,y:146.3},5).wait(11));

	// 샘머리
	this.instance_2 = new lib.샘머리_n_54_n_14("synched",0);
	this.instance_2.setTransform(100.4,171.25,1,1,0,0,0,88.7,171.5);
	this.instance_2._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_2).wait(1).to({_off:false},0).wait(58).to({startPosition:0},0).to({regX:88.8,regY:171.4,rotation:9.3844,x:108.85,y:167.15},6).to({y:170.15},5).wait(34).to({startPosition:0},0).to({regX:88.7,regY:171.5,rotation:0,x:100.4,y:174.25},6).to({y:171.25},5).wait(11));

	// Symbol_135_copy
	this.instance_3 = new lib.Symbol135copy_n_59_n_19("single",0);
	this.instance_3.setTransform(161.05,190.85,0.995,0.995,0,0,0,32,2.1);
	this.instance_3._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_3).wait(1).to({_off:false},0).wait(58).to({startPosition:0},0).to({regX:32.2,regY:2.3,rotation:-16.4592,x:168.5,y:191.65},6).to({regX:32.1,regY:2.2,rotation:-10.9937,x:168.35,y:194.55},5).wait(34).to({startPosition:0},0).to({rotation:1.4834,x:161.15,y:193.95},6).to({regX:32,regY:2.1,rotation:0,x:161.05,y:190.85},5).wait(11));

	// 샘_후드1
	this.instance_4 = new lib.vgfgf_n_69_n_29("single",0);
	this.instance_4.setTransform(110.3,242.45,1,1,0,0,0,65,97.8);
	this.instance_4._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_4).wait(1).to({_off:false},0).wait(58).to({startPosition:0},0).to({regX:65.1,rotation:3.1962,x:114.85,y:240.15},6).to({y:243.15},5).wait(34).to({startPosition:0},0).to({regX:65,rotation:0,x:110.3,y:245.45},6).to({y:242.45},5).wait(11));

	// 샘_다리
	this.instance_5 = new lib.샘_다리_n_72_n_32("single",0);
	this.instance_5.setTransform(92.9,306.25,1,1,0,0,0,93.2,8.4);
	this.instance_5._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_5).wait(1).to({_off:false},0).wait(58).to({startPosition:0},0).to({startPosition:0},6).to({startPosition:0},5).wait(34).to({startPosition:0},0).to({startPosition:0},6).to({startPosition:0},5).wait(11));

	// Symbol_140_copy_2
	this.instance_6 = new lib.Symbol140copy2_n_78_n_38("single",6);
	this.instance_6.setTransform(60.85,229.4,1.0471,1.0471,3.9967,0,0,28.8,44.2);
	this.instance_6._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_6).wait(1).to({_off:false},0).wait(58).to({startPosition:6},0).to({regY:44.1,rotation:115.4649,x:62.25,y:248.85},6).to({rotation:111.2428,x:67,y:252.6},5).wait(34).to({startPosition:6},0).to({regY:44.2,rotation:2.0201,x:62.25,y:232.5},6).to({rotation:3.9967,x:60.85,y:229.4},5).wait(11));

	// Symbol_140_copy_2
	this.instance_7 = new lib.Symbol140copy2_n_78_n_38("single",5);
	this.instance_7.setTransform(65.5,185.35,1.0471,1.0471,3.9967,0,0,30.3,1.9);
	this.instance_7._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_7).wait(1).to({_off:false},0).wait(58).to({startPosition:5},0).to({regX:30.4,regY:2,rotation:21.6315,x:73.6,y:180.55},6).to({regX:30.3,regY:1.9,rotation:17.4081,x:73.2,y:183.6},5).wait(34).to({startPosition:5},0).to({regY:2,rotation:2.0201,x:65.4,y:188.4},6).to({regY:1.9,rotation:3.9967,x:65.5,y:185.35},5).wait(11));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-73.8,-10.6,319.5,506.5);


(lib.Symbol85_n_42_4_n_59 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// 샘_눈
	this.instance = new lib.샘_눈_n_43_n_3("single",1);
	this.instance.setTransform(81.5,110.05,1,1,0,0,0,32.5,29.1);
	this.instance._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1).to({_off:false},0).to({startPosition:1},58).to({regX:32.6,rotation:9.3844,x:100.1,y:103.65},6).to({y:106.65},5).to({startPosition:1},34).to({regX:32.5,rotation:0,x:81.5,y:113.05},6).to({y:110.05},5).wait(11));

	// 샘_입_copy_2
	this.instance_1 = new lib.샘입_우울_n_52("single",5);
	this.instance_1.setTransform(85.4,146.3,1.1229,1.1229,1.467,0,0,21.6,6);
	this.instance_1._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_1).wait(1).to({_off:false},0).wait(58).to({startPosition:0},0).to({rotation:10.8515,x:98,y:140},6).to({y:143,mode:"synched",startPosition:10},5).wait(26).to({mode:"single",startPosition:11},0).wait(8).to({startPosition:20},0).to({rotation:1.467,x:85.4,y:149.3},6).to({rotation:1.467,y:146.3},5).wait(11));

	// Layer_4
	this.instance_2 = new lib.brgt_n_60("synched",0);
	this.instance_2.setTransform(129.8,122,1,1,0,0,0,8,19.8);
	this.instance_2._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_2).wait(1).to({_off:false},0).wait(58).to({startPosition:0},0).to({x:147.95,y:120},6).to({y:122},5).wait(34).to({startPosition:0},0).to({x:129.8,y:125.5},6).to({y:122},5).wait(11));

	// 샘머리
	this.instance_3 = new lib.샘머리_n_54_n_14("synched",0);
	this.instance_3.setTransform(100.4,171.25,1,1,0,0,0,88.7,171.5);
	this.instance_3._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_3).wait(1).to({_off:false},0).wait(58).to({startPosition:0},0).to({regX:88.8,regY:171.4,rotation:9.3844,x:108.85,y:167.15},6).to({y:170.15},5).wait(34).to({startPosition:0},0).to({regX:88.7,regY:171.5,rotation:0,x:100.4,y:174.25},6).to({y:171.25},5).wait(11));

	// Symbol_135_copy
	this.instance_4 = new lib.Symbol135copy_n_59_n_19("single",0);
	this.instance_4.setTransform(161.05,190.85,0.995,0.995,0,0,0,32,2.1);
	this.instance_4._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_4).wait(1).to({_off:false},0).wait(58).to({startPosition:0},0).to({regX:32.2,regY:2.3,rotation:-16.4592,x:168.5,y:191.65},6).to({regX:32.1,regY:2.2,rotation:-10.9937,x:168.35,y:194.55},5).wait(34).to({startPosition:0},0).to({rotation:1.4834,x:161.15,y:193.95},6).to({regX:32,regY:2.1,rotation:0,x:161.05,y:190.85},5).wait(11));

	// 샘_후드1
	this.instance_5 = new lib.vgfgf_n_69_n_29("single",0);
	this.instance_5.setTransform(110.3,242.45,1,1,0,0,0,65,97.8);
	this.instance_5._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_5).wait(1).to({_off:false},0).wait(58).to({startPosition:0},0).to({regX:65.1,rotation:3.1962,x:114.85,y:240.15},6).to({y:243.15},5).wait(34).to({startPosition:0},0).to({regX:65,rotation:0,x:110.3,y:245.45},6).to({y:242.45},5).wait(11));

	// 샘_다리
	this.instance_6 = new lib.샘_다리_n_72_n_32("single",0);
	this.instance_6.setTransform(92.9,306.25,1,1,0,0,0,93.2,8.4);
	this.instance_6._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_6).wait(1).to({_off:false},0).wait(58).to({startPosition:0},0).to({startPosition:0},6).to({startPosition:0},5).wait(34).to({startPosition:0},0).to({startPosition:0},6).to({startPosition:0},5).wait(11));

	// Symbol_140_copy_2
	this.instance_7 = new lib.Symbol140copy2_n_78_n_38("single",6);
	this.instance_7.setTransform(60.85,229.4,1.0471,1.0471,3.9967,0,0,28.8,44.2);
	this.instance_7._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_7).wait(1).to({_off:false},0).wait(58).to({startPosition:6},0).to({regY:44.1,rotation:115.4649,x:62.25,y:248.85},6).to({rotation:111.2428,x:67,y:252.6},5).wait(34).to({startPosition:6},0).to({regY:44.2,rotation:2.0201,x:62.25,y:232.5},6).to({rotation:3.9967,x:60.85,y:229.4},5).wait(11));

	// Symbol_140_copy_2
	this.instance_8 = new lib.Symbol140copy2_n_78_n_38("single",5);
	this.instance_8.setTransform(65.5,185.35,1.0471,1.0471,3.9967,0,0,30.3,1.9);
	this.instance_8._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_8).wait(1).to({_off:false},0).wait(58).to({startPosition:5},0).to({regX:30.4,regY:2,rotation:21.6315,x:73.6,y:180.55},6).to({regX:30.3,regY:1.9,rotation:17.4081,x:73.2,y:183.6},5).wait(34).to({startPosition:5},0).to({regY:2,rotation:2.0201,x:65.4,y:188.4},6).to({regY:1.9,rotation:3.9967,x:65.5,y:185.35},5).wait(11));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-73.8,-10.6,319.5,506.5);


(lib.Symbol85_n_42_3_n_56 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// 샘_눈
	this.instance = new lib.샘_눈_n_43_n_3("single",3);
	this.instance.setTransform(81.5,110.05,1,1,0,0,0,32.5,29.1);
	this.instance._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1).to({_off:false},0).to({startPosition:3},58).to({regX:32.6,rotation:9.3844,x:100.1,y:103.65},6).to({y:106.65},5).to({startPosition:3},34).to({regX:32.5,rotation:0,x:81.5,y:113.05},6).to({y:111.25},3).to({_off:true},1).wait(12));

	// 샘_입_copy_2
	this.instance_1 = new lib.샘입_우울_n_52("single",0);
	this.instance_1.setTransform(85.4,146.3,1.1229,1.1229,1.467,0,0,21.6,6);
	this.instance_1._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_1).wait(1).to({_off:false},0).wait(58).to({mode:"synched",startPosition:4},0).to({rotation:10.8515,x:98,y:140,startPosition:10},6).to({y:143},5).wait(28).to({mode:"single",startPosition:13},0).wait(6).to({startPosition:11},0).to({rotation:1.467,x:85.4,y:149.3},6).to({rotation:1.4663,x:85.35,y:147.55},3).to({_off:true},1).wait(12));

	// 샘머리
	this.instance_2 = new lib.샘머리_n_54_n_14("synched",0);
	this.instance_2.setTransform(100.4,171.25,1,1,0,0,0,88.7,171.5);
	this.instance_2._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_2).wait(1).to({_off:false},0).wait(58).to({startPosition:0},0).to({regX:88.8,regY:171.4,rotation:9.3844,x:108.85,y:167.15},6).to({y:170.15},5).wait(34).to({startPosition:0},0).to({regX:88.7,regY:171.5,rotation:0,x:100.4,y:174.25},6).to({y:172.45},3).to({_off:true},1).wait(12));

	// Symbol_135_copy
	this.instance_3 = new lib.Symbol135copy_n_59_n_19("single",0);
	this.instance_3.setTransform(161.05,190.85,0.995,0.995,0,0,0,32,2.1);
	this.instance_3._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_3).wait(1).to({_off:false},0).wait(58).to({startPosition:0},0).to({regX:32.2,regY:2.3,rotation:-16.4592,x:168.5,y:191.65},6).to({regX:32.1,regY:2.2,rotation:-10.9937,x:168.35,y:194.55},5).wait(34).to({startPosition:0},0).to({rotation:1.4834,x:161.15,y:193.95},6).to({regX:32.2,rotation:0.5931,x:161.2,y:192.2},3).to({_off:true},1).wait(12));

	// 샘_후드1
	this.instance_4 = new lib.vgfgf_n_69_n_29("single",0);
	this.instance_4.setTransform(110.3,242.45,1,1,0,0,0,65,97.8);
	this.instance_4._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_4).wait(1).to({_off:false},0).wait(58).to({startPosition:0},0).to({regX:65.1,rotation:3.1962,x:114.85,y:240.15},6).to({y:243.15},5).wait(34).to({startPosition:0},0).to({regX:65,rotation:0,x:110.3,y:245.45},6).to({y:243.65},3).to({_off:true},1).wait(12));

	// 샘_다리
	this.instance_5 = new lib.샘_다리_n_72_n_32("single",0);
	this.instance_5.setTransform(92.9,306.25,1,1,0,0,0,93.2,8.4);
	this.instance_5._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_5).wait(1).to({_off:false},0).wait(58).to({startPosition:0},0).to({startPosition:0},6).to({startPosition:0},5).wait(34).to({startPosition:0},0).to({startPosition:0},6).to({startPosition:0},3).to({_off:true},1).wait(12));

	// Symbol_140_copy_2
	this.instance_6 = new lib.Symbol140copy2_n_78_n_38("single",6);
	this.instance_6.setTransform(60.85,229.4,1.0471,1.0471,3.9967,0,0,28.8,44.2);
	this.instance_6._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_6).wait(1).to({_off:false},0).wait(58).to({startPosition:6},0).to({regY:44.1,rotation:115.4649,x:62.25,y:248.85},6).to({rotation:111.2428,x:67,y:252.6},5).wait(34).to({startPosition:6},0).to({regY:44.2,rotation:2.0201,x:62.25,y:232.5},6).to({regX:28.9,rotation:3.2062,x:61.5,y:230.7},3).to({_off:true},1).wait(12));

	// Symbol_140_copy_2
	this.instance_7 = new lib.Symbol140copy2_n_78_n_38("single",5);
	this.instance_7.setTransform(65.5,185.35,1.0471,1.0471,3.9967,0,0,30.3,1.9);
	this.instance_7._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_7).wait(1).to({_off:false},0).wait(58).to({startPosition:5},0).to({regX:30.4,regY:2,rotation:21.6315,x:73.6,y:180.55},6).to({regX:30.3,regY:1.9,rotation:17.4081,x:73.2,y:183.6},5).wait(34).to({startPosition:5},0).to({regY:2,rotation:2.0201,x:65.4,y:188.4},6).to({regX:30.4,rotation:3.2062,x:65.65,y:186.7},3).to({_off:true},1).wait(12));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-73.8,-10.6,319.5,506.5);


(lib.Symbol85_n_42_2_n_54 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// 샘_눈
	this.instance = new lib.샘_눈_n_43_n_3("single",2);
	this.instance.setTransform(81.5,110.05,1,1,0,0,0,32.5,29.1);
	this.instance._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1).to({_off:false},0).wait(64).to({startPosition:2},0).to({regX:32.6,rotation:9.3844,x:100.1,y:103.65},6).to({y:106.65},5).wait(34).to({startPosition:2},0).to({regX:32.5,rotation:0,x:81.5,y:113.05},6).to({y:110.05},5).wait(2).to({startPosition:2},0).to({_off:true},1).wait(8));

	// 샘_입_copy_2
	this.instance_1 = new lib.샘입_우울_n_52("single",0);
	this.instance_1.setTransform(85.4,146.3,1.1229,1.1229,1.467,0,0,21.6,6);
	this.instance_1._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_1).wait(1).to({_off:false},0).wait(64).to({mode:"synched",startPosition:4},0).to({rotation:10.8515,x:98,y:140,startPosition:10},6).to({y:143},5).wait(28).to({mode:"single",startPosition:13},0).wait(6).to({startPosition:11},0).to({rotation:1.467,x:85.4,y:149.3},6).to({rotation:1.467,y:146.3,startPosition:0},5).wait(2).to({startPosition:0},0).to({_off:true},1).wait(8));

	// 샘머리
	this.instance_2 = new lib.샘머리_n_54_n_14("synched",0);
	this.instance_2.setTransform(100.4,171.25,1,1,0,0,0,88.7,171.5);
	this.instance_2._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_2).wait(1).to({_off:false},0).wait(64).to({startPosition:0},0).to({regX:88.8,regY:171.4,rotation:9.3844,x:108.85,y:167.15},6).to({y:170.15},5).wait(34).to({startPosition:0},0).to({regX:88.7,regY:171.5,rotation:0,x:100.4,y:174.25},6).to({y:171.25},5).wait(2).to({startPosition:0},0).to({_off:true},1).wait(8));

	// Symbol_135_copy
	this.instance_3 = new lib.Symbol135copy_n_59_n_19("single",0);
	this.instance_3.setTransform(161.05,190.85,0.995,0.995,0,0,0,32,2.1);
	this.instance_3._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_3).wait(1).to({_off:false},0).wait(64).to({startPosition:0},0).to({regX:32.2,regY:2.3,rotation:-16.4592,x:168.5,y:191.65},6).to({regX:32.1,regY:2.2,rotation:-10.9937,x:168.35,y:194.55},5).wait(34).to({startPosition:0},0).to({rotation:1.4834,x:161.15,y:193.95},6).to({regX:32,regY:2.1,rotation:0,x:161.05,y:190.85},5).wait(2).to({startPosition:0},0).to({_off:true},1).wait(8));

	// 샘_후드1
	this.instance_4 = new lib.vgfgf_n_69_n_29("single",0);
	this.instance_4.setTransform(110.3,242.45,1,1,0,0,0,65,97.8);
	this.instance_4._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_4).wait(1).to({_off:false},0).wait(64).to({startPosition:0},0).to({regX:65.1,rotation:3.1962,x:114.85,y:240.15},6).to({y:243.15},5).wait(34).to({startPosition:0},0).to({regX:65,rotation:0,x:110.3,y:245.45},6).to({y:242.45},5).wait(2).to({startPosition:0},0).to({_off:true},1).wait(8));

	// 샘_다리
	this.instance_5 = new lib.샘_다리_n_72_n_32("single",0);
	this.instance_5.setTransform(92.9,306.25,1,1,0,0,0,93.2,8.4);
	this.instance_5._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_5).wait(1).to({_off:false},0).wait(64).to({startPosition:0},0).to({startPosition:0},6).to({startPosition:0},5).wait(34).to({startPosition:0},0).to({startPosition:0},6).to({startPosition:0},5).wait(2).to({startPosition:0},0).to({_off:true},1).wait(8));

	// Symbol_140_copy_2
	this.instance_6 = new lib.Symbol140copy2_n_78_n_38("single",6);
	this.instance_6.setTransform(60.85,229.4,1.0471,1.0471,3.9967,0,0,28.8,44.2);
	this.instance_6._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_6).wait(1).to({_off:false},0).wait(64).to({startPosition:6},0).to({regY:44.1,rotation:115.4649,x:62.25,y:248.85},6).to({rotation:111.2428,x:67,y:252.6},5).wait(34).to({startPosition:6},0).to({regY:44.2,rotation:2.0201,x:62.25,y:232.5},6).to({rotation:3.9967,x:60.85,y:229.4},5).wait(2).to({startPosition:6},0).to({_off:true},1).wait(8));

	// Symbol_140_copy_2
	this.instance_7 = new lib.Symbol140copy2_n_78_n_38("single",5);
	this.instance_7.setTransform(65.5,185.35,1.0471,1.0471,3.9967,0,0,30.3,1.9);
	this.instance_7._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_7).wait(1).to({_off:false},0).wait(64).to({startPosition:5},0).to({regX:30.4,regY:2,rotation:21.6315,x:73.6,y:180.55},6).to({regX:30.3,regY:1.9,rotation:17.4081,x:73.2,y:183.6},5).wait(34).to({startPosition:5},0).to({regY:2,rotation:2.0201,x:65.4,y:188.4},6).to({regY:1.9,rotation:3.9967,x:65.5,y:185.35},5).wait(2).to({startPosition:5},0).to({_off:true},1).wait(8));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-73.8,-10.6,319.5,506.5);


(lib.Symbol85_n_42_1_n_51 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// 샘_눈
	this.instance = new lib.샘_눈_n_43_n_3("single",0);
	this.instance.setTransform(81.5,110.05,1,1,0,0,0,32.5,29.1);
	this.instance._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1).to({_off:false},0).wait(58).to({startPosition:0},0).to({regX:32.6,rotation:9.3844,x:100.1,y:103.65},6).to({y:106.65},5).wait(34).to({startPosition:0},0).to({regX:32.5,rotation:0,x:81.5,y:113.05},6).to({y:110.05},5).wait(3).to({startPosition:0},0).to({_off:true},1).wait(7));

	// 샘_입_copy_2
	this.instance_1 = new lib.샘입_우울_n_52("single",0);
	this.instance_1.setTransform(85.4,146.3,1.1229,1.1229,1.467,0,0,21.6,6);
	this.instance_1._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_1).wait(1).to({_off:false},0).wait(58).to({mode:"synched",startPosition:4},0).to({rotation:10.8515,x:98,y:140,startPosition:10},6).to({y:143},5).wait(28).to({mode:"single",startPosition:13},0).wait(6).to({startPosition:11},0).to({rotation:1.467,x:85.4,y:149.3},6).to({rotation:1.467,y:146.3,startPosition:0},5).wait(3).to({startPosition:0},0).to({_off:true},1).wait(7));

	// 샘머리
	this.instance_2 = new lib.샘머리_n_54_n_14("synched",0);
	this.instance_2.setTransform(100.4,171.25,1,1,0,0,0,88.7,171.5);
	this.instance_2._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_2).wait(1).to({_off:false},0).wait(58).to({startPosition:0},0).to({regX:88.8,regY:171.4,rotation:9.3844,x:108.85,y:167.15},6).to({y:170.15},5).wait(34).to({startPosition:0},0).to({regX:88.7,regY:171.5,rotation:0,x:100.4,y:174.25},6).to({y:171.25},5).wait(3).to({startPosition:0},0).to({_off:true},1).wait(7));

	// Symbol_135_copy
	this.instance_3 = new lib.Symbol135copy_n_59_n_19("single",0);
	this.instance_3.setTransform(161.05,190.85,0.995,0.995,0,0,0,32,2.1);
	this.instance_3._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_3).wait(1).to({_off:false},0).wait(58).to({startPosition:0},0).to({regX:32.2,regY:2.3,rotation:-16.4592,x:168.5,y:191.65},6).to({regX:32.1,regY:2.2,rotation:-10.9937,x:168.35,y:194.55},5).wait(34).to({startPosition:0},0).to({rotation:1.4834,x:161.15,y:193.95},6).to({regX:32,regY:2.1,rotation:0,x:161.05,y:190.85},5).wait(3).to({startPosition:0},0).to({_off:true},1).wait(7));

	// 샘_후드1
	this.instance_4 = new lib.vgfgf_n_69_n_29("single",0);
	this.instance_4.setTransform(110.3,242.45,1,1,0,0,0,65,97.8);
	this.instance_4._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_4).wait(1).to({_off:false},0).wait(58).to({startPosition:0},0).to({regX:65.1,rotation:3.1962,x:114.85,y:240.15},6).to({y:243.15},5).wait(34).to({startPosition:0},0).to({regX:65,rotation:0,x:110.3,y:245.45},6).to({y:242.45},5).wait(3).to({startPosition:0},0).to({_off:true},1).wait(7));

	// 샘_다리
	this.instance_5 = new lib.샘_다리_n_72_n_32("single",0);
	this.instance_5.setTransform(92.9,306.25,1,1,0,0,0,93.2,8.4);
	this.instance_5._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_5).wait(1).to({_off:false},0).wait(58).to({startPosition:0},0).to({startPosition:0},6).to({startPosition:0},5).wait(34).to({startPosition:0},0).to({startPosition:0},6).to({startPosition:0},5).wait(3).to({startPosition:0},0).to({_off:true},1).wait(7));

	// Symbol_140_copy_2
	this.instance_6 = new lib.Symbol140copy2_n_78_n_38("single",6);
	this.instance_6.setTransform(60.85,229.4,1.0471,1.0471,3.9967,0,0,28.8,44.2);
	this.instance_6._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_6).wait(1).to({_off:false},0).wait(58).to({startPosition:6},0).to({regY:44.1,rotation:115.4649,x:62.25,y:248.85},6).to({rotation:111.2428,x:67,y:252.6},5).wait(34).to({startPosition:6},0).to({regY:44.2,rotation:2.0201,x:62.25,y:232.5},6).to({rotation:3.9967,x:60.85,y:229.4},5).wait(3).to({startPosition:6},0).to({_off:true},1).wait(7));

	// Symbol_140_copy_2
	this.instance_7 = new lib.Symbol140copy2_n_78_n_38("single",5);
	this.instance_7.setTransform(65.5,185.35,1.0471,1.0471,3.9967,0,0,30.3,1.9);
	this.instance_7._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_7).wait(1).to({_off:false},0).wait(58).to({startPosition:5},0).to({regX:30.4,regY:2,rotation:21.6315,x:73.6,y:180.55},6).to({regX:30.3,regY:1.9,rotation:17.4081,x:73.2,y:183.6},5).wait(34).to({startPosition:5},0).to({regY:2,rotation:2.0201,x:65.4,y:188.4},6).to({regY:1.9,rotation:3.9967,x:65.5,y:185.35},5).wait(3).to({startPosition:5},0).to({_off:true},1).wait(7));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-73.8,-10.6,319.5,506.5);


(lib.표익_따09_2_n_64 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// timeline functions:
	this.frame_4 = function() {
		playSound("_4_2단원_01표현익히기_32mp3copy");
	}

	// actions tween:
	this.timeline.addTween(cjs.Tween.get(this).wait(4).call(this.frame_4).wait(61));

	// Symbol_87_n_0
	this.instance = new lib.Symbol85_n_42_3_n_56("synched",49,false);
	this.instance.setTransform(1135.6,673.65,1.02,1.02,0,0,0,97.2,249);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(65));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(0,0,1286.9,925.5);


(lib.표익_따08_2_n_63 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// timeline functions:
	this.frame_4 = function() {
		playSound("_4_2단원_01표현익히기_62");
	}

	// actions tween:
	this.timeline.addTween(cjs.Tween.get(this).wait(4).call(this.frame_4).wait(54));

	// Symbol_87_n_0
	this.instance = new lib.Symbol85_n_42_n_2("synched",52,false);
	this.instance.setTransform(1135.6,673.65,1.02,1.02,0,0,0,97.2,249);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(58));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(961.2,408.8,325.70000000000005,516.7);


(lib.표익_따07_2_n_61 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// timeline functions:
	this.frame_4 = function() {
		playSound("_4_2단원_01표현익히기_52");
	}

	// actions tween:
	this.timeline.addTween(cjs.Tween.get(this).wait(4).call(this.frame_4).wait(91));

	// Symbol_87_n_0
	this.instance = new lib.Symbol85_n_42_5_n_62("synched",54,false);
	this.instance.setTransform(1135.6,673.65,1.02,1.02,0,0,0,97.2,249);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(95));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(961.2,408.8,325.70000000000005,516.7);


(lib.표익_따06_2_n_58 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// timeline functions:
	this.frame_3 = function() {
		playSound("_4_2단원_01표현익히기_44");
	}

	// actions tween:
	this.timeline.addTween(cjs.Tween.get(this).wait(3).call(this.frame_3).wait(92));

	// Symbol_87_n_0
	this.instance = new lib.Symbol85_n_42_4_n_59("synched",54,false);
	this.instance.setTransform(1135.6,673.65,1.02,1.02,0,0,0,97.2,249);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(95));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(961.2,408.8,325.70000000000005,516.7);


(lib.표익_따05_2_n_57 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// timeline functions:
	this.frame_4 = function() {
		playSound("_4_2단원_01표현익히기_42");
	}

	// actions tween:
	this.timeline.addTween(cjs.Tween.get(this).wait(4).call(this.frame_4).wait(59));

	// Symbol_87_n_0
	this.instance = new lib.Symbol85_n_42_n_2("synched",51,false);
	this.instance.setTransform(1135.6,673.65,1.02,1.02,0,0,0,97.2,249);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(63));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(961.2,408.8,325.70000000000005,516.7);


(lib.표익_따04_2_n_55 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// timeline functions:
	this.frame_3 = function() {
		playSound("_4_2단원_01표현익히기_32mp3copy");
	}

	// actions tween:
	this.timeline.addTween(cjs.Tween.get(this).wait(3).call(this.frame_3).wait(60));

	// Symbol_87_n_0
	this.instance = new lib.Symbol85_n_42_3_n_56("synched",50,false);
	this.instance.setTransform(1135.6,673.65,1.02,1.02,0,0,0,97.2,249);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(63));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(0,0,1286.9,925.5);


(lib.표익_따03_2_n_53 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// timeline functions:
	this.frame_4 = function() {
		playSound("_4_2단원_01표현익히기_32");
	}

	// actions tween:
	this.timeline.addTween(cjs.Tween.get(this).wait(4).call(this.frame_4).wait(63));

	// Symbol_87_n_0
	this.instance = new lib.Symbol85_n_42_2_n_54("synched",57,false);
	this.instance.setTransform(1135.6,673.65,1.02,1.02,0,0,0,97.2,249);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(67));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(961.2,408.8,325.70000000000005,516.7);


(lib.표익_따01_2_n_1 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// timeline functions:
	this.frame_5 = function() {
		playSound("_4_2단원_01표현익히기_12");
	}

	// actions tween:
	this.timeline.addTween(cjs.Tween.get(this).wait(5).call(this.frame_5).wait(63));

	// Symbol_85_n_42
	this.instance = new lib.Symbol85_n_42_n_2("synched",51,false);
	this.instance.setTransform(1135.6,673.65,1.02,1.02,0,0,0,97.2,249);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(68));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(961.2,408.8,325.70000000000005,516.7);


(lib.표익_따09_1_n_116 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// timeline functions:
	this.frame_7 = function() {
		playSound("_4_2단원_01표현익히기_63");
	}

	// actions tween:
	this.timeline.addTween(cjs.Tween.get(this).wait(7).call(this.frame_7).wait(45));

	// Symbol_85_n_42
	this.instance = new lib.Symbol87_n_0_n_67("synched",0,false);
	this.instance.setTransform(250.9,686.1,1.05,1.05,0,0,180,105.4,237.9);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(52));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(0,0,426,935.7);


(lib.표익_따08_1_n_115 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// timeline functions:
	this.frame_7 = function() {
		playSound("_4_2단원_01표현익히기_61");
	}

	// actions tween:
	this.timeline.addTween(cjs.Tween.get(this).wait(7).call(this.frame_7).wait(49));

	// Symbol_85_n_42
	this.instance = new lib.Symbol87_n_0_n_67("synched",0,false);
	this.instance.setTransform(250.9,686.1,1.05,1.05,0,0,180,105.4,237.9);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(56));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(0,0,426,935.7);


(lib.표익_따07_1_n_114 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// timeline functions:
	this.frame_7 = function() {
		playSound("_4_2단원_01표현익히기_51");
	}

	// actions tween:
	this.timeline.addTween(cjs.Tween.get(this).wait(7).call(this.frame_7).wait(48));

	// Symbol_85_n_42
	this.instance = new lib.Symbol87_n_0_n_67("synched",0,false);
	this.instance.setTransform(250.9,686.1,1.05,1.05,0,0,180,105.4,237.9);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(55));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(0,0,426,935.7);


(lib.표익_따06_1_n_113 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// timeline functions:
	this.frame_7 = function() {
		playSound("_4_2단원_01표현익히기_43");
	}

	// actions tween:
	this.timeline.addTween(cjs.Tween.get(this).wait(7).call(this.frame_7).wait(48));

	// Symbol_85_n_42
	this.instance = new lib.Symbol87_n_0_n_67("synched",0,false);
	this.instance.setTransform(250.9,686.1,1.05,1.05,0,0,180,105.4,237.9);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(55));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(0,0,426,935.7);


(lib.표익_따05_1_n_112 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// timeline functions:
	this.frame_7 = function() {
		playSound("_4_2단원_01표현익히기_31mp3copy");
	}

	// actions tween:
	this.timeline.addTween(cjs.Tween.get(this).wait(7).call(this.frame_7).wait(48));

	// Symbol_85_n_42
	this.instance = new lib.Symbol87_n_0_n_67("synched",0,false);
	this.instance.setTransform(250.9,686.1,1.05,1.05,0,0,180,105.4,237.9);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(55));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(0,0,426,935.7);


(lib.표익_따04_1_n_111 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// timeline functions:
	this.frame_7 = function() {
		playSound("_4_2단원_01표현익히기_31mp3copy");
	}

	// actions tween:
	this.timeline.addTween(cjs.Tween.get(this).wait(7).call(this.frame_7).wait(46));

	// Symbol_85_n_42
	this.instance = new lib.Symbol87_n_0_n_67("synched",0,false);
	this.instance.setTransform(250.9,686.1,1.05,1.05,0,0,180,105.4,237.9);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(53));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(0,0,426,935.7);


(lib.표익_따03_1_n_110 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// timeline functions:
	this.frame_7 = function() {
		playSound("_4_2단원_01표현익히기_23mp3copy");
	}

	// actions tween:
	this.timeline.addTween(cjs.Tween.get(this).wait(7).call(this.frame_7).wait(48));

	// Symbol_85_n_42
	this.instance = new lib.Symbol87_n_0_n_67("synched",0,false);
	this.instance.setTransform(250.9,686.1,1.05,1.05,0,0,180,105.4,237.9);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(55));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(0,0,426,935.7);


(lib.표익_따02_2_n_50 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// timeline functions:
	this.frame_6 = function() {
		playSound("_4_2단원_01표현익히기_22");
	}

	// actions tween:
	this.timeline.addTween(cjs.Tween.get(this).wait(6).call(this.frame_6).wait(62));

	// Symbol_87_n_0
	this.instance = new lib.Symbol85_n_42_1_n_51("synched",51,false);
	this.instance.setTransform(1135.6,673.65,1.02,1.02,0,0,0,97.2,249);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(68));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(961.2,408.8,325.70000000000005,516.7);


(lib.표익_따02_1_n_109 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// timeline functions:
	this.frame_7 = function() {
		playSound("_4_2단원_01표현익히기_21");
	}

	// actions tween:
	this.timeline.addTween(cjs.Tween.get(this).wait(7).call(this.frame_7).wait(43));

	// Symbol_85_n_42
	this.instance = new lib.Symbol87_n_0_n_67("synched",0,false);
	this.instance.setTransform(250.9,686.1,1.05,1.05,0,0,180,105.4,237.9);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(50));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(124.8,430.4,301.2,505.30000000000007);


(lib.표익_따01_1_n_66 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// timeline functions:
	this.frame_7 = function() {
		playSound("_4_2단원_01표현익히기_11");
	}

	// actions tween:
	this.timeline.addTween(cjs.Tween.get(this).wait(7).call(this.frame_7).wait(45));

	// Symbol_87_n_0
	this.instance = new lib.Symbol87_n_0_n_67("synched",0,false);
	this.instance.setTransform(250.9,686.1,1.05,1.05,0,0,180,105.4,237.9);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(52));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(0,0,426,935.7);


(lib.표익_따따_n_65 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {idle:0,item_0:7,item_1:14,item_2:21,item_3:28,item_4:35,item_5:42,item_6:49,item_7:57,item_8:64};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.표익_따01_1_n_66("synched",0,false);
	this.instance.setTransform(27.5,27.5,1,1,0,0,0,27.5,27.5);

	this.instance_1 = new lib.표익_따02_1_n_109();
	this.instance_1.setTransform(27.5,27.5,1,1,0,0,0,27.5,27.5);

	this.instance_2 = new lib.표익_따03_1_n_110();
	this.instance_2.setTransform(27.5,27.5,1,1,0,0,0,27.5,27.5);

	this.instance_3 = new lib.표익_따04_1_n_111();
	this.instance_3.setTransform(27.5,27.5,1,1,0,0,0,27.5,27.5);

	this.instance_4 = new lib.표익_따05_1_n_112();
	this.instance_4.setTransform(27.5,27.5,1,1,0,0,0,27.5,27.5);

	this.instance_5 = new lib.표익_따06_1_n_113();
	this.instance_5.setTransform(27.5,27.5,1,1,0,0,0,27.5,27.5);

	this.instance_6 = new lib.표익_따07_1_n_114();
	this.instance_6.setTransform(27.5,27.5,1,1,0,0,0,27.5,27.5);

	this.instance_7 = new lib.표익_따08_1_n_115();
	this.instance_7.setTransform(27.5,27.5,1,1,0,0,0,27.5,27.5);

	this.instance_8 = new lib.표익_따09_1_n_116();
	this.instance_8.setTransform(27.5,27.5,1,1,0,0,0,27.5,27.5);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.instance,p:{mode:"synched",startPosition:0,loop:false}}]}).to({state:[{t:this.instance,p:{mode:"independent",startPosition:undefined,loop:undefined}}]},7).to({state:[{t:this.instance_1}]},7).to({state:[{t:this.instance_2}]},7).to({state:[{t:this.instance_3}]},7).to({state:[{t:this.instance_4}]},7).to({state:[{t:this.instance_5}]},7).to({state:[{t:this.instance_6}]},7).to({state:[{t:this.instance_7}]},8).to({state:[{t:this.instance_8}]},7).wait(7));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(141.6,435.5,218.4,500.20000000000005);


(lib.표익_따따2_n_0 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {"idle":0,"item_0":7,"item_1":14,"item_2":21,"item_3":28,"item_4":35,"item_5":42,"item_6":49,"item_7":57,"item_8":64};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.표익_따01_2_n_1("single",0);
	this.instance.setTransform(27.5,27.5,1,1,0,0,0,27.5,27.5);

	this.instance_1 = new lib.표익_따02_2_n_50();
	this.instance_1.setTransform(27.5,27.5,1,1,0,0,0,27.5,27.5);

	this.instance_2 = new lib.표익_따03_2_n_53();
	this.instance_2.setTransform(27.5,27.5,1,1,0,0,0,27.5,27.5);

	this.instance_3 = new lib.표익_따04_2_n_55();
	this.instance_3.setTransform(27.5,27.5,1,1,0,0,0,27.5,27.5);

	this.instance_4 = new lib.표익_따05_2_n_57();
	this.instance_4.setTransform(27.5,27.5,1,1,0,0,0,27.5,27.5);

	this.instance_5 = new lib.표익_따06_2_n_58();
	this.instance_5.setTransform(27.5,27.5,1,1,0,0,0,27.5,27.5);

	this.instance_6 = new lib.표익_따07_2_n_61();
	this.instance_6.setTransform(27.5,27.5,1,1,0,0,0,27.5,27.5);

	this.instance_7 = new lib.표익_따08_2_n_63();
	this.instance_7.setTransform(27.5,27.5,1,1,0,0,0,27.5,27.5);

	this.instance_8 = new lib.표익_따09_2_n_64();
	this.instance_8.setTransform(27.5,27.5,1,1,0,0,0,27.5,27.5);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.instance,p:{mode:"single",startPosition:0}}]}).to({state:[{t:this.instance,p:{mode:"independent",startPosition:undefined}}]},7).to({state:[{t:this.instance_1}]},7).to({state:[{t:this.instance_2}]},7).to({state:[{t:this.instance_3}]},7).to({state:[{t:this.instance_4}]},7).to({state:[{t:this.instance_5}]},7).to({state:[{t:this.instance_6}]},7).to({state:[{t:this.instance_7}]},8).to({state:[{t:this.instance_8}]},7).wait(7));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(1036.5,425.1,197.9000000000001,500.4);


// stage content:
(lib.animation = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_2
	this.mc_char_0 = new lib.표익_따따_n_65();
	this.mc_char_0.name = "mc_char_0";
	this.mc_char_0.setTransform(0,0,0.92,0.92);

	this.timeline.addTween(cjs.Tween.get(this.mc_char_0).wait(1));

	// Layer_1
	this.mc_char_1 = new lib.표익_따따2_n_0();
	this.mc_char_1.name = "mc_char_1";
	this.mc_char_1.setTransform(0,0,0.92,0.92);

	this.timeline.addTween(cjs.Tween.get(this.mc_char_1).wait(1));

	this._renderFirstFrame();

}).prototype = p = new lib.AnMovieClip();
p.nominalBounds = new cjs.Rectangle(770.3,751.1,365.29999999999995,109.69999999999993);
// library properties:
lib.properties = {
	id: '554F53FD8DB39B44B0F09FA5FC0170F2',
	width: 1280,
	height: 720,
	fps: 30,
	color: "#999999",
	opacity: 1.00,
	manifest: [
		{src:"images/animation_atlas_1.png", id:"animation_atlas_1"},
		{src:"images/animation_atlas_2.png", id:"animation_atlas_2"},
		{src:"sounds/_4_2단원_01표현익히기_11.mp3", id:"_4_2단원_01표현익히기_11"},
		{src:"sounds/_4_2단원_01표현익히기_12.mp3", id:"_4_2단원_01표현익히기_12"},
		{src:"sounds/_4_2단원_01표현익히기_21.mp3", id:"_4_2단원_01표현익히기_21"},
		{src:"sounds/_4_2단원_01표현익히기_22.mp3", id:"_4_2단원_01표현익히기_22"},
		{src:"sounds/_4_2단원_01표현익히기_23mp3copy.mp3", id:"_4_2단원_01표현익히기_23mp3copy"},
		{src:"sounds/_4_2단원_01표현익히기_31mp3copy.mp3", id:"_4_2단원_01표현익히기_31mp3copy"},
		{src:"sounds/_4_2단원_01표현익히기_32.mp3", id:"_4_2단원_01표현익히기_32"},
		{src:"sounds/_4_2단원_01표현익히기_32mp3copy.mp3", id:"_4_2단원_01표현익히기_32mp3copy"},
		{src:"sounds/_4_2단원_01표현익히기_42.mp3", id:"_4_2단원_01표현익히기_42"},
		{src:"sounds/_4_2단원_01표현익히기_43.mp3", id:"_4_2단원_01표현익히기_43"},
		{src:"sounds/_4_2단원_01표현익히기_44.mp3", id:"_4_2단원_01표현익히기_44"},
		{src:"sounds/_4_2단원_01표현익히기_51.mp3", id:"_4_2단원_01표현익히기_51"},
		{src:"sounds/_4_2단원_01표현익히기_52.mp3", id:"_4_2단원_01표현익히기_52"},
		{src:"sounds/_4_2단원_01표현익히기_61.mp3", id:"_4_2단원_01표현익히기_61"},
		{src:"sounds/_4_2단원_01표현익히기_62.mp3", id:"_4_2단원_01표현익히기_62"},
		{src:"sounds/_4_2단원_01표현익히기_63.mp3", id:"_4_2단원_01표현익히기_63"}
	],
	preloads: []
};



// bootstrap callback support:

(lib.Stage = function(canvas) {
	createjs.Stage.call(this, canvas);
}).prototype = p = new createjs.Stage();

p.setAutoPlay = function(autoPlay) {
	this.tickEnabled = autoPlay;
}
p.play = function() { this.tickEnabled = true; this.getChildAt(0).gotoAndPlay(this.getTimelinePosition()) }
p.stop = function(ms) { if(ms) this.seek(ms); this.tickEnabled = false; }
p.seek = function(ms) { this.tickEnabled = true; this.getChildAt(0).gotoAndStop(lib.properties.fps * ms / 1000); }
p.getDuration = function() { return this.getChildAt(0).totalFrames / lib.properties.fps * 1000; }

p.getTimelinePosition = function() { return this.getChildAt(0).currentFrame / lib.properties.fps * 1000; }

an.bootcompsLoaded = an.bootcompsLoaded || [];
if(!an.bootstrapListeners) {
	an.bootstrapListeners=[];
}

an.bootstrapCallback=function(fnCallback) {
	an.bootstrapListeners.push(fnCallback);
	if(an.bootcompsLoaded.length > 0) {
		for(var i=0; i<an.bootcompsLoaded.length; ++i) {
			fnCallback(an.bootcompsLoaded[i]);
		}
	}
};

an.compositions = an.compositions || {};
an.compositions['554F53FD8DB39B44B0F09FA5FC0170F2'] = {
	getStage: function() { return exportRoot.stage; },
	getLibrary: function() { return lib; },
	getSpriteSheet: function() { return ss; },
	getImages: function() { return img; }
};

an.compositionLoaded = function(id) {
	an.bootcompsLoaded.push(id);
	for(var j=0; j<an.bootstrapListeners.length; j++) {
		an.bootstrapListeners[j](id);
	}
}

an.getComposition = function(id) {
	return an.compositions[id];
}


an.makeResponsive = function(isResp, respDim, isScale, scaleType, domContainers) {		
	var lastW, lastH, lastS=1;		
	window.addEventListener('resize', resizeCanvas);		
	resizeCanvas();		
	function resizeCanvas() {			
		var w = lib.properties.width, h = lib.properties.height;			
		var iw = window.innerWidth, ih=window.innerHeight;			
		var pRatio = window.devicePixelRatio || 1, xRatio=iw/w, yRatio=ih/h, sRatio=1;			
		if(isResp) {                
			if((respDim=='width'&&lastW==iw) || (respDim=='height'&&lastH==ih)) {                    
				sRatio = lastS;                
			}				
			else if(!isScale) {					
				if(iw<w || ih<h)						
					sRatio = Math.min(xRatio, yRatio);				
			}				
			else if(scaleType==1) {					
				sRatio = Math.min(xRatio, yRatio);				
			}				
			else if(scaleType==2) {					
				sRatio = Math.max(xRatio, yRatio);				
			}			
		}
		domContainers[0].width = w * pRatio * sRatio;			
		domContainers[0].height = h * pRatio * sRatio;
		domContainers.forEach(function(container) {				
			container.style.width = w * sRatio + 'px';				
			container.style.height = h * sRatio + 'px';			
		});
		stage.scaleX = pRatio*sRatio;			
		stage.scaleY = pRatio*sRatio;
		lastW = iw; lastH = ih; lastS = sRatio;            
		stage.tickOnUpdate = false;            
		stage.update();            
		stage.tickOnUpdate = true;		
	}
}
an.handleSoundStreamOnTick = function(event) {
	if(!event.paused){
		var stageChild = stage.getChildAt(0);
		if(!stageChild.paused || stageChild.ignorePause){
			stageChild.syncStreamSounds();
		}
	}
}
an.handleFilterCache = function(event) {
	if(!event.paused){
		var target = event.target;
		if(target){
			if(target.filterCacheList){
				for(var index = 0; index < target.filterCacheList.length ; index++){
					var cacheInst = target.filterCacheList[index];
					if((cacheInst.startFrame <= target.currentFrame) && (target.currentFrame <= cacheInst.endFrame)){
						cacheInst.instance.cache(cacheInst.x, cacheInst.y, cacheInst.w, cacheInst.h);
					}
				}
			}
		}
	}
}


})(createjs = createjs||{}, AdobeAn = AdobeAn||{});
var createjs, AdobeAn;