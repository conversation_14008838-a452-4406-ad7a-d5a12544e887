
<svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" preserveAspectRatio="none" x="0px" y="0px" width="284px" height="90px" viewBox="0 0 284 90">
<defs>
<linearGradient id="Gradient_1" gradientUnits="userSpaceOnUse" x1="991.35" y1="67.45" x2="991.35" y2="95.05" spreadMethod="pad">
<stop  offset="5.098039215686274%" stop-color="#E7EFFB"/>

<stop  offset="100%" stop-color="#CBD3E0"/>
</linearGradient>

<filter id="Filter_1" x="-20%" y="-20%" width="140%" height="140%" color-interpolation-filters="sRGB">
<feGaussianBlur in="SourceGraphic" stdDeviation="1.3333333333333333,1.3333333333333333" result="result1"/>
</filter>

<g id="Symbol_63_0_Layer0_0_FILL">
<path fill="#000000" fill-opacity="0.09803921568627451" stroke="none" d="
M 1077.75 59.4
Q 1073.35 55 1067.15 55
L 957 55
Q 950.8 55 946.4 59.4 942 63.8 942 70
L 942 80
Q 942 86.2 946.4 90.6 950.8 95 957 95
L 1067.15 95
Q 1073.35 95 1077.75 90.6 1082.15 86.2 1082.15 80
L 1082.15 70
Q 1082.15 63.8 1077.75 59.4 Z"/>
</g>

<g id="Symbol_96_copy_4_0_Layer0_1_FILL">
<path fill="url(#Gradient_1)" stroke="none" d="
M 1077.75 59.4
Q 1073.35 55 1067.15 55
L 957 55
Q 950.8 55 946.4 59.4 942 63.8 942 70
L 942 80
Q 942 86.2 946.4 90.6 950.8 95 957 95
L 1067.15 95
Q 1073.35 95 1077.75 90.6 1082.15 86.2 1082.15 80
L 1082.15 70
Q 1082.15 63.8 1077.75 59.4 Z"/>
</g>

<g id="Symbol_96_copy_4_0_Layer0_2_FILL">
<path fill="#000000" fill-opacity="0.047058823529411764" stroke="none" d="
M 140 25
L 140 22.8
Q 139.888671875 28.811328125 135.6 33.1 131.2 37.5 125 37.5
L 15 37.5
Q 8.8 37.5 4.4 33.1 0.111328125 28.811328125 0 22.8
L 0 25
Q 0 31.2 4.4 35.6 8.8 40 15 40
L 125 40
Q 131.2 40 135.6 35.6 140 31.2 140 25 Z"/>
</g>
</defs>

<g transform="matrix( 2, 0, 0, 2, 2,2.5) ">
<g filter="url(#Filter_1)" transform="matrix( 0.5, 0, 0, 0.5, -1,-1.25) ">
<g transform="matrix( 2, 0, 0, 2, 2,7.5) ">
<g transform="matrix( 0.99884033203125, 0, 0, 1, -940.9,-55) ">
<use xlink:href="#Symbol_63_0_Layer0_0_FILL"/>
</g>
</g>
</g>

<g transform="matrix( 0.99884033203125, 0, 0, 1, -940.9,-55) ">
<use xlink:href="#Symbol_96_copy_4_0_Layer0_1_FILL"/>
</g>

<g transform="matrix( 1, 0, 0, 1, 0,0) ">
<use xlink:href="#Symbol_96_copy_4_0_Layer0_2_FILL"/>
</g>
</g>
</svg>
