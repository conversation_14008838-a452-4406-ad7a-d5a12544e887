
<svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" preserveAspectRatio="none" x="0px" y="0px" width="1280px" height="107px" viewBox="0 0 1280 107">
<defs>
<linearGradient id="Gradient_1" gradientUnits="userSpaceOnUse" x1="1149.8" y1="956.7875" x2="1149.8" y2="854.7125" spreadMethod="pad">
<stop  offset="0%" stop-color="#FFFFFF" stop-opacity="0"/>

<stop  offset="44.31372549019608%" stop-color="#FFFFFF" stop-opacity="0.6980392156862745"/>

<stop  offset="100%" stop-color="#FFFFFF"/>
</linearGradient>

<filter id="Filter_1" x="-20%" y="-100%" width="140%" height="300%" color-interpolation-filters="sRGB">
<feGaussianBlur in="SourceGraphic" stdDeviation="2.6666666666666665,2.6666666666666665" result="result1"/>
</filter>

<g id="Symbol_1_0_Layer0_0_FILL">
<path fill="url(#Gradient_1)" stroke="none" d="
M 1797.15 971.2
L 1797.15 866.2 517.15 866.2 517.15 971.2 1797.15 971.2 Z"/>
</g>

<g id="Symbol_1_0_Layer0_1_FILL">
<path fill="#FFFFFF" stroke="none" d="
M 1797.15 868.2
L 1797.15 866.2 517.15 866.2 517.15 868.2 1797.15 868.2 Z"/>
</g>

<g id="Symbol_189_n_592_0_Layer0_0_FILL">
<path fill="#000000" fill-opacity="0.09803921568627451" stroke="none" d="
M 1797.15 868.2
L 1797.15 866.2 517.15 866.2 517.15 868.2 1797.15 868.2 Z"/>
</g>
</defs>

<g transform="matrix( 1, 0, 0, 1, 0,0) ">
<g transform="matrix( 1, 0, 0, 1, -517.15,-864.15) ">
<use xlink:href="#Symbol_1_0_Layer0_0_FILL"/>
</g>

<g transform="matrix( 1, 0, 0, 1, -517.15,-864.15) ">
<use xlink:href="#Symbol_1_0_Layer0_1_FILL"/>
</g>

<g filter="url(#Filter_1)" transform="matrix( 1, 0, 0, 1, 0,0) ">
<g transform="matrix( 1, 0, 0, 1, 0,0) ">
<g transform="matrix( 1, 0, 0, 1, -517.15,-866.2) ">
<use xlink:href="#Symbol_189_n_592_0_Layer0_0_FILL"/>
</g>
</g>
</g>
</g>
</svg>
