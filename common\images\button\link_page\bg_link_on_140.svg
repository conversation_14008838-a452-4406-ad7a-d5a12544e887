
<svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" preserveAspectRatio="none" x="0px" y="0px" width="284px" height="90px" viewBox="0 0 284 90">
<defs>
<linearGradient id="Gradient_1" gradientUnits="userSpaceOnUse" x1="988" y1="66.19999999999999" x2="988" y2="89.9" spreadMethod="pad">
<stop  offset="13.72549019607843%" stop-color="#745DE3"/>

<stop  offset="100%" stop-color="#5B45C8"/>
</linearGradient>

<filter id="Filter_1" x="-20%" y="-20%" width="140%" height="140%" color-interpolation-filters="sRGB">
<feGaussianBlur in="SourceGraphic" stdDeviation="1.3333333333333333,1.3333333333333333" result="result1"/>
</filter>

<g id="Symbol_59_0_Layer0_0_FILL">
<path fill="#000000" fill-opacity="0.2980392156862745" stroke="none" d="
M 1077.75 59.4
Q 1073.35 55 1067.15 55
L 957 55
Q 950.8 55 946.4 59.4 942 63.8 942 70
L 942 80
Q 942 86.2 946.4 90.6 950.8 95 957 95
L 1067.15 95
Q 1073.35 95 1077.75 90.6 1082.15 86.2 1082.15 80
L 1082.15 70
Q 1082.15 63.8 1077.75 59.4 Z"/>
</g>

<g id="Symbol_96_copy_4_1_Layer0_1_FILL">
<path fill="url(#Gradient_1)" stroke="none" d="
M 1077.7 59.4
Q 1073.3 55 1067.1 55
L 957 55
Q 950.8 55 946.4 59.4 942 63.8 942 70
L 942 80
Q 942 86.2 946.4 90.6 950.8 95 957 95
L 1067.1 95
Q 1073.3 95 1077.7 90.6 1082.1 86.2 1082.1 80
L 1082.1 70
Q 1082.1 63.8 1077.7 59.4 Z"/>
</g>

<g id="Symbol_96_copy_4_1_Layer0_2_FILL">
<path fill="#FFFFFF" fill-opacity="0.09803921568627451" stroke="none" d="
M 1168.2 60.3
Q 1167.7 59.8 1167 59.8
L 1055.4 59.8
Q 1054.7 59.8 1054.2 60.3 1053.7 60.8 1053.7 61.5
L 1053.7 61.55
Q 1053.7 62.25 1054.2 62.75 1054.7 63.25 1055.4 63.25
L 1167 63.25
Q 1167.7 63.25 1168.2 62.75 1168.7 62.25 1168.7 61.55
L 1168.7 61.5
Q 1168.7 60.8 1168.2 60.3 Z"/>
</g>
</defs>

<g transform="matrix( 2, 0, 0, 2, 2,2.5) ">
<g filter="url(#Filter_1)" transform="matrix( 0.5, 0, 0, 0.5, -1,-1.25) ">
<g transform="matrix( 2, 0, 0, 2, 2,6.6) ">
<g transform="matrix( 0.99884033203125, 0, 0, 0.9999847412109375, -940.9,-55) ">
<use xlink:href="#Symbol_59_0_Layer0_0_FILL"/>
</g>
</g>
</g>

<g transform="matrix( 0.99896240234375, 0, 0, 1, -941,-55) ">
<use xlink:href="#Symbol_96_copy_4_1_Layer0_1_FILL"/>
</g>

<g transform="matrix( 1, 0, 0, 1, -1041.2,-57.5) ">
<use xlink:href="#Symbol_96_copy_4_1_Layer0_2_FILL"/>
</g>
</g>
</svg>
