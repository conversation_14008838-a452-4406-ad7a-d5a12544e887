function setLocalStorage(t,e){let a=window.localStorage.getItem(t);if(a){a=JSON.parse(a);const i=a=>{let i=!1;switch(t){case"QUIZ_INPUT":case"QUIZ_CORRECT":case"STUDY_ACT":case"ETC":i=a.id===e.id&&a.type===e.type;break;case"LAST_PAGE":i=a.type===e.type;break;default:i=a.id===e.id}return i};Array.isArray(a)||(a=[a]),a=a.filter((t=>i(t))).length?a.map((t=>i(t)?{...t,...e}:{...t})):[...a,e]}else a=[e];console.log("set activity data :: ",a),window.localStorage.setItem(t,JSON.stringify(a)),setDataHistory()}function getLocalStorage(t,e){let a=window.localStorage.getItem(t);if(console.log("get activity data :: ",a),a){return a=Array.isArray(JSON.parse(a))?JSON.parse(a):[JSON.parse(a)],a?.find((t=>t.id===e.id&&t.type===e.type))??a}return{}}function getGlobalLMSData(t,e){const a=getLocalStorage(t,initActivityFunc("getGlobalLMSData",t,e));window[e](a)}function setActivityData(t,e,a){console.log(`setActivityData call!! mainKey : ${t}, subKey : ${e}, saveData : ${a}`);setLocalStorage(t,initActivityFunc("setActivityData",t,e,a))}function getActivityData(t,e,a){const i=getLocalStorage(t,initActivityFunc("getActivityData",t,e,a));window[a](i)}function callContentsTool(t,e){let a=null,i=null,n={};try{n=JSON.parse(e)}catch(t){n={}}"string"==typeof n.callback?(a=n.callback,i=initActivityFunc("callContentsTool",t,e,a),window[a](getContentsToolReturnData(t,e))):(i=initActivityFunc("callContentsTool",t,e),alert(`callContentsTool mainKey : ${t} call!`)),console.log("callContentsTool",i)}function getContentsToolReturnData(t,e){let a={};const i=JSON.parse(e);switch(t){case"OCR":var n="thank you";"math"==i.type&&(n="sqrt{54}"),a={id:i.id,type:i.type,resultText:n,imgPath:"입력한 ocr 이미지 경로"};break;case"STT":a={id:i.id,resultText:"thank you"};break;case"EXPRESS":a={id:i.id,latex:"sqrt{54}"};break;case"CORRECT":a={id:i.id,correct:"첨삭내용"}}return a}function initActivityFunc(t,...arguments){setHistory(t,...arguments);let e=arguments.map((t=>{if("string"==typeof t)return parseJSON(t);{let e=Array.isArray(t)?"array":typeof t;setErrorHistory(`Arguments '${JSON.stringify(t)||String(t)}' is ${e} type : change to 'String' type`)}}));if(t.includes("getGlobalLMSData")){let[t,e]=arguments.flat();return isValidCallbackFn(e)}return t.includes("set")?isValidActivityParams("set",e):t.includes("get")?isValidActivityParams("get",e):isValidActivityParams("",e)}function parseJSON(t){try{return JSON.parse(t)}catch(e){return t}}function getFormatDefault(t,e){const a={QUIZ_INPUT:{id:"",type:""},QUIZ_CORRECT:{id:"",type:""},STUDY_ACT:{id:"",type:""},LAST_PAGE:{type:""},ETC:{id:"",type:""},VIDEO:{id:"",event:""},OCR:{id:"",type:"",imgPath:"",callback:""},STT:{id:"",text:"",callback:""},TOOL:{},CLASSBOARD:{},VOICE_TEST:{id:"",text:"",key:"",aiModelId:""},EXPRESS:{id:"",key:"",latex:"",callback:""},CORRECT:{id:"",question:"",answer:"",callback:""}},i={QUIZ_INPUT:{answer:"",input:"",solveTime:""},QUIZ_CORRECT:{id:"",type:""},STUDY_ACT:{data:""},LAST_PAGE:{data:""},ETC:{data:""},VIDEO:{data:""}},n={QUIZ_INPUT:{callback:""},QUIZ_CORRECT:{callback:""},STUDY_ACT:{callback:""},LAST_PAGE:{callback:""},ETC:{callback:""},VIDEO:{callback:""}};return t.includes("set")?Object.assign(a[e],i[e]):t.includes("get")?Object.assign(a[e],n[e]):a[e]}function isValidActivityParams(t="",arguments){const[e,a,i={}]=arguments;e&&a&&i||setErrorHistory("Arguments is invalid : Enter all the keys");const n=getFormatDefault(t,e);let o,r;switch(e){case"LAST_PAGE":o={type:a},r={data:i};break;case"STUDY_ACT":case"ETC":case"VIDEO":o=a,r={data:i};break;default:o=a,r=i}return"get"===t&&(r={callback:i}),obj=Object.assign(o,r),Object.keys(n).forEach((e=>{if(!obj.hasOwnProperty(e)){setErrorHistory(`Key '${e}' is not undefined : enter '${e}' key`)}if("set"===t&&!(r instanceof Object)){setErrorHistory(`Arguments '${r}' is invalid type : change to 'Object' type`)}"callback"===e&&isValidCallbackFn(obj.callback)})),obj}function isValidCallbackFn(t,arguments=null){if("string"!=typeof t){return setErrorHistory('The name of the callback function should be "String"',t),!1}if("function"!=typeof window[t]){return setErrorHistory("The type of the callback function is invalid",t),!1}return!0}function setHistory(t,...arguments){if(setDataHistory(),"DEV"!==window.MODE)return;let e=document.querySelector("#LWLog");if(!e){const t=document.createElement("style");t.setAttribute("id","lwStyle"),t.innerHTML="\n      .lw-log { position: absolute; top: 100vh; background: #fff; border: 1px solid #000; width: 50%; padding: 10px; overflow: auto}\n      .lw-log h1{ font-size: 24px; font-weight: bold;}\n      .lw-log *{font-family: Consolas; font-size: 16px; white-space: nowrap;}\n      .lw-log strong{font-weight: bold;}\n    ",document.body.appendChild(t);const a=document.createElement("div");a.setAttribute("id","LWLog"),a.classList.add("lw-log"),a.innerHTML="<h1>Log history</h1>",document.body.appendChild(a),e=document.querySelector("#LWLog")}if(!t)return;let a=arguments.flatMap((t=>"string"==typeof t?t:JSON.stringify(t))),i=e.innerHTML;i+=`<p><strong>${t}</strong>(${a})</p>`,e.innerHTML=i}function setErrorHistory(t,e=""){let a=document.querySelector("#LWLog");if(a){let i=a.innerHTML;i=`<p style="color: red; font-weight: bold;font-family: Consolas">(Error) ${t} ${e}</p>`+i,a.innerHTML=i}throw alert(t),new Error(t,e)}function setDataHistory(){if("DEV"!==window.MODE)return;let t={...window.localStorage};Object.keys(t).forEach((e=>{try{t[e]=JSON.parse(t[e])}catch(t){}}));let e=document.querySelector("#LWDataLog");if(!e){const t=document.createElement("div"),a=document.createElement("pre");t.classList.add("lw-log"),t.setAttribute("style","right: 0;"),a.setAttribute("id","LWDataLog"),a.setAttribute("style","white-space: pre;"),t.innerHTML="<h1>Data history</h1>",t.appendChild(a),document.body.appendChild(t),e=document.querySelector("#LWLog")}document.querySelector("#LWDataLog").innerText=JSON.stringify(t,null,2)}