
<svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" preserveAspectRatio="none" x="0px" y="0px" width="50px" height="42px" viewBox="0 0 50 42">
<defs>
<linearGradient id="Gradient_1" gradientUnits="userSpaceOnUse" x1="991.35" y1="67.45" x2="991.35" y2="95.05" spreadMethod="pad">
<stop  offset="5.098039215686274%" stop-color="#E7EFFB"/>

<stop  offset="100%" stop-color="#CBD3E0"/>
</linearGradient>

<filter id="Filter_1" x="-23.65685424949238%" y="-27.071067811865476%" width="147.31370849898477%" height="154.14213562373095%" color-interpolation-filters="sRGB">
<feGaussianBlur in="SourceGraphic" stdDeviation="1.3333333333333333,1.3333333333333333" result="result1"/>
</filter>

<g id="Symbol_68_copy_2_0_Layer0_0_FILL">
<path fill="#000000" fill-opacity="0.09803921568627451" stroke="none" d="
M 987.65 59.4
Q 983.25 55 977.05 55
L 957 55
Q 950.8 55 946.4 59.4 942 63.8 942 70
L 942 80
Q 942 86.2 946.4 90.6 950.8 95 957 95
L 977.05 95
Q 983.25 95 987.65 90.6 992.05 86.2 992.05 80
L 992.05 70
Q 992.05 63.8 987.65 59.4 Z"/>
</g>

<g id="Layer0_1_FILL">
<path fill="url(#Gradient_1)" stroke="none" d="
M 987.65 59.4
Q 983.25 55 977.05 55
L 957 55
Q 950.8 55 946.4 59.4 942 63.8 942 70
L 942 80
Q 942 86.2 946.4 90.6 950.8 95 957 95
L 977.05 95
Q 983.25 95 987.65 90.6 992.05 86.2 992.05 80
L 992.05 70
Q 992.05 63.8 987.65 59.4 Z"/>
</g>

<g id="Layer0_2_FILL">
<path fill="#000000" fill-opacity="0.047058823529411764" stroke="none" d="
M 45.6 35.6
Q 50 31.2 50 25
L 50 23.15
Q 49.8 28.9 45.6 33.1 41.2 37.5 35 37.5
L 15 37.5
Q 8.8 37.5 4.4 33.1 0.111328125 28.811328125 0 22.8
L 0 25
Q 0 31.2 4.4 35.6 8.8 40 15 40
L 35 40
Q 41.2 40 45.6 35.6 Z"/>
</g>
</defs>

<g filter="url(#Filter_1)" transform="matrix( 1, 0, 0, 1, 0,0) ">
<g transform="matrix( 1, 0, 0, 1, 0,2.5) ">
<g transform="matrix( 0.99884033203125, 0, 0, 1, -940.9,-55) ">
<use xlink:href="#Symbol_68_copy_2_0_Layer0_0_FILL"/>
</g>
</g>
</g>

<g transform="matrix( 0.99884033203125, 0, 0, 1, -940.9,-55) ">
<use xlink:href="#Layer0_1_FILL"/>
</g>

<g transform="matrix( 1, 0, 0, 1, 0,0) ">
<use xlink:href="#Layer0_2_FILL"/>
</g>
</svg>
