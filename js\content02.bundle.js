/*! For license information please see content02.bundle.js.LICENSE.txt */
(()=>{var t,e,i={778:(t,e,i)=>{i(33),i(653),i(941),i(681)},33:()=>{window.createjs=window.createjs||{},createjs.extend=function(t,e){"use strict";function i(){this.constructor=t}return i.prototype=e.prototype,t.prototype=new i},window.createjs=window.createjs||{},createjs.promote=function(t,e){"use strict";var i=t.prototype,r=Object.getPrototypeOf&&Object.getPrototypeOf(i)||i.__proto__;if(r)for(var n in i[(e+="_")+"constructor"]=r.constructor,r)i.hasOwnProperty(n)&&"function"==typeof r[n]&&(i[e+n]=r[n]);return t},window.createjs=window.createjs||{},createjs.indexOf=function(t,e){"use strict";for(var i=0,r=t.length;i<r;i++)if(e===t[i])return i;return-1},window.createjs=window.createjs||{},function(){"use strict";function t(){throw"UID cannot be instantiated"}t._nextID=0,t.get=function(){return t._nextID++},createjs.UID=t}(),window.createjs=window.createjs||{},createjs.deprecate=function(t,e){"use strict";return function(){var i="Deprecated property or method '"+e+"'. See docs for info.";return console&&(console.warn?console.warn(i):console.log(i)),t&&t.apply(this,arguments)}},window.createjs=window.createjs||{},function(){"use strict";function t(t,e,i){this.type=t,this.target=null,this.currentTarget=null,this.eventPhase=0,this.bubbles=!!e,this.cancelable=!!i,this.timeStamp=(new Date).getTime(),this.defaultPrevented=!1,this.propagationStopped=!1,this.immediatePropagationStopped=!1,this.removed=!1}var e=t.prototype;e.preventDefault=function(){this.defaultPrevented=this.cancelable&&!0},e.stopPropagation=function(){this.propagationStopped=!0},e.stopImmediatePropagation=function(){this.immediatePropagationStopped=this.propagationStopped=!0},e.remove=function(){this.removed=!0},e.clone=function(){return new t(this.type,this.bubbles,this.cancelable)},e.set=function(t){for(var e in t)this[e]=t[e];return this},e.toString=function(){return"[Event (type="+this.type+")]"},createjs.Event=t}(),window.createjs=window.createjs||{},function(){"use strict";function t(){this._listeners=null,this._captureListeners=null}var e=t.prototype;t.initialize=function(t){t.addEventListener=e.addEventListener,t.on=e.on,t.removeEventListener=t.off=e.removeEventListener,t.removeAllEventListeners=e.removeAllEventListeners,t.hasEventListener=e.hasEventListener,t.dispatchEvent=e.dispatchEvent,t._dispatchEvent=e._dispatchEvent,t.willTrigger=e.willTrigger},e.addEventListener=function(t,e,i){var r,n=(r=i?this._captureListeners=this._captureListeners||{}:this._listeners=this._listeners||{})[t];return n&&this.removeEventListener(t,e,i),(n=r[t])?n.push(e):r[t]=[e],e},e.on=function(t,e,i,r,n,s){return e.handleEvent&&(i=i||e,e=e.handleEvent),i=i||this,this.addEventListener(t,(function(t){e.call(i,t,n),r&&t.remove()}),s)},e.removeEventListener=function(t,e,i){var r=i?this._captureListeners:this._listeners;if(r){var n=r[t];if(n)for(var s=0,a=n.length;s<a;s++)if(n[s]==e){1==a?delete r[t]:n.splice(s,1);break}}},e.off=e.removeEventListener,e.removeAllEventListeners=function(t){t?(this._listeners&&delete this._listeners[t],this._captureListeners&&delete this._captureListeners[t]):this._listeners=this._captureListeners=null},e.dispatchEvent=function(t,e,i){if("string"==typeof t){var r=this._listeners;if(!(e||r&&r[t]))return!0;t=new createjs.Event(t,e,i)}else t.target&&t.clone&&(t=t.clone());try{t.target=this}catch(t){}if(t.bubbles&&this.parent){for(var n=this,s=[n];n.parent;)s.push(n=n.parent);var a,o=s.length;for(a=o-1;a>=0&&!t.propagationStopped;a--)s[a]._dispatchEvent(t,1+(0==a));for(a=1;a<o&&!t.propagationStopped;a++)s[a]._dispatchEvent(t,3)}else this._dispatchEvent(t,2);return!t.defaultPrevented},e.hasEventListener=function(t){var e=this._listeners,i=this._captureListeners;return!!(e&&e[t]||i&&i[t])},e.willTrigger=function(t){for(var e=this;e;){if(e.hasEventListener(t))return!0;e=e.parent}return!1},e.toString=function(){return"[EventDispatcher]"},e._dispatchEvent=function(t,e){var i,r,n=e<=2?this._captureListeners:this._listeners;if(t&&n&&(r=n[t.type])&&(i=r.length)){try{t.currentTarget=this}catch(t){}try{t.eventPhase=0|e}catch(t){}t.removed=!1,r=r.slice();for(var s=0;s<i&&!t.immediatePropagationStopped;s++){var a=r[s];a.handleEvent?a.handleEvent(t):a(t),t.removed&&(this.off(t.type,a,1==e),t.removed=!1)}}2===e&&this._dispatchEvent(t,2.1)},createjs.EventDispatcher=t}(),window.createjs=window.createjs||{},function(){"use strict";function t(){throw"Ticker cannot be instantiated."}t.RAF_SYNCHED="synched",t.RAF="raf",t.TIMEOUT="timeout",t.timingMode=null,t.maxDelta=0,t.paused=!1,t.removeEventListener=null,t.removeAllEventListeners=null,t.dispatchEvent=null,t.hasEventListener=null,t._listeners=null,createjs.EventDispatcher.initialize(t),t._addEventListener=t.addEventListener,t.addEventListener=function(){return!t._inited&&t.init(),t._addEventListener.apply(t,arguments)},t._inited=!1,t._startTime=0,t._pausedTime=0,t._ticks=0,t._pausedTicks=0,t._interval=50,t._lastTime=0,t._times=null,t._tickTimes=null,t._timerId=null,t._raf=!0,t._setInterval=function(e){t._interval=e,t._inited&&t._setupTick()},t.setInterval=createjs.deprecate(t._setInterval,"Ticker.setInterval"),t._getInterval=function(){return t._interval},t.getInterval=createjs.deprecate(t._getInterval,"Ticker.getInterval"),t._setFPS=function(e){t._setInterval(1e3/e)},t.setFPS=createjs.deprecate(t._setFPS,"Ticker.setFPS"),t._getFPS=function(){return 1e3/t._interval},t.getFPS=createjs.deprecate(t._getFPS,"Ticker.getFPS");try{Object.defineProperties(t,{interval:{get:t._getInterval,set:t._setInterval},framerate:{get:t._getFPS,set:t._setFPS}})}catch(t){console.log(t)}t.init=function(){t._inited||(t._inited=!0,t._times=[],t._tickTimes=[],t._startTime=t._getTime(),t._times.push(t._lastTime=0),t.interval=t._interval)},t.reset=function(){if(t._raf){var e=window.cancelAnimationFrame||window.webkitCancelAnimationFrame||window.mozCancelAnimationFrame||window.oCancelAnimationFrame||window.msCancelAnimationFrame;e&&e(t._timerId)}else clearTimeout(t._timerId);t.removeAllEventListeners("tick"),t._timerId=t._times=t._tickTimes=null,t._startTime=t._lastTime=t._ticks=t._pausedTime=0,t._inited=!1},t.getMeasuredTickTime=function(e){var i=0,r=t._tickTimes;if(!r||r.length<1)return-1;e=Math.min(r.length,e||0|t._getFPS());for(var n=0;n<e;n++)i+=r[n];return i/e},t.getMeasuredFPS=function(e){var i=t._times;return!i||i.length<2?-1:(e=Math.min(i.length-1,e||0|t._getFPS()),1e3/((i[0]-i[e])/e))},t.getTime=function(e){return t._startTime?t._getTime()-(e?t._pausedTime:0):-1},t.getEventTime=function(e){return t._startTime?(t._lastTime||t._startTime)-(e?t._pausedTime:0):-1},t.getTicks=function(e){return t._ticks-(e?t._pausedTicks:0)},t._handleSynch=function(){t._timerId=null,t._setupTick(),t._getTime()-t._lastTime>=.97*(t._interval-1)&&t._tick()},t._handleRAF=function(){t._timerId=null,t._setupTick(),t._tick()},t._handleTimeout=function(){t._timerId=null,t._setupTick(),t._tick()},t._setupTick=function(){if(null==t._timerId){var e=t.timingMode;if(e==t.RAF_SYNCHED||e==t.RAF){var i=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame;if(i)return t._timerId=i(e==t.RAF?t._handleRAF:t._handleSynch),void(t._raf=!0)}t._raf=!1,t._timerId=setTimeout(t._handleTimeout,t._interval)}},t._tick=function(){var e=t.paused,i=t._getTime(),r=i-t._lastTime;if(t._lastTime=i,t._ticks++,e&&(t._pausedTicks++,t._pausedTime+=r),t.hasEventListener("tick")){var n=new createjs.Event("tick"),s=t.maxDelta;n.delta=s&&r>s?s:r,n.paused=e,n.time=i,n.runTime=i-t._pausedTime,t.dispatchEvent(n)}for(t._tickTimes.unshift(t._getTime()-i);t._tickTimes.length>100;)t._tickTimes.pop();for(t._times.unshift(i);t._times.length>100;)t._times.pop()};var e=window,i=e.performance.now||e.performance.mozNow||e.performance.msNow||e.performance.oNow||e.performance.webkitNow;t._getTime=function(){return(i&&i.call(e.performance)||(new Date).getTime())-t._startTime},createjs.Ticker=t}(),window.createjs=window.createjs||{},function(){"use strict";function t(t){this.readyState=t.readyState,this._video=t,this._canvas=null,this._lastTime=-1,this.readyState<2&&t.addEventListener("canplaythrough",this._videoReady.bind(this))}var e=t.prototype;e.getImage=function(){if(!(this.readyState<2)){var t=this._canvas,e=this._video;if(t||((t=this._canvas=createjs.createCanvas?createjs.createCanvas():document.createElement("canvas")).width=e.videoWidth,t.height=e.videoHeight),e.readyState>=2&&e.currentTime!==this._lastTime){var i=t.getContext("2d");i.clearRect(0,0,t.width,t.height),i.drawImage(e,0,0,t.width,t.height),this._lastTime=e.currentTime}return t}},e._videoReady=function(){this.readyState=2},createjs.VideoBuffer=t}(),window.createjs=window.createjs||{},function(){"use strict";function t(t,e,i,r,n,s,a,o,c,u,l){this.Event_constructor(t,e,i),this.stageX=r,this.stageY=n,this.rawX=null==c?r:c,this.rawY=null==u?n:u,this.nativeEvent=s,this.pointerID=a,this.primary=!!o,this.relatedTarget=l}var e=createjs.extend(t,createjs.Event);e._get_localX=function(){return this.currentTarget.globalToLocal(this.rawX,this.rawY).x},e._get_localY=function(){return this.currentTarget.globalToLocal(this.rawX,this.rawY).y},e._get_isTouch=function(){return-1!==this.pointerID};try{Object.defineProperties(e,{localX:{get:e._get_localX},localY:{get:e._get_localY},isTouch:{get:e._get_isTouch}})}catch(t){}e.clone=function(){return new t(this.type,this.bubbles,this.cancelable,this.stageX,this.stageY,this.nativeEvent,this.pointerID,this.primary,this.rawX,this.rawY)},e.toString=function(){return"[MouseEvent (type="+this.type+" stageX="+this.stageX+" stageY="+this.stageY+")]"},createjs.MouseEvent=createjs.promote(t,"Event")}(),window.createjs=window.createjs||{},function(){"use strict";function t(t,e,i,r,n,s){this.setValues(t,e,i,r,n,s)}var e=t.prototype;t.DEG_TO_RAD=Math.PI/180,t.identity=null,e.setValues=function(t,e,i,r,n,s){return this.a=null==t?1:t,this.b=e||0,this.c=i||0,this.d=null==r?1:r,this.tx=n||0,this.ty=s||0,this},e.append=function(t,e,i,r,n,s){var a=this.a,o=this.b,c=this.c,u=this.d;return 1==t&&0==e&&0==i&&1==r||(this.a=a*t+c*e,this.b=o*t+u*e,this.c=a*i+c*r,this.d=o*i+u*r),this.tx=a*n+c*s+this.tx,this.ty=o*n+u*s+this.ty,this},e.prepend=function(t,e,i,r,n,s){var a=this.a,o=this.c,c=this.tx;return this.a=t*a+i*this.b,this.b=e*a+r*this.b,this.c=t*o+i*this.d,this.d=e*o+r*this.d,this.tx=t*c+i*this.ty+n,this.ty=e*c+r*this.ty+s,this},e.appendMatrix=function(t){return this.append(t.a,t.b,t.c,t.d,t.tx,t.ty)},e.prependMatrix=function(t){return this.prepend(t.a,t.b,t.c,t.d,t.tx,t.ty)},e.appendTransform=function(e,i,r,n,s,a,o,c,u){if(s%360)var l=s*t.DEG_TO_RAD,h=Math.cos(l),d=Math.sin(l);else h=1,d=0;return a||o?(a*=t.DEG_TO_RAD,o*=t.DEG_TO_RAD,this.append(Math.cos(o),Math.sin(o),-Math.sin(a),Math.cos(a),e,i),this.append(h*r,d*r,-d*n,h*n,0,0)):this.append(h*r,d*r,-d*n,h*n,e,i),(c||u)&&(this.tx-=c*this.a+u*this.c,this.ty-=c*this.b+u*this.d),this},e.prependTransform=function(e,i,r,n,s,a,o,c,u){if(s%360)var l=s*t.DEG_TO_RAD,h=Math.cos(l),d=Math.sin(l);else h=1,d=0;return(c||u)&&(this.tx-=c,this.ty-=u),a||o?(a*=t.DEG_TO_RAD,o*=t.DEG_TO_RAD,this.prepend(h*r,d*r,-d*n,h*n,0,0),this.prepend(Math.cos(o),Math.sin(o),-Math.sin(a),Math.cos(a),e,i)):this.prepend(h*r,d*r,-d*n,h*n,e,i),this},e.rotate=function(e){e*=t.DEG_TO_RAD;var i=Math.cos(e),r=Math.sin(e),n=this.a,s=this.b;return this.a=n*i+this.c*r,this.b=s*i+this.d*r,this.c=-n*r+this.c*i,this.d=-s*r+this.d*i,this},e.skew=function(e,i){return e*=t.DEG_TO_RAD,i*=t.DEG_TO_RAD,this.append(Math.cos(i),Math.sin(i),-Math.sin(e),Math.cos(e),0,0),this},e.scale=function(t,e){return this.a*=t,this.b*=t,this.c*=e,this.d*=e,this},e.translate=function(t,e){return this.tx+=this.a*t+this.c*e,this.ty+=this.b*t+this.d*e,this},e.identity=function(){return this.a=this.d=1,this.b=this.c=this.tx=this.ty=0,this},e.invert=function(){var t=this.a,e=this.b,i=this.c,r=this.d,n=this.tx,s=t*r-e*i;return this.a=r/s,this.b=-e/s,this.c=-i/s,this.d=t/s,this.tx=(i*this.ty-r*n)/s,this.ty=-(t*this.ty-e*n)/s,this},e.isIdentity=function(){return 0===this.tx&&0===this.ty&&1===this.a&&0===this.b&&0===this.c&&1===this.d},e.equals=function(t){return this.tx===t.tx&&this.ty===t.ty&&this.a===t.a&&this.b===t.b&&this.c===t.c&&this.d===t.d},e.transformPoint=function(t,e,i){return(i=i||{}).x=t*this.a+e*this.c+this.tx,i.y=t*this.b+e*this.d+this.ty,i},e.decompose=function(e){null==e&&(e={}),e.x=this.tx,e.y=this.ty,e.scaleX=Math.sqrt(this.a*this.a+this.b*this.b),e.scaleY=Math.sqrt(this.c*this.c+this.d*this.d);var i=Math.atan2(-this.c,this.d),r=Math.atan2(this.b,this.a);return Math.abs(1-i/r)<1e-5?(e.rotation=r/t.DEG_TO_RAD,this.a<0&&this.d>=0&&(e.rotation+=e.rotation<=0?180:-180),e.skewX=e.skewY=0):(e.skewX=i/t.DEG_TO_RAD,e.skewY=r/t.DEG_TO_RAD),e},e.copy=function(t){return this.setValues(t.a,t.b,t.c,t.d,t.tx,t.ty)},e.clone=function(){return new t(this.a,this.b,this.c,this.d,this.tx,this.ty)},e.toString=function(){return"[Matrix2D (a="+this.a+" b="+this.b+" c="+this.c+" d="+this.d+" tx="+this.tx+" ty="+this.ty+")]"},t.identity=new t,createjs.Matrix2D=t}(),window.createjs=window.createjs||{},function(){"use strict";function t(t,e,i,r,n){this.setValues(t,e,i,r,n)}var e=t.prototype;e.setValues=function(t,e,i,r,n){return this.visible=null==t||!!t,this.alpha=null==e?1:e,this.shadow=i,this.compositeOperation=r,this.matrix=n||this.matrix&&this.matrix.identity()||new createjs.Matrix2D,this},e.append=function(t,e,i,r,n){return this.alpha*=e,this.shadow=i||this.shadow,this.compositeOperation=r||this.compositeOperation,this.visible=this.visible&&t,n&&this.matrix.appendMatrix(n),this},e.prepend=function(t,e,i,r,n){return this.alpha*=e,this.shadow=this.shadow||i,this.compositeOperation=this.compositeOperation||r,this.visible=this.visible&&t,n&&this.matrix.prependMatrix(n),this},e.identity=function(){return this.visible=!0,this.alpha=1,this.shadow=this.compositeOperation=null,this.matrix.identity(),this},e.clone=function(){return new t(this.alpha,this.shadow,this.compositeOperation,this.visible,this.matrix.clone())},createjs.DisplayProps=t}(),window.createjs=window.createjs||{},function(){"use strict";function t(t,e){this.setValues(t,e)}var e=t.prototype;e.setValues=function(t,e){return this.x=t||0,this.y=e||0,this},e.copy=function(t){return this.x=t.x,this.y=t.y,this},e.clone=function(){return new t(this.x,this.y)},e.toString=function(){return"[Point (x="+this.x+" y="+this.y+")]"},createjs.Point=t}(),window.createjs=window.createjs||{},function(){"use strict";function t(t,e,i,r){this.setValues(t,e,i,r)}var e=t.prototype;e.setValues=function(t,e,i,r){return this.x=t||0,this.y=e||0,this.width=i||0,this.height=r||0,this},e.extend=function(t,e,i,r){return r=r||0,t+(i=i||0)>this.x+this.width&&(this.width=t+i-this.x),e+r>this.y+this.height&&(this.height=e+r-this.y),t<this.x&&(this.width+=this.x-t,this.x=t),e<this.y&&(this.height+=this.y-e,this.y=e),this},e.pad=function(t,e,i,r){return this.x-=e,this.y-=t,this.width+=e+r,this.height+=t+i,this},e.copy=function(t){return this.setValues(t.x,t.y,t.width,t.height)},e.contains=function(t,e,i,r){return i=i||0,r=r||0,t>=this.x&&t+i<=this.x+this.width&&e>=this.y&&e+r<=this.y+this.height},e.union=function(t){return this.clone().extend(t.x,t.y,t.width,t.height)},e.intersection=function(e){var i=e.x,r=e.y,n=i+e.width,s=r+e.height;return this.x>i&&(i=this.x),this.y>r&&(r=this.y),this.x+this.width<n&&(n=this.x+this.width),this.y+this.height<s&&(s=this.y+this.height),n<=i||s<=r?null:new t(i,r,n-i,s-r)},e.intersects=function(t){return t.x<=this.x+this.width&&this.x<=t.x+t.width&&t.y<=this.y+this.height&&this.y<=t.y+t.height},e.isEmpty=function(){return this.width<=0||this.height<=0},e.clone=function(){return new t(this.x,this.y,this.width,this.height)},e.toString=function(){return"[Rectangle (x="+this.x+" y="+this.y+" width="+this.width+" height="+this.height+")]"},createjs.Rectangle=t}(),window.createjs=window.createjs||{},function(){"use strict";function t(t,e,i,r,n,s,a){t.addEventListener&&(this.target=t,this.overLabel=null==i?"over":i,this.outLabel=null==e?"out":e,this.downLabel=null==r?"down":r,this.play=n,this._isPressed=!1,this._isOver=!1,this._enabled=!1,t.mouseChildren=!1,this.enabled=!0,this.handleEvent({}),s&&(a&&(s.actionsEnabled=!1,s.gotoAndStop&&s.gotoAndStop(a)),t.hitArea=s))}var e=t.prototype;e._setEnabled=function(t){if(t!=this._enabled){var e=this.target;this._enabled=t,t?(e.cursor="pointer",e.addEventListener("rollover",this),e.addEventListener("rollout",this),e.addEventListener("mousedown",this),e.addEventListener("pressup",this),e._reset&&(e.__reset=e._reset,e._reset=this._reset)):(e.cursor=null,e.removeEventListener("rollover",this),e.removeEventListener("rollout",this),e.removeEventListener("mousedown",this),e.removeEventListener("pressup",this),e.__reset&&(e._reset=e.__reset,delete e.__reset))}},e.setEnabled=createjs.deprecate(e._setEnabled,"ButtonHelper.setEnabled"),e._getEnabled=function(){return this._enabled},e.getEnabled=createjs.deprecate(e._getEnabled,"ButtonHelper.getEnabled");try{Object.defineProperties(e,{enabled:{get:e._getEnabled,set:e._setEnabled}})}catch(t){}e.toString=function(){return"[ButtonHelper]"},e.handleEvent=function(t){var e,i=this.target,r=t.type;"mousedown"==r?(this._isPressed=!0,e=this.downLabel):"pressup"==r?(this._isPressed=!1,e=this._isOver?this.overLabel:this.outLabel):"rollover"==r?(this._isOver=!0,e=this._isPressed?this.downLabel:this.overLabel):(this._isOver=!1,e=this._isPressed?this.overLabel:this.outLabel),this.play?i.gotoAndPlay&&i.gotoAndPlay(e):i.gotoAndStop&&i.gotoAndStop(e)},e._reset=function(){var t=this.paused;this.__reset(),this.paused=t},createjs.ButtonHelper=t}(),window.createjs=window.createjs||{},function(){"use strict";function t(t,e,i,r){this.color=t||"black",this.offsetX=e||0,this.offsetY=i||0,this.blur=r||0}var e=t.prototype;t.identity=new t("transparent",0,0,0),e.toString=function(){return"[Shadow]"},e.clone=function(){return new t(this.color,this.offsetX,this.offsetY,this.blur)},createjs.Shadow=t}(),window.createjs=window.createjs||{},function(){"use strict";function t(t){this.EventDispatcher_constructor(),this.complete=!0,this.framerate=0,this._animations=null,this._frames=null,this._images=null,this._data=null,this._loadCount=0,this._frameHeight=0,this._frameWidth=0,this._numFrames=0,this._regX=0,this._regY=0,this._spacing=0,this._margin=0,this._parseData(t)}var e=createjs.extend(t,createjs.EventDispatcher);e._getAnimations=function(){return this._animations.slice()},e.getAnimations=createjs.deprecate(e._getAnimations,"SpriteSheet.getAnimations");try{Object.defineProperties(e,{animations:{get:e._getAnimations}})}catch(t){}e.getNumFrames=function(t){if(null==t)return this._frames?this._frames.length:this._numFrames||0;var e=this._data[t];return null==e?0:e.frames.length},e.getAnimation=function(t){return this._data[t]},e.getFrame=function(t){var e;return this._frames&&(e=this._frames[t])?e:null},e.getFrameBounds=function(t,e){var i=this.getFrame(t);return i?(e||new createjs.Rectangle).setValues(-i.regX,-i.regY,i.rect.width,i.rect.height):null},e.toString=function(){return"[SpriteSheet]"},e.clone=function(){throw"SpriteSheet cannot be cloned."},e._parseData=function(t){var e,i,r,n;if(null!=t){if(this.framerate=t.framerate||0,t.images&&(i=t.images.length)>0)for(n=this._images=[],e=0;e<i;e++){var s=t.images[e];if("string"==typeof s){var a=s;(s=document.createElement("img")).src=a}n.push(s),s.getContext||s.naturalWidth||(this._loadCount++,this.complete=!1,function(t,e){s.onload=function(){t._handleImageLoad(e)}}(this,a),function(t,e){s.onerror=function(){t._handleImageError(e)}}(this,a))}if(null==t.frames);else if(Array.isArray(t.frames))for(this._frames=[],e=0,i=(n=t.frames).length;e<i;e++){var o=n[e];this._frames.push({image:this._images[o[4]?o[4]:0],rect:new createjs.Rectangle(o[0],o[1],o[2],o[3]),regX:o[5]||0,regY:o[6]||0})}else r=t.frames,this._frameWidth=r.width,this._frameHeight=r.height,this._regX=r.regX||0,this._regY=r.regY||0,this._spacing=r.spacing||0,this._margin=r.margin||0,this._numFrames=r.count,0==this._loadCount&&this._calculateFrames();var c;if(this._animations=[],null!=(r=t.animations))for(c in this._data={},r){var u={name:c},l=r[c];if("number"==typeof l)n=u.frames=[l];else if(Array.isArray(l))if(1==l.length)u.frames=[l[0]];else for(u.speed=l[3],u.next=l[2],n=u.frames=[],e=l[0];e<=l[1];e++)n.push(e);else{u.speed=l.speed,u.next=l.next;var h=l.frames;n=u.frames="number"==typeof h?[h]:h.slice(0)}!0!==u.next&&void 0!==u.next||(u.next=c),(!1===u.next||n.length<2&&u.next==c)&&(u.next=null),u.speed||(u.speed=1),this._animations.push(c),this._data[c]=u}}},e._handleImageLoad=function(t){0==--this._loadCount&&(this._calculateFrames(),this.complete=!0,this.dispatchEvent("complete"))},e._handleImageError=function(t){var e=new createjs.Event("error");e.src=t,this.dispatchEvent(e),0==--this._loadCount&&this.dispatchEvent("complete")},e._calculateFrames=function(){if(!this._frames&&0!=this._frameWidth){this._frames=[];var t=this._numFrames||1e5,e=0,i=this._frameWidth,r=this._frameHeight,n=this._spacing,s=this._margin;t:for(var a=0,o=this._images;a<o.length;a++)for(var c=o[a],u=c.width||c.naturalWidth,l=c.height||c.naturalHeight,h=s;h<=l-s-r;){for(var d=s;d<=u-s-i;){if(e>=t)break t;e++,this._frames.push({image:c,rect:new createjs.Rectangle(d,h,i,r),regX:this._regX,regY:this._regY}),d+=i+n}h+=r+n}this._numFrames=e}},createjs.SpriteSheet=createjs.promote(t,"EventDispatcher")}(),window.createjs=window.createjs||{},function(){"use strict";function t(){this.command=null,this._stroke=null,this._strokeStyle=null,this._oldStrokeStyle=null,this._strokeDash=null,this._oldStrokeDash=null,this._strokeIgnoreScale=!1,this._fill=null,this._instructions=[],this._commitIndex=0,this._activeInstructions=[],this._dirty=!1,this._storeIndex=0,this.clear()}var e=t.prototype,i=t;t.getRGB=function(t,e,i,r){return null!=t&&null==i&&(r=e,i=255&t,e=t>>8&255,t=t>>16&255),null==r?"rgb("+t+","+e+","+i+")":"rgba("+t+","+e+","+i+","+r+")"},t.getHSL=function(t,e,i,r){return null==r?"hsl("+t%360+","+e+"%,"+i+"%)":"hsla("+t%360+","+e+"%,"+i+"%,"+r+")"},t.BASE_64={A:0,B:1,C:2,D:3,E:4,F:5,G:6,H:7,I:8,J:9,K:10,L:11,M:12,N:13,O:14,P:15,Q:16,R:17,S:18,T:19,U:20,V:21,W:22,X:23,Y:24,Z:25,a:26,b:27,c:28,d:29,e:30,f:31,g:32,h:33,i:34,j:35,k:36,l:37,m:38,n:39,o:40,p:41,q:42,r:43,s:44,t:45,u:46,v:47,w:48,x:49,y:50,z:51,0:52,1:53,2:54,3:55,4:56,5:57,6:58,7:59,8:60,9:61,"+":62,"/":63},t.STROKE_CAPS_MAP=["butt","round","square"],t.STROKE_JOINTS_MAP=["miter","round","bevel"];var r=createjs.createCanvas?createjs.createCanvas():document.createElement("canvas");r.getContext&&(t._ctx=r.getContext("2d"),r.width=r.height=1),e._getInstructions=function(){return this._updateInstructions(),this._instructions},e.getInstructions=createjs.deprecate(e._getInstructions,"Graphics.getInstructions");try{Object.defineProperties(e,{instructions:{get:e._getInstructions}})}catch(t){}e.isEmpty=function(){return!(this._instructions.length||this._activeInstructions.length)},e.draw=function(t,e){this._updateInstructions();for(var i=this._instructions,r=this._storeIndex,n=i.length;r<n;r++)i[r].exec(t,e)},e.drawAsPath=function(t){this._updateInstructions();for(var e,i=this._instructions,r=this._storeIndex,n=i.length;r<n;r++)!1!==(e=i[r]).path&&e.exec(t)},e.moveTo=function(t,e){return this.append(new i.MoveTo(t,e),!0)},e.lineTo=function(t,e){return this.append(new i.LineTo(t,e))},e.arcTo=function(t,e,r,n,s){return this.append(new i.ArcTo(t,e,r,n,s))},e.arc=function(t,e,r,n,s,a){return this.append(new i.Arc(t,e,r,n,s,a))},e.quadraticCurveTo=function(t,e,r,n){return this.append(new i.QuadraticCurveTo(t,e,r,n))},e.bezierCurveTo=function(t,e,r,n,s,a){return this.append(new i.BezierCurveTo(t,e,r,n,s,a))},e.rect=function(t,e,r,n){return this.append(new i.Rect(t,e,r,n))},e.closePath=function(){return this._activeInstructions.length?this.append(new i.ClosePath):this},e.clear=function(){return this._instructions.length=this._activeInstructions.length=this._commitIndex=0,this._strokeStyle=this._oldStrokeStyle=this._stroke=this._fill=this._strokeDash=this._oldStrokeDash=null,this._dirty=this._strokeIgnoreScale=!1,this},e.beginFill=function(t){return this._setFill(t?new i.Fill(t):null)},e.beginLinearGradientFill=function(t,e,r,n,s,a){return this._setFill((new i.Fill).linearGradient(t,e,r,n,s,a))},e.beginRadialGradientFill=function(t,e,r,n,s,a,o,c){return this._setFill((new i.Fill).radialGradient(t,e,r,n,s,a,o,c))},e.beginBitmapFill=function(t,e,r){return this._setFill(new i.Fill(null,r).bitmap(t,e))},e.endFill=function(){return this.beginFill()},e.setStrokeStyle=function(t,e,r,n,s){return this._updateInstructions(!0),this._strokeStyle=this.command=new i.StrokeStyle(t,e,r,n,s),this._stroke&&(this._stroke.ignoreScale=s),this._strokeIgnoreScale=s,this},e.setStrokeDash=function(t,e){return this._updateInstructions(!0),this._strokeDash=this.command=new i.StrokeDash(t,e),this},e.beginStroke=function(t){return this._setStroke(t?new i.Stroke(t):null)},e.beginLinearGradientStroke=function(t,e,r,n,s,a){return this._setStroke((new i.Stroke).linearGradient(t,e,r,n,s,a))},e.beginRadialGradientStroke=function(t,e,r,n,s,a,o,c){return this._setStroke((new i.Stroke).radialGradient(t,e,r,n,s,a,o,c))},e.beginBitmapStroke=function(t,e){return this._setStroke((new i.Stroke).bitmap(t,e))},e.endStroke=function(){return this.beginStroke()},e.curveTo=e.quadraticCurveTo,e.drawRect=e.rect,e.drawRoundRect=function(t,e,i,r,n){return this.drawRoundRectComplex(t,e,i,r,n,n,n,n)},e.drawRoundRectComplex=function(t,e,r,n,s,a,o,c){return this.append(new i.RoundRect(t,e,r,n,s,a,o,c))},e.drawCircle=function(t,e,r){return this.append(new i.Circle(t,e,r))},e.drawEllipse=function(t,e,r,n){return this.append(new i.Ellipse(t,e,r,n))},e.drawPolyStar=function(t,e,r,n,s,a){return this.append(new i.PolyStar(t,e,r,n,s,a))},e.append=function(t,e){return this._activeInstructions.push(t),this.command=t,e||(this._dirty=!0),this},e.decodePath=function(e){for(var i=[this.moveTo,this.lineTo,this.quadraticCurveTo,this.bezierCurveTo,this.closePath],r=[2,2,4,6,0],n=0,s=e.length,a=[],o=0,c=0,u=t.BASE_64;n<s;){var l=e.charAt(n),h=u[l],d=h>>3,f=i[d];if(!f||3&h)throw"bad path data (@"+n+"): "+l;var p=r[d];d||(o=c=0),a.length=0,n++;for(var _=2+(h>>2&1),v=0;v<p;v++){var m=u[e.charAt(n)],g=m>>5?-1:1;m=(31&m)<<6|u[e.charAt(n+1)],3==_&&(m=m<<6|u[e.charAt(n+2)]),m=g*m/10,v%2?o=m+=o:c=m+=c,a[v]=m,n+=_}f.apply(this,a)}return this},e.store=function(){return this._updateInstructions(!0),this._storeIndex=this._instructions.length,this},e.unstore=function(){return this._storeIndex=0,this},e.clone=function(){var e=new t;return e.command=this.command,e._stroke=this._stroke,e._strokeStyle=this._strokeStyle,e._strokeDash=this._strokeDash,e._strokeIgnoreScale=this._strokeIgnoreScale,e._fill=this._fill,e._instructions=this._instructions.slice(),e._commitIndex=this._commitIndex,e._activeInstructions=this._activeInstructions.slice(),e._dirty=this._dirty,e._storeIndex=this._storeIndex,e},e.toString=function(){return"[Graphics]"},e.mt=e.moveTo,e.lt=e.lineTo,e.at=e.arcTo,e.bt=e.bezierCurveTo,e.qt=e.quadraticCurveTo,e.a=e.arc,e.r=e.rect,e.cp=e.closePath,e.c=e.clear,e.f=e.beginFill,e.lf=e.beginLinearGradientFill,e.rf=e.beginRadialGradientFill,e.bf=e.beginBitmapFill,e.ef=e.endFill,e.ss=e.setStrokeStyle,e.sd=e.setStrokeDash,e.s=e.beginStroke,e.ls=e.beginLinearGradientStroke,e.rs=e.beginRadialGradientStroke,e.bs=e.beginBitmapStroke,e.es=e.endStroke,e.dr=e.drawRect,e.rr=e.drawRoundRect,e.rc=e.drawRoundRectComplex,e.dc=e.drawCircle,e.de=e.drawEllipse,e.dp=e.drawPolyStar,e.p=e.decodePath,e._updateInstructions=function(e){var i=this._instructions,r=this._activeInstructions,n=this._commitIndex;if(this._dirty&&r.length){i.length=n,i.push(t.beginCmd);var s=r.length,a=i.length;i.length=a+s;for(var o=0;o<s;o++)i[o+a]=r[o];this._fill&&i.push(this._fill),this._stroke&&(this._strokeDash!==this._oldStrokeDash&&i.push(this._strokeDash),this._strokeStyle!==this._oldStrokeStyle&&i.push(this._strokeStyle),e&&(this._oldStrokeStyle=this._strokeStyle,this._oldStrokeDash=this._strokeDash),i.push(this._stroke)),this._dirty=!1}e&&(r.length=0,this._commitIndex=i.length)},e._setFill=function(t){return this._updateInstructions(!0),this.command=this._fill=t,this},e._setStroke=function(t){return this._updateInstructions(!0),(this.command=this._stroke=t)&&(t.ignoreScale=this._strokeIgnoreScale),this},(i.LineTo=function(t,e){this.x=t,this.y=e}).prototype.exec=function(t){t.lineTo(this.x,this.y)},(i.MoveTo=function(t,e){this.x=t,this.y=e}).prototype.exec=function(t){t.moveTo(this.x,this.y)},(i.ArcTo=function(t,e,i,r,n){this.x1=t,this.y1=e,this.x2=i,this.y2=r,this.radius=n}).prototype.exec=function(t){t.arcTo(this.x1,this.y1,this.x2,this.y2,this.radius)},(i.Arc=function(t,e,i,r,n,s){this.x=t,this.y=e,this.radius=i,this.startAngle=r,this.endAngle=n,this.anticlockwise=!!s}).prototype.exec=function(t){t.arc(this.x,this.y,this.radius,this.startAngle,this.endAngle,this.anticlockwise)},(i.QuadraticCurveTo=function(t,e,i,r){this.cpx=t,this.cpy=e,this.x=i,this.y=r}).prototype.exec=function(t){t.quadraticCurveTo(this.cpx,this.cpy,this.x,this.y)},(i.BezierCurveTo=function(t,e,i,r,n,s){this.cp1x=t,this.cp1y=e,this.cp2x=i,this.cp2y=r,this.x=n,this.y=s}).prototype.exec=function(t){t.bezierCurveTo(this.cp1x,this.cp1y,this.cp2x,this.cp2y,this.x,this.y)},(i.Rect=function(t,e,i,r){this.x=t,this.y=e,this.w=i,this.h=r}).prototype.exec=function(t){t.rect(this.x,this.y,this.w,this.h)},(i.ClosePath=function(){}).prototype.exec=function(t){t.closePath()},(i.BeginPath=function(){}).prototype.exec=function(t){t.beginPath()},(e=(i.Fill=function(t,e){this.style=t,this.matrix=e}).prototype).exec=function(t){if(this.style){t.fillStyle=this.style;var e=this.matrix;e&&(t.save(),t.transform(e.a,e.b,e.c,e.d,e.tx,e.ty)),t.fill(),e&&t.restore()}},e.linearGradient=function(e,i,r,n,s,a){for(var o=this.style=t._ctx.createLinearGradient(r,n,s,a),c=0,u=e.length;c<u;c++)o.addColorStop(i[c],e[c]);return o.props={colors:e,ratios:i,x0:r,y0:n,x1:s,y1:a,type:"linear"},this},e.radialGradient=function(e,i,r,n,s,a,o,c){for(var u=this.style=t._ctx.createRadialGradient(r,n,s,a,o,c),l=0,h=e.length;l<h;l++)u.addColorStop(i[l],e[l]);return u.props={colors:e,ratios:i,x0:r,y0:n,r0:s,x1:a,y1:o,r1:c,type:"radial"},this},e.bitmap=function(e,i){(e.naturalWidth||e.getContext||e.readyState>=2)&&((this.style=t._ctx.createPattern(e,i||"")).props={image:e,repetition:i,type:"bitmap"});return this},e.path=!1,(e=(i.Stroke=function(t,e){this.style=t,this.ignoreScale=e}).prototype).exec=function(t){this.style&&(t.strokeStyle=this.style,this.ignoreScale&&(t.save(),t.setTransform(1,0,0,1,0,0)),t.stroke(),this.ignoreScale&&t.restore())},e.linearGradient=i.Fill.prototype.linearGradient,e.radialGradient=i.Fill.prototype.radialGradient,e.bitmap=i.Fill.prototype.bitmap,e.path=!1,(e=(i.StrokeStyle=function(t,e,i,r,n){this.width=t,this.caps=e,this.joints=i,this.miterLimit=r,this.ignoreScale=n}).prototype).exec=function(e){e.lineWidth=null==this.width?"1":this.width,e.lineCap=null==this.caps?"butt":isNaN(this.caps)?this.caps:t.STROKE_CAPS_MAP[this.caps],e.lineJoin=null==this.joints?"miter":isNaN(this.joints)?this.joints:t.STROKE_JOINTS_MAP[this.joints],e.miterLimit=null==this.miterLimit?"10":this.miterLimit,e.ignoreScale=null!=this.ignoreScale&&this.ignoreScale},e.path=!1,(i.StrokeDash=function(t,e){this.segments=t,this.offset=e||0}).prototype.exec=function(t){t.setLineDash&&(t.setLineDash(this.segments||i.StrokeDash.EMPTY_SEGMENTS),t.lineDashOffset=this.offset||0)},i.StrokeDash.EMPTY_SEGMENTS=[],(i.RoundRect=function(t,e,i,r,n,s,a,o){this.x=t,this.y=e,this.w=i,this.h=r,this.radiusTL=n,this.radiusTR=s,this.radiusBR=a,this.radiusBL=o}).prototype.exec=function(t){var e=(c<u?c:u)/2,i=0,r=0,n=0,s=0,a=this.x,o=this.y,c=this.w,u=this.h,l=this.radiusTL,h=this.radiusTR,d=this.radiusBR,f=this.radiusBL;l<0&&(l*=i=-1),l>e&&(l=e),h<0&&(h*=r=-1),h>e&&(h=e),d<0&&(d*=n=-1),d>e&&(d=e),f<0&&(f*=s=-1),f>e&&(f=e),t.moveTo(a+c-h,o),t.arcTo(a+c+h*r,o-h*r,a+c,o+h,h),t.lineTo(a+c,o+u-d),t.arcTo(a+c+d*n,o+u+d*n,a+c-d,o+u,d),t.lineTo(a+f,o+u),t.arcTo(a-f*s,o+u+f*s,a,o+u-f,f),t.lineTo(a,o+l),t.arcTo(a-l*i,o-l*i,a+l,o,l),t.closePath()},(i.Circle=function(t,e,i){this.x=t,this.y=e,this.radius=i}).prototype.exec=function(t){t.arc(this.x,this.y,this.radius,0,2*Math.PI)},(i.Ellipse=function(t,e,i,r){this.x=t,this.y=e,this.w=i,this.h=r}).prototype.exec=function(t){var e=this.x,i=this.y,r=this.w,n=this.h,s=.5522848,a=r/2*s,o=n/2*s,c=e+r,u=i+n,l=e+r/2,h=i+n/2;t.moveTo(e,h),t.bezierCurveTo(e,h-o,l-a,i,l,i),t.bezierCurveTo(l+a,i,c,h-o,c,h),t.bezierCurveTo(c,h+o,l+a,u,l,u),t.bezierCurveTo(l-a,u,e,h+o,e,h)},(i.PolyStar=function(t,e,i,r,n,s){this.x=t,this.y=e,this.radius=i,this.sides=r,this.pointSize=n,this.angle=s}).prototype.exec=function(t){var e=this.x,i=this.y,r=this.radius,n=(this.angle||0)/180*Math.PI,s=this.sides,a=1-(this.pointSize||0),o=Math.PI/s;t.moveTo(e+Math.cos(n)*r,i+Math.sin(n)*r);for(var c=0;c<s;c++)n+=o,1!=a&&t.lineTo(e+Math.cos(n)*r*a,i+Math.sin(n)*r*a),n+=o,t.lineTo(e+Math.cos(n)*r,i+Math.sin(n)*r);t.closePath()},t.beginCmd=new i.BeginPath,createjs.Graphics=t}(),window.createjs=window.createjs||{},function(){"use strict";function t(){this.EventDispatcher_constructor(),this.alpha=1,this.cacheCanvas=null,this.bitmapCache=null,this.id=createjs.UID.get(),this.mouseEnabled=!0,this.tickEnabled=!0,this.name=null,this.parent=null,this.regX=0,this.regY=0,this.rotation=0,this.scaleX=1,this.scaleY=1,this.skewX=0,this.skewY=0,this.shadow=null,this.visible=!0,this.x=0,this.y=0,this.transformMatrix=null,this.compositeOperation=null,this.snapToPixel=!0,this.filters=null,this.mask=null,this.hitArea=null,this.cursor=null,this._props=new createjs.DisplayProps,this._rectangle=new createjs.Rectangle,this._bounds=null,this._webGLRenderStyle=t._StageGL_NONE}var e=createjs.extend(t,createjs.EventDispatcher);t._MOUSE_EVENTS=["click","dblclick","mousedown","mouseout","mouseover","pressmove","pressup","rollout","rollover"],t.suppressCrossDomainErrors=!1,t._snapToPixelEnabled=!1,t._StageGL_NONE=0,t._StageGL_SPRITE=1,t._StageGL_BITMAP=2;var i=createjs.createCanvas?createjs.createCanvas():document.createElement("canvas");i.getContext&&(t._hitTestCanvas=i,t._hitTestContext=i.getContext("2d"),i.width=i.height=1),e._getStage=function(){for(var t=this,e=createjs.Stage;t.parent;)t=t.parent;return t instanceof e?t:null},e.getStage=createjs.deprecate(e._getStage,"DisplayObject.getStage");try{Object.defineProperties(e,{stage:{get:e._getStage},cacheID:{get:function(){return this.bitmapCache&&this.bitmapCache.cacheID},set:function(t){this.bitmapCache&&(this.bitmapCache.cacheID=t)}},scale:{get:function(){return this.scaleX},set:function(t){this.scaleX=this.scaleY=t}}})}catch(t){}e.isVisible=function(){return!!(this.visible&&this.alpha>0&&0!=this.scaleX&&0!=this.scaleY)},e.draw=function(t,e){var i=this.bitmapCache;return!(!i||e)&&i.draw(t)},e.updateContext=function(e){var i=this,r=i.mask,n=i._props.matrix;r&&r.graphics&&!r.graphics.isEmpty()&&(r.getMatrix(n),e.transform(n.a,n.b,n.c,n.d,n.tx,n.ty),r.graphics.drawAsPath(e),e.clip(),n.invert(),e.transform(n.a,n.b,n.c,n.d,n.tx,n.ty)),this.getMatrix(n);var s=n.tx,a=n.ty;t._snapToPixelEnabled&&i.snapToPixel&&(s=s+(s<0?-.5:.5)|0,a=a+(a<0?-.5:.5)|0),e.transform(n.a,n.b,n.c,n.d,s,a),e.globalAlpha*=i.alpha,i.compositeOperation&&(e.globalCompositeOperation=i.compositeOperation),i.shadow&&this._applyShadow(e,i.shadow)},e.cache=function(t,e,i,r,n,s){this.bitmapCache||(this.bitmapCache=new createjs.BitmapCache),this.bitmapCache.define(this,t,e,i,r,n,s)},e.updateCache=function(t){if(!this.bitmapCache)throw"cache() must be called before updateCache()";this.bitmapCache.update(t)},e.uncache=function(){this.bitmapCache&&(this.bitmapCache.release(),this.bitmapCache=void 0)},e.getCacheDataURL=function(){return this.bitmapCache?this.bitmapCache.getDataURL():null},e.localToGlobal=function(t,e,i){return this.getConcatenatedMatrix(this._props.matrix).transformPoint(t,e,i||new createjs.Point)},e.globalToLocal=function(t,e,i){return this.getConcatenatedMatrix(this._props.matrix).invert().transformPoint(t,e,i||new createjs.Point)},e.localToLocal=function(t,e,i,r){return r=this.localToGlobal(t,e,r),i.globalToLocal(r.x,r.y,r)},e.setTransform=function(t,e,i,r,n,s,a,o,c){return this.x=t||0,this.y=e||0,this.scaleX=null==i?1:i,this.scaleY=null==r?1:r,this.rotation=n||0,this.skewX=s||0,this.skewY=a||0,this.regX=o||0,this.regY=c||0,this},e.getMatrix=function(t){var e=this,i=t&&t.identity()||new createjs.Matrix2D;return e.transformMatrix?i.copy(e.transformMatrix):i.appendTransform(e.x,e.y,e.scaleX,e.scaleY,e.rotation,e.skewX,e.skewY,e.regX,e.regY)},e.getConcatenatedMatrix=function(t){for(var e=this,i=this.getMatrix(t);e=e.parent;)i.prependMatrix(e.getMatrix(e._props.matrix));return i},e.getConcatenatedDisplayProps=function(t){t=t?t.identity():new createjs.DisplayProps;var e=this,i=e.getMatrix(t.matrix);do{t.prepend(e.visible,e.alpha,e.shadow,e.compositeOperation),e!=this&&i.prependMatrix(e.getMatrix(e._props.matrix))}while(e=e.parent);return t},e.hitTest=function(e,i){var r=t._hitTestContext;r.setTransform(1,0,0,1,-e,-i),this.draw(r);var n=this._testHit(r);return r.setTransform(1,0,0,1,0,0),r.clearRect(0,0,2,2),n},e.set=function(t){for(var e in t)this[e]=t[e];return this},e.getBounds=function(){if(this._bounds)return this._rectangle.copy(this._bounds);var t=this.cacheCanvas;if(t){var e=this._cacheScale;return this._rectangle.setValues(this._cacheOffsetX,this._cacheOffsetY,t.width/e,t.height/e)}return null},e.getTransformedBounds=function(){return this._getBounds()},e.setBounds=function(t,e,i,r){this._bounds=null!=t?(this._bounds||new createjs.Rectangle).setValues(t,e,i,r):t},e.clone=function(){return this._cloneProps(new t)},e.toString=function(){return"[DisplayObject (name="+this.name+")]"},e._updateState=null,e._cloneProps=function(t){return t.alpha=this.alpha,t.mouseEnabled=this.mouseEnabled,t.tickEnabled=this.tickEnabled,t.name=this.name,t.regX=this.regX,t.regY=this.regY,t.rotation=this.rotation,t.scaleX=this.scaleX,t.scaleY=this.scaleY,t.shadow=this.shadow,t.skewX=this.skewX,t.skewY=this.skewY,t.visible=this.visible,t.x=this.x,t.y=this.y,t.compositeOperation=this.compositeOperation,t.snapToPixel=this.snapToPixel,t.filters=null==this.filters?null:this.filters.slice(0),t.mask=this.mask,t.hitArea=this.hitArea,t.cursor=this.cursor,t._bounds=this._bounds,t},e._applyShadow=function(t,e){e=e||Shadow.identity,t.shadowColor=e.color,t.shadowOffsetX=e.offsetX,t.shadowOffsetY=e.offsetY,t.shadowBlur=e.blur},e._tick=function(t){var e=this._listeners;e&&e.tick&&(t.target=null,t.propagationStopped=t.immediatePropagationStopped=!1,this.dispatchEvent(t))},e._testHit=function(e){try{var i=e.getImageData(0,0,1,1).data[3]>1}catch(e){if(!t.suppressCrossDomainErrors)throw"An error has occurred. This is most likely due to security restrictions on reading canvas pixel data with local or cross-domain images."}return i},e._getBounds=function(t,e){return this._transformBounds(this.getBounds(),t,e)},e._transformBounds=function(t,e,i){if(!t)return t;var r=t.x,n=t.y,s=t.width,a=t.height,o=this._props.matrix;o=i?o.identity():this.getMatrix(o),(r||n)&&o.appendTransform(0,0,1,1,0,0,0,-r,-n),e&&o.prependMatrix(e);var c=s*o.a,u=s*o.b,l=a*o.c,h=a*o.d,d=o.tx,f=o.ty,p=d,_=d,v=f,m=f;return(r=c+d)<p?p=r:r>_&&(_=r),(r=c+l+d)<p?p=r:r>_&&(_=r),(r=l+d)<p?p=r:r>_&&(_=r),(n=u+f)<v?v=n:n>m&&(m=n),(n=u+h+f)<v?v=n:n>m&&(m=n),(n=h+f)<v?v=n:n>m&&(m=n),t.setValues(p,v,_-p,m-v)},e._hasMouseEventListener=function(){for(var e=t._MOUSE_EVENTS,i=0,r=e.length;i<r;i++)if(this.hasEventListener(e[i]))return!0;return!!this.cursor},createjs.DisplayObject=createjs.promote(t,"EventDispatcher")}(),window.createjs=window.createjs||{},function(){"use strict";function t(){this.DisplayObject_constructor(),this.children=[],this.mouseChildren=!0,this.tickChildren=!0}var e=createjs.extend(t,createjs.DisplayObject);e._getNumChildren=function(){return this.children.length},e.getNumChildren=createjs.deprecate(e._getNumChildren,"Container.getNumChildren");try{Object.defineProperties(e,{numChildren:{get:e._getNumChildren}})}catch(t){}e.initialize=t,e.isVisible=function(){var t=this.cacheCanvas||this.children.length;return!!(this.visible&&this.alpha>0&&0!=this.scaleX&&0!=this.scaleY&&t)},e.draw=function(t,e){if(this.DisplayObject_draw(t,e))return!0;for(var i=this.children.slice(),r=0,n=i.length;r<n;r++){var s=i[r];s.isVisible()&&(t.save(),s.updateContext(t),s.draw(t),t.restore())}return!0},e.addChild=function(t){if(null==t)return t;var e=arguments.length;if(e>1){for(var i=0;i<e;i++)this.addChild(arguments[i]);return arguments[e-1]}var r=t.parent,n=r===this;return r&&r._removeChildAt(createjs.indexOf(r.children,t),n),t.parent=this,this.children.push(t),n||t.dispatchEvent("added"),t},e.addChildAt=function(t,e){var i=arguments.length,r=arguments[i-1];if(r<0||r>this.children.length)return arguments[i-2];if(i>2){for(var n=0;n<i-1;n++)this.addChildAt(arguments[n],r+n);return arguments[i-2]}var s=t.parent,a=s===this;return s&&s._removeChildAt(createjs.indexOf(s.children,t),a),t.parent=this,this.children.splice(e,0,t),a||t.dispatchEvent("added"),t},e.removeChild=function(t){var e=arguments.length;if(e>1){for(var i=!0,r=0;r<e;r++)i=i&&this.removeChild(arguments[r]);return i}return this._removeChildAt(createjs.indexOf(this.children,t))},e.removeChildAt=function(t){var e=arguments.length;if(e>1){for(var i=[],r=0;r<e;r++)i[r]=arguments[r];i.sort((function(t,e){return e-t}));var n=!0;for(r=0;r<e;r++)n=n&&this._removeChildAt(i[r]);return n}return this._removeChildAt(t)},e.removeAllChildren=function(){for(var t=this.children;t.length;)this._removeChildAt(0)},e.getChildAt=function(t){return this.children[t]},e.getChildByName=function(t){for(var e=this.children,i=0,r=e.length;i<r;i++)if(e[i].name==t)return e[i];return null},e.sortChildren=function(t){this.children.sort(t)},e.getChildIndex=function(t){return createjs.indexOf(this.children,t)},e.swapChildrenAt=function(t,e){var i=this.children,r=i[t],n=i[e];r&&n&&(i[t]=n,i[e]=r)},e.swapChildren=function(t,e){for(var i,r,n=this.children,s=0,a=n.length;s<a&&(n[s]==t&&(i=s),n[s]==e&&(r=s),null==i||null==r);s++);s!=a&&(n[i]=e,n[r]=t)},e.setChildIndex=function(t,e){var i=this.children,r=i.length;if(!(t.parent!=this||e<0||e>=r)){for(var n=0;n<r&&i[n]!=t;n++);n!=r&&n!=e&&(i.splice(n,1),i.splice(e,0,t))}},e.contains=function(t){for(;t;){if(t==this)return!0;t=t.parent}return!1},e.hitTest=function(t,e){return null!=this.getObjectUnderPoint(t,e)},e.getObjectsUnderPoint=function(t,e,i){var r=[],n=this.localToGlobal(t,e);return this._getObjectsUnderPoint(n.x,n.y,r,i>0,1==i),r},e.getObjectUnderPoint=function(t,e,i){var r=this.localToGlobal(t,e);return this._getObjectsUnderPoint(r.x,r.y,null,i>0,1==i)},e.getBounds=function(){return this._getBounds(null,!0)},e.getTransformedBounds=function(){return this._getBounds()},e.clone=function(e){var i=this._cloneProps(new t);return e&&this._cloneChildren(i),i},e.toString=function(){return"[Container (name="+this.name+")]"},e._tick=function(t){if(this.tickChildren)for(var e=this.children.length-1;e>=0;e--){var i=this.children[e];i.tickEnabled&&i._tick&&i._tick(t)}this.DisplayObject__tick(t)},e._cloneChildren=function(t){t.children.length&&t.removeAllChildren();for(var e=t.children,i=0,r=this.children.length;i<r;i++){var n=this.children[i].clone(!0);n.parent=t,e.push(n)}},e._removeChildAt=function(t,e){if(t<0||t>this.children.length-1)return!1;var i=this.children[t];return i&&(i.parent=null),this.children.splice(t,1),e||i.dispatchEvent("removed"),!0},e._getObjectsUnderPoint=function(e,i,r,n,s,a){if(!(a=a||0)&&!this._testMask(this,e,i))return null;var o,c=createjs.DisplayObject._hitTestContext;s=s||n&&this._hasMouseEventListener();for(var u=this.children,l=u.length-1;l>=0;l--){var h=u[l],d=h.hitArea;if(h.visible&&(d||h.isVisible())&&(!n||h.mouseEnabled)&&(d||this._testMask(h,e,i)))if(!d&&h instanceof t){var f=h._getObjectsUnderPoint(e,i,r,n,s,a+1);if(!r&&f)return n&&!this.mouseChildren?this:f}else{if(n&&!s&&!h._hasMouseEventListener())continue;var p=h.getConcatenatedDisplayProps(h._props);if(o=p.matrix,d&&(o.appendMatrix(d.getMatrix(d._props.matrix)),p.alpha=d.alpha),c.globalAlpha=p.alpha,c.setTransform(o.a,o.b,o.c,o.d,o.tx-e,o.ty-i),(d||h).draw(c),!this._testHit(c))continue;if(c.setTransform(1,0,0,1,0,0),c.clearRect(0,0,2,2),!r)return n&&!this.mouseChildren?this:h;r.push(h)}}return null},e._testMask=function(t,e,i){var r=t.mask;if(!r||!r.graphics||r.graphics.isEmpty())return!0;var n=this._props.matrix,s=t.parent;n=s?s.getConcatenatedMatrix(n):n.identity(),n=r.getMatrix(r._props.matrix).prependMatrix(n);var a=createjs.DisplayObject._hitTestContext;return a.setTransform(n.a,n.b,n.c,n.d,n.tx-e,n.ty-i),r.graphics.drawAsPath(a),a.fillStyle="#000",a.fill(),!!this._testHit(a)&&(a.setTransform(1,0,0,1,0,0),a.clearRect(0,0,2,2),!0)},e._getBounds=function(t,e){var i=this.DisplayObject_getBounds();if(i)return this._transformBounds(i,t,e);var r=this._props.matrix;r=e?r.identity():this.getMatrix(r),t&&r.prependMatrix(t);for(var n=this.children.length,s=null,a=0;a<n;a++){var o=this.children[a];o.visible&&(i=o._getBounds(r))&&(s?s.extend(i.x,i.y,i.width,i.height):s=i.clone())}return s},createjs.Container=createjs.promote(t,"DisplayObject")}(),window.createjs=window.createjs||{},function(){"use strict";function t(t){this.Container_constructor(),this.autoClear=!0,this.canvas="string"==typeof t?document.getElementById(t):t,this.mouseX=0,this.mouseY=0,this.drawRect=null,this.snapToPixelEnabled=!1,this.mouseInBounds=!1,this.tickOnUpdate=!0,this.mouseMoveOutside=!1,this.preventSelection=!0,this._pointerData={},this._pointerCount=0,this._primaryPointerID=null,this._mouseOverIntervalID=null,this._nextStage=null,this._prevStage=null,this.enableDOMEvents(!0)}var e=createjs.extend(t,createjs.Container);e._get_nextStage=function(){return this._nextStage},e._set_nextStage=function(t){this._nextStage&&(this._nextStage._prevStage=null),t&&(t._prevStage=this),this._nextStage=t};try{Object.defineProperties(e,{nextStage:{get:e._get_nextStage,set:e._set_nextStage}})}catch(t){}e.update=function(t){if(this.canvas&&(this.tickOnUpdate&&this.tick(t),!1!==this.dispatchEvent("drawstart",!1,!0))){createjs.DisplayObject._snapToPixelEnabled=this.snapToPixelEnabled;var e=this.drawRect,i=this.canvas.getContext("2d");i.setTransform(1,0,0,1,0,0),this.autoClear&&(e?i.clearRect(e.x,e.y,e.width,e.height):i.clearRect(0,0,this.canvas.width+1,this.canvas.height+1)),i.save(),this.drawRect&&(i.beginPath(),i.rect(e.x,e.y,e.width,e.height),i.clip()),this.updateContext(i),this.draw(i,!1),i.restore(),this.dispatchEvent("drawend")}},e.tick=function(t){if(this.tickEnabled&&!1!==this.dispatchEvent("tickstart",!1,!0)){var e=new createjs.Event("tick");if(t)for(var i in t)t.hasOwnProperty(i)&&(e[i]=t[i]);this._tick(e),this.dispatchEvent("tickend")}},e.handleEvent=function(t){"tick"==t.type&&this.update(t)},e.clear=function(){if(this.canvas){var t=this.canvas.getContext("2d");t.setTransform(1,0,0,1,0,0),t.clearRect(0,0,this.canvas.width+1,this.canvas.height+1)}},e.toDataURL=function(t,e){var i,r=this.canvas.getContext("2d"),n=this.canvas.width,s=this.canvas.height;if(t){i=r.getImageData(0,0,n,s);var a=r.globalCompositeOperation;r.globalCompositeOperation="destination-over",r.fillStyle=t,r.fillRect(0,0,n,s)}var o=this.canvas.toDataURL(e||"image/png");return t&&(r.putImageData(i,0,0),r.globalCompositeOperation=a),o},e.enableMouseOver=function(t){if(this._mouseOverIntervalID&&(clearInterval(this._mouseOverIntervalID),this._mouseOverIntervalID=null,0==t&&this._testMouseOver(!0)),null==t)t=20;else if(t<=0)return;var e=this;this._mouseOverIntervalID=setInterval((function(){e._testMouseOver()}),1e3/Math.min(50,t))},e.enableDOMEvents=function(t){null==t&&(t=!0);var e,i,r=this._eventListeners;if(!t&&r){for(e in r)(i=r[e]).t.removeEventListener(e,i.f,!1);this._eventListeners=null}else if(t&&!r&&this.canvas){var n=window.addEventListener?window:document,s=this;for(e in(r=this._eventListeners={}).mouseup={t:n,f:function(t){s._handleMouseUp(t)}},r.mousemove={t:n,f:function(t){s._handleMouseMove(t)}},r.dblclick={t:this.canvas,f:function(t){s._handleDoubleClick(t)}},r.mousedown={t:this.canvas,f:function(t){s._handleMouseDown(t)}},r)(i=r[e]).t.addEventListener(e,i.f,!1)}},e.clone=function(){throw"Stage cannot be cloned."},e.toString=function(){return"[Stage (name="+this.name+")]"},e._getElementRect=function(t){var e;try{e=t.getBoundingClientRect()}catch(i){e={top:t.offsetTop,left:t.offsetLeft,width:t.offsetWidth,height:t.offsetHeight}}var i=(window.pageXOffset||document.scrollLeft||0)-(document.clientLeft||document.body.clientLeft||0),r=(window.pageYOffset||document.scrollTop||0)-(document.clientTop||document.body.clientTop||0),n=window.getComputedStyle?getComputedStyle(t,null):t.currentStyle,s=parseInt(n.paddingLeft)+parseInt(n.borderLeftWidth),a=parseInt(n.paddingTop)+parseInt(n.borderTopWidth),o=parseInt(n.paddingRight)+parseInt(n.borderRightWidth),c=parseInt(n.paddingBottom)+parseInt(n.borderBottomWidth);return{left:e.left+i+s,right:e.right+i-o,top:e.top+r+a,bottom:e.bottom+r-c}},e._getPointerData=function(t){var e=this._pointerData[t];return e||(e=this._pointerData[t]={x:0,y:0}),e},e._handleMouseMove=function(t){t||(t=window.event),this._handlePointerMove(-1,t,t.pageX,t.pageY)},e._handlePointerMove=function(t,e,i,r,n){if((!this._prevStage||void 0!==n)&&this.canvas){var s=this._nextStage,a=this._getPointerData(t),o=a.inBounds;this._updatePointerPosition(t,e,i,r),(o||a.inBounds||this.mouseMoveOutside)&&(-1===t&&a.inBounds==!o&&this._dispatchMouseEvent(this,o?"mouseleave":"mouseenter",!1,t,a,e),this._dispatchMouseEvent(this,"stagemousemove",!1,t,a,e),this._dispatchMouseEvent(a.target,"pressmove",!0,t,a,e)),s&&s._handlePointerMove(t,e,i,r,null)}},e._updatePointerPosition=function(t,e,i,r){var n=this._getElementRect(this.canvas);i-=n.left,r-=n.top;var s=this.canvas.width,a=this.canvas.height;i/=(n.right-n.left)/s,r/=(n.bottom-n.top)/a;var o=this._getPointerData(t);(o.inBounds=i>=0&&r>=0&&i<=s-1&&r<=a-1)?(o.x=i,o.y=r):this.mouseMoveOutside&&(o.x=i<0?0:i>s-1?s-1:i,o.y=r<0?0:r>a-1?a-1:r),o.posEvtObj=e,o.rawX=i,o.rawY=r,t!==this._primaryPointerID&&-1!==t||(this.mouseX=o.x,this.mouseY=o.y,this.mouseInBounds=o.inBounds)},e._handleMouseUp=function(t){this._handlePointerUp(-1,t,!1)},e._handlePointerUp=function(t,e,i,r){var n=this._nextStage,s=this._getPointerData(t);if(!this._prevStage||void 0!==r){var a=null,o=s.target;r||!o&&!n||(a=this._getObjectsUnderPoint(s.x,s.y,null,!0)),s.down&&(this._dispatchMouseEvent(this,"stagemouseup",!1,t,s,e,a),s.down=!1),a==o&&this._dispatchMouseEvent(o,"click",!0,t,s,e),this._dispatchMouseEvent(o,"pressup",!0,t,s,e),i?(t==this._primaryPointerID&&(this._primaryPointerID=null),delete this._pointerData[t]):s.target=null,n&&n._handlePointerUp(t,e,i,r||a&&this)}},e._handleMouseDown=function(t){this._handlePointerDown(-1,t,t.pageX,t.pageY)},e._handlePointerDown=function(t,e,i,r,n){this.preventSelection&&e.preventDefault(),null!=this._primaryPointerID&&-1!==t||(this._primaryPointerID=t),null!=r&&this._updatePointerPosition(t,e,i,r);var s=null,a=this._nextStage,o=this._getPointerData(t);n||(s=o.target=this._getObjectsUnderPoint(o.x,o.y,null,!0)),o.inBounds&&(this._dispatchMouseEvent(this,"stagemousedown",!1,t,o,e,s),o.down=!0),this._dispatchMouseEvent(s,"mousedown",!0,t,o,e),a&&a._handlePointerDown(t,e,i,r,n||s&&this)},e._testMouseOver=function(t,e,i){if(!this._prevStage||void 0!==e){var r=this._nextStage;if(this._mouseOverIntervalID){var n=this._getPointerData(-1);if(n&&(t||this.mouseX!=this._mouseOverX||this.mouseY!=this._mouseOverY||!this.mouseInBounds)){var s,a,o,c=n.posEvtObj,u=i||c&&c.target==this.canvas,l=null,h=-1,d="";!e&&(t||this.mouseInBounds&&u)&&(l=this._getObjectsUnderPoint(this.mouseX,this.mouseY,null,!0),this._mouseOverX=this.mouseX,this._mouseOverY=this.mouseY);var f=this._mouseOverTarget||[],p=f[f.length-1],_=this._mouseOverTarget=[];for(s=l;s;)_.unshift(s),d||(d=s.cursor),s=s.parent;for(this.canvas.style.cursor=d,!e&&i&&(i.canvas.style.cursor=d),a=0,o=_.length;a<o&&_[a]==f[a];a++)h=a;for(p!=l&&this._dispatchMouseEvent(p,"mouseout",!0,-1,n,c,l),a=f.length-1;a>h;a--)this._dispatchMouseEvent(f[a],"rollout",!1,-1,n,c,l);for(a=_.length-1;a>h;a--)this._dispatchMouseEvent(_[a],"rollover",!1,-1,n,c,p);p!=l&&this._dispatchMouseEvent(l,"mouseover",!0,-1,n,c,p),r&&r._testMouseOver(t,e||l&&this,i||u&&this)}}else r&&r._testMouseOver(t,e,i)}},e._handleDoubleClick=function(t,e){var i=null,r=this._nextStage,n=this._getPointerData(-1);e||(i=this._getObjectsUnderPoint(n.x,n.y,null,!0),this._dispatchMouseEvent(i,"dblclick",!0,-1,n,t)),r&&r._handleDoubleClick(t,e||i&&this)},e._dispatchMouseEvent=function(t,e,i,r,n,s,a){if(t&&(i||t.hasEventListener(e))){var o=new createjs.MouseEvent(e,i,!1,n.x,n.y,s,r,r===this._primaryPointerID||-1===r,n.rawX,n.rawY,a);t.dispatchEvent(o)}},createjs.Stage=createjs.promote(t,"Container")}(),window.createjs=window.createjs||{},function(){"use strict";function t(e,i){if(this.Stage_constructor(e),void 0!==i){if("object"!=typeof i)throw"Invalid options object";var r=i.premultiply,n=i.transparent,s=i.antialias,a=i.preserveBuffer,o=i.autoPurge}this.vocalDebug=!1,this._preserveBuffer=a||!1,this._antialias=s||!1,this._transparent=n||!1,this._premultiply=r||!1,this._autoPurge=void 0,this.autoPurge=o,this._viewportWidth=0,this._viewportHeight=0,this._projectionMatrix=null,this._webGLContext=null,this._clearColor={r:.5,g:.5,b:.5,a:0},this._maxCardsPerBatch=t.DEFAULT_MAX_BATCH_SIZE,this._activeShader=null,this._vertices=null,this._vertexPositionBuffer=null,this._uvs=null,this._uvPositionBuffer=null,this._indices=null,this._textureIndexBuffer=null,this._alphas=null,this._alphaBuffer=null,this._textureDictionary=[],this._textureIDs={},this._batchTextures=[],this._baseTextures=[],this._batchTextureCount=8,this._lastTextureInsert=-1,this._batchID=0,this._drawID=0,this._slotBlacklist=[],this._isDrawing=0,this._lastTrackedCanvas=0,this.isCacheControlled=!1,this._cacheContainer=new createjs.Container,this._initializeWebGL()}var e=createjs.extend(t,createjs.Stage);t.buildUVRects=function(t,e,i){if(!t||!t._frames)return null;void 0===e&&(e=-1),void 0===i&&(i=!1);for(var r=-1!=e&&i?e:0,n=-1!=e&&i?e+1:t._frames.length,s=r;s<n;s++){var a=t._frames[s];if(!(a.uvRect||a.image.width<=0||a.image.height<=0)){var o=a.rect;a.uvRect={t:o.y/a.image.height,l:o.x/a.image.width,b:(o.y+o.height)/a.image.height,r:(o.x+o.width)/a.image.width}}}return t._frames[-1!=e?e:0].uvRect||{t:0,l:0,b:1,r:1}},t.isWebGLActive=function(t){return t&&t instanceof WebGLRenderingContext&&"undefined"!=typeof WebGLRenderingContext},t.VERTEX_PROPERTY_COUNT=6,t.INDICIES_PER_CARD=6,t.DEFAULT_MAX_BATCH_SIZE=1e4,t.WEBGL_MAX_INDEX_NUM=Math.pow(2,16),t.UV_RECT={t:0,l:0,b:1,r:1};try{t.COVER_VERT=new Float32Array([-1,1,1,1,-1,-1,1,1,1,-1,-1,-1]),t.COVER_UV=new Float32Array([0,0,1,0,0,1,1,0,1,1,0,1]),t.COVER_UV_FLIP=new Float32Array([0,1,1,1,0,0,1,1,1,0,0,0])}catch(t){}t.REGULAR_VARYING_HEADER="precision mediump float;varying vec2 vTextureCoord;varying lowp float indexPicker;varying lowp float alphaValue;",t.REGULAR_VERTEX_HEADER=t.REGULAR_VARYING_HEADER+"attribute vec2 vertexPosition;attribute vec2 uvPosition;attribute lowp float textureIndex;attribute lowp float objectAlpha;uniform mat4 pMatrix;",t.REGULAR_FRAGMENT_HEADER=t.REGULAR_VARYING_HEADER+"uniform sampler2D uSampler[{{count}}];",t.REGULAR_VERTEX_BODY="void main(void) {gl_Position = vec4((vertexPosition.x * pMatrix[0][0]) + pMatrix[3][0],(vertexPosition.y * pMatrix[1][1]) + pMatrix[3][1],pMatrix[3][2],1.0);alphaValue = objectAlpha;indexPicker = textureIndex;vTextureCoord = uvPosition;}",t.REGULAR_FRAGMENT_BODY="void main(void) {vec4 color = vec4(1.0, 0.0, 0.0, 1.0);if (indexPicker <= 0.5) {color = texture2D(uSampler[0], vTextureCoord);{{alternates}}}{{fragColor}}}",t.REGULAR_FRAG_COLOR_NORMAL="gl_FragColor = vec4(color.rgb, color.a * alphaValue);",t.REGULAR_FRAG_COLOR_PREMULTIPLY="if(color.a > 0.0035) {gl_FragColor = vec4(color.rgb/color.a, color.a * alphaValue);} else {gl_FragColor = vec4(0.0, 0.0, 0.0, 0.0);}",t.PARTICLE_VERTEX_BODY=t.REGULAR_VERTEX_BODY,t.PARTICLE_FRAGMENT_BODY=t.REGULAR_FRAGMENT_BODY,t.COVER_VARYING_HEADER="precision mediump float;varying highp vec2 vRenderCoord;varying highp vec2 vTextureCoord;",t.COVER_VERTEX_HEADER=t.COVER_VARYING_HEADER+"attribute vec2 vertexPosition;attribute vec2 uvPosition;uniform float uUpright;",t.COVER_FRAGMENT_HEADER=t.COVER_VARYING_HEADER+"uniform sampler2D uSampler;",t.COVER_VERTEX_BODY="void main(void) {gl_Position = vec4(vertexPosition.x, vertexPosition.y, 0.0, 1.0);vRenderCoord = uvPosition;vTextureCoord = vec2(uvPosition.x, abs(uUpright - uvPosition.y));}",t.COVER_FRAGMENT_BODY="void main(void) {vec4 color = texture2D(uSampler, vRenderCoord);gl_FragColor = color;}",e._get_isWebGL=function(){return!!this._webGLContext},e._set_autoPurge=function(t){-1!=(t=isNaN(t)?1200:t)&&(t=t<10?10:t),this._autoPurge=t},e._get_autoPurge=function(){return Number(this._autoPurge)};try{Object.defineProperties(e,{isWebGL:{get:e._get_isWebGL},autoPurge:{get:e._get_autoPurge,set:e._set_autoPurge}})}catch(t){}e._initializeWebGL=function(){if(this.canvas){if(!this._webGLContext||this._webGLContext.canvas!==this.canvas){var t={depth:!1,alpha:this._transparent,stencil:!0,antialias:this._antialias,premultipliedAlpha:this._premultiply,preserveDrawingBuffer:this._preserveBuffer},e=this._webGLContext=this._fetchWebGLContext(this.canvas,t);if(!e)return null;this.updateSimultaneousTextureCount(e.getParameter(e.MAX_TEXTURE_IMAGE_UNITS)),this._maxTextureSlots=e.getParameter(e.MAX_COMBINED_TEXTURE_IMAGE_UNITS),this._createBuffers(e),this._initTextures(e),e.disable(e.DEPTH_TEST),e.enable(e.BLEND),e.blendFuncSeparate(e.SRC_ALPHA,e.ONE_MINUS_SRC_ALPHA,e.ONE,e.ONE_MINUS_SRC_ALPHA),e.pixelStorei(e.UNPACK_PREMULTIPLY_ALPHA_WEBGL,this._premultiply),this._webGLContext.clearColor(this._clearColor.r,this._clearColor.g,this._clearColor.b,this._clearColor.a),this.updateViewport(this._viewportWidth||this.canvas.width,this._viewportHeight||this.canvas.height)}}else this._webGLContext=null;return this._webGLContext},e.update=function(t){if(this.canvas){if(this.tickOnUpdate&&this.tick(t),this.dispatchEvent("drawstart"),this.autoClear&&this.clear(),this._webGLContext)this._batchDraw(this,this._webGLContext),-1==this._autoPurge||this._drawID%(this._autoPurge/2|0)||this.purgeTextures(this._autoPurge);else{var e=this.canvas.getContext("2d");e.save(),this.updateContext(e),this.draw(e,!1),e.restore()}this.dispatchEvent("drawend")}},e.clear=function(){if(this.canvas)if(t.isWebGLActive(this._webGLContext)){var e=this._webGLContext,i=this._clearColor,r=this._transparent?i.a:1;this._webGLContext.clearColor(i.r*r,i.g*r,i.b*r,r),e.clear(e.COLOR_BUFFER_BIT),this._webGLContext.clearColor(i.r,i.g,i.b,i.a)}else this.Stage_clear()},e.draw=function(e,i){if(e===this._webGLContext&&t.isWebGLActive(this._webGLContext)){var r=this._webGLContext;return this._batchDraw(this,r,i),!0}return this.Stage_draw(e,i)},e.cacheDraw=function(e,i,r){if(t.isWebGLActive(this._webGLContext)){var n=this._webGLContext;return this._cacheDraw(n,e,i,r),!0}return!1},e.protectTextureSlot=function(t,e){if(t>this._maxTextureSlots||t<0)throw"Slot outside of acceptable range";this._slotBlacklist[t]=!!e},e.getTargetRenderTexture=function(t,e,i){var r,n=!1,s=this._webGLContext;if(void 0!==t.__lastRT&&t.__lastRT===t.__rtA&&(n=!0),n?(void 0===t.__rtB?t.__rtB=this.getRenderBufferTexture(e,i):(e==t.__rtB._width&&i==t.__rtB._height||this.resizeTexture(t.__rtB,e,i),this.setTextureParams(s)),r=t.__rtB):(void 0===t.__rtA?t.__rtA=this.getRenderBufferTexture(e,i):(e==t.__rtA._width&&i==t.__rtA._height||this.resizeTexture(t.__rtA,e,i),this.setTextureParams(s)),r=t.__rtA),!r)throw"Problems creating render textures, known causes include using too much VRAM by not releasing WebGL texture instances";return t.__lastRT=r,r},e.releaseTexture=function(t){var e,i;if(t){if(t.children)for(e=0,i=t.children.length;e<i;e++)this.releaseTexture(t.children[e]);t.cacheCanvas&&t.uncache();var r=void 0;if(void 0!==t._storeID){if(t===this._textureDictionary[t._storeID])return this._killTextureObject(t),void(t._storeID=void 0);r=t}else if(2===t._webGLRenderStyle)r=t.image;else if(1===t._webGLRenderStyle){for(e=0,i=t.spriteSheet._images.length;e<i;e++)this.releaseTexture(t.spriteSheet._images[e]);return}void 0!==r?(this._killTextureObject(this._textureDictionary[r._storeID]),r._storeID=void 0):this.vocalDebug&&console.log("No associated texture found on release")}},e.purgeTextures=function(t){null==t&&(t=100);for(var e=this._textureDictionary,i=e.length,r=0;r<i;r++){var n=e[r];n&&(n._drawID+t<=this._drawID&&this._killTextureObject(n))}},e.updateSimultaneousTextureCount=function(t){var e=this._webGLContext,i=!1;for((t<1||isNaN(t))&&(t=1),this._batchTextureCount=t;!i;)try{this._activeShader=this._fetchShaderProgram(e),i=!0}catch(t){if(1==this._batchTextureCount)throw"Cannot compile shader "+t;this._batchTextureCount-=4,this._batchTextureCount<1&&(this._batchTextureCount=1),this.vocalDebug&&console.log("Reducing desired texture count due to errors: "+this._batchTextureCount)}},e.updateViewport=function(t,e){this._viewportWidth=0|t,this._viewportHeight=0|e;var i=this._webGLContext;i&&(i.viewport(0,0,this._viewportWidth,this._viewportHeight),this._projectionMatrix=new Float32Array([2/this._viewportWidth,0,0,0,0,-2/this._viewportHeight,1,0,0,0,1,0,-1,1,.1,0]),this._projectionMatrixFlip=new Float32Array([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]),this._projectionMatrixFlip.set(this._projectionMatrix),this._projectionMatrixFlip[5]*=-1,this._projectionMatrixFlip[13]*=-1)},e.getFilterShader=function(t){t||(t=this);var e=this._webGLContext,i=this._activeShader;if(t._builtShader)i=t._builtShader,t.shaderParamSetup&&(e.useProgram(i),t.shaderParamSetup(e,this,i));else try{i=this._fetchShaderProgram(e,"filter",t.VTX_SHADER_BODY,t.FRAG_SHADER_BODY,t.shaderParamSetup&&t.shaderParamSetup.bind(t)),t._builtShader=i,i._name=t.toString()}catch(t){console&&console.log("SHADER SWITCH FAILURE",t)}return i},e.getBaseTexture=function(t,e){var i=Math.ceil(t>0?t:1)||1,r=Math.ceil(e>0?e:1)||1,n=this._webGLContext,s=n.createTexture();return this.resizeTexture(s,i,r),this.setTextureParams(n,!1),s},e.resizeTexture=function(t,e,i){var r=this._webGLContext;r.bindTexture(r.TEXTURE_2D,t),r.texImage2D(r.TEXTURE_2D,0,r.RGBA,e,i,0,r.RGBA,r.UNSIGNED_BYTE,null),t.width=e,t.height=i},e.getRenderBufferTexture=function(t,e){var i=this._webGLContext,r=this.getBaseTexture(t,e);if(!r)return null;var n=i.createFramebuffer();return n?(r.width=t,r.height=e,i.bindFramebuffer(i.FRAMEBUFFER,n),i.framebufferTexture2D(i.FRAMEBUFFER,i.COLOR_ATTACHMENT0,i.TEXTURE_2D,r,0),n._renderTexture=r,r._frameBuffer=n,r._storeID=this._textureDictionary.length,this._textureDictionary[r._storeID]=r,i.bindFramebuffer(i.FRAMEBUFFER,null),r):null},e.setTextureParams=function(t,e){e&&this._antialias?(t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MIN_FILTER,t.LINEAR),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MAG_FILTER,t.LINEAR)):(t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MIN_FILTER,t.NEAREST),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MAG_FILTER,t.NEAREST)),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_WRAP_S,t.CLAMP_TO_EDGE),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_WRAP_T,t.CLAMP_TO_EDGE)},e.setClearColor=function(t){var e,i,r,n,s;"string"==typeof t?0==t.indexOf("#")?(4==t.length&&(t="#"+t.charAt(1)+t.charAt(1)+t.charAt(2)+t.charAt(2)+t.charAt(3)+t.charAt(3)),e=Number("0x"+t.slice(1,3))/255,i=Number("0x"+t.slice(3,5))/255,r=Number("0x"+t.slice(5,7))/255,n=Number("0x"+t.slice(7,9))/255):0==t.indexOf("rgba(")&&(s=t.slice(5,-1).split(","),e=Number(s[0])/255,i=Number(s[1])/255,r=Number(s[2])/255,n=Number(s[3])):(e=((4278190080&t)>>>24)/255,i=((16711680&t)>>>16)/255,r=((65280&t)>>>8)/255,n=(255&t)/255),this._clearColor.r=e||0,this._clearColor.g=i||0,this._clearColor.b=r||0,this._clearColor.a=n||0,this._webGLContext&&this._webGLContext.clearColor(this._clearColor.r,this._clearColor.g,this._clearColor.b,this._clearColor.a)},e.toString=function(){return"[StageGL (name="+this.name+")]"},e._fetchWebGLContext=function(t,e){var i;try{i=t.getContext("webgl",e)||t.getContext("experimental-webgl",e)}catch(t){}if(i)i.viewportWidth=t.width,i.viewportHeight=t.height;else{var r="Could not initialize WebGL";console.error?console.error(r):console.log(r)}return i},e._fetchShaderProgram=function(e,i,r,n,s){var a,o;switch(e.useProgram(null),i){case"filter":o=t.COVER_VERTEX_HEADER+(r||t.COVER_VERTEX_BODY),a=t.COVER_FRAGMENT_HEADER+(n||t.COVER_FRAGMENT_BODY);break;case"particle":o=t.REGULAR_VERTEX_HEADER+t.PARTICLE_VERTEX_BODY,a=t.REGULAR_FRAGMENT_HEADER+t.PARTICLE_FRAGMENT_BODY;break;case"override":o=t.REGULAR_VERTEX_HEADER+(r||t.REGULAR_VERTEX_BODY),a=t.REGULAR_FRAGMENT_HEADER+(n||t.REGULAR_FRAGMENT_BODY);break;default:o=t.REGULAR_VERTEX_HEADER+t.REGULAR_VERTEX_BODY,a=t.REGULAR_FRAGMENT_HEADER+t.REGULAR_FRAGMENT_BODY}var c=this._createShader(e,e.VERTEX_SHADER,o),u=this._createShader(e,e.FRAGMENT_SHADER,a),l=e.createProgram();if(e.attachShader(l,c),e.attachShader(l,u),e.linkProgram(l),l._type=i,!e.getProgramParameter(l,e.LINK_STATUS))throw e.useProgram(this._activeShader),e.getProgramInfoLog(l);if(e.useProgram(l),"filter"===i)l.vertexPositionAttribute=e.getAttribLocation(l,"vertexPosition"),e.enableVertexAttribArray(l.vertexPositionAttribute),l.uvPositionAttribute=e.getAttribLocation(l,"uvPosition"),e.enableVertexAttribArray(l.uvPositionAttribute),l.samplerUniform=e.getUniformLocation(l,"uSampler"),e.uniform1i(l.samplerUniform,0),l.uprightUniform=e.getUniformLocation(l,"uUpright"),e.uniform1f(l.uprightUniform,0),s&&s(e,this,l);else{l.vertexPositionAttribute=e.getAttribLocation(l,"vertexPosition"),e.enableVertexAttribArray(l.vertexPositionAttribute),l.uvPositionAttribute=e.getAttribLocation(l,"uvPosition"),e.enableVertexAttribArray(l.uvPositionAttribute),l.textureIndexAttribute=e.getAttribLocation(l,"textureIndex"),e.enableVertexAttribArray(l.textureIndexAttribute),l.alphaAttribute=e.getAttribLocation(l,"objectAlpha"),e.enableVertexAttribArray(l.alphaAttribute);for(var h=[],d=0;d<this._batchTextureCount;d++)h[d]=d;l.samplerData=h,l.samplerUniform=e.getUniformLocation(l,"uSampler"),e.uniform1iv(l.samplerUniform,h),l.pMatrixUniform=e.getUniformLocation(l,"pMatrix")}return e.useProgram(this._activeShader),l},e._createShader=function(e,i,r){r=r.replace(/{{count}}/g,this._batchTextureCount);for(var n="",s=1;s<this._batchTextureCount;s++)n+="} else if (indexPicker <= "+s+".5) { color = texture2D(uSampler["+s+"], vTextureCoord);";r=(r=r.replace(/{{alternates}}/g,n)).replace(/{{fragColor}}/g,this._premultiply?t.REGULAR_FRAG_COLOR_PREMULTIPLY:t.REGULAR_FRAG_COLOR_NORMAL);var a=e.createShader(i);if(e.shaderSource(a,r),e.compileShader(a),!e.getShaderParameter(a,e.COMPILE_STATUS))throw e.getShaderInfoLog(a);return a},e._createBuffers=function(e){var i,r,n,s=this._maxCardsPerBatch*t.INDICIES_PER_CARD,a=this._vertexPositionBuffer=e.createBuffer();e.bindBuffer(e.ARRAY_BUFFER,a),i=2;var o=this._vertices=new Float32Array(s*i);for(r=0,n=o.length;r<n;r+=i)o[r]=o[r+1]=0;e.bufferData(e.ARRAY_BUFFER,o,e.DYNAMIC_DRAW),a.itemSize=i,a.numItems=s;var c=this._uvPositionBuffer=e.createBuffer();e.bindBuffer(e.ARRAY_BUFFER,c),i=2;var u=this._uvs=new Float32Array(s*i);for(r=0,n=u.length;r<n;r+=i)u[r]=u[r+1]=0;e.bufferData(e.ARRAY_BUFFER,u,e.DYNAMIC_DRAW),c.itemSize=i,c.numItems=s;var l=this._textureIndexBuffer=e.createBuffer();e.bindBuffer(e.ARRAY_BUFFER,l),i=1;var h=this._indices=new Float32Array(s*i);for(r=0,n=h.length;r<n;r++)h[r]=0;e.bufferData(e.ARRAY_BUFFER,h,e.DYNAMIC_DRAW),l.itemSize=i,l.numItems=s;var d=this._alphaBuffer=e.createBuffer();e.bindBuffer(e.ARRAY_BUFFER,d),i=1;var f=this._alphas=new Float32Array(s*i);for(r=0,n=f.length;r<n;r++)f[r]=1;e.bufferData(e.ARRAY_BUFFER,f,e.DYNAMIC_DRAW),d.itemSize=i,d.numItems=s},e._initTextures=function(){this._lastTextureInsert=-1,this._textureDictionary=[],this._textureIDs={},this._baseTextures=[],this._batchTextures=[];for(var t=0;t<this._batchTextureCount;t++){var e=this.getBaseTexture();if(this._baseTextures[t]=this._batchTextures[t]=e,!e)throw"Problems creating basic textures, known causes include using too much VRAM by not releasing WebGL texture instances"}},e._loadTextureImage=function(t,e){var i=e.src;i||(e._isCanvas=!0,i=e.src="canvas_"+this._lastTrackedCanvas++);var r=this._textureIDs[i];void 0===r&&(r=this._textureIDs[i]=this._textureDictionary.length),void 0===this._textureDictionary[r]&&(this._textureDictionary[r]=this.getBaseTexture());var n=this._textureDictionary[r];if(n)n._batchID=this._batchID,n._storeID=r,n._imageData=e,this._insertTextureInBatch(t,n),e._storeID=r,e.complete||e.naturalWidth||e._isCanvas?this._updateTextureImageData(t,e):e.addEventListener("load",this._updateTextureImageData.bind(this,t,e));else{var s="Problem creating desired texture, known causes include using too much VRAM by not releasing WebGL texture instances";console.error&&console.error(s)||console.log(s),(n=this._baseTextures[0])._batchID=this._batchID,n._storeID=-1,n._imageData=n,this._insertTextureInBatch(t,n)}return n},e._updateTextureImageData=function(t,e){var i=e.width&e.width-1||e.height&e.height-1,r=this._textureDictionary[e._storeID];t.activeTexture(t.TEXTURE0+r._activeIndex),t.bindTexture(t.TEXTURE_2D,r),r.isPOT=!i,this.setTextureParams(t,r.isPOT);try{t.texImage2D(t.TEXTURE_2D,0,t.RGBA,t.RGBA,t.UNSIGNED_BYTE,e)}catch(t){var n="\nAn error has occurred. This is most likely due to security restrictions on WebGL images with local or cross-domain origins";console.error?(console.error(n),console.error(t)):console&&(console.log(n),console.log(t))}e._invalid=!1,r._w=e.width,r._h=e.height,this.vocalDebug&&(i&&console.warn("NPOT(Non Power of Two) Texture: "+e.src),(e.width>t.MAX_TEXTURE_SIZE||e.height>t.MAX_TEXTURE_SIZE)&&console&&console.error("Oversized Texture: "+e.width+"x"+e.height+" vs "+t.MAX_TEXTURE_SIZE+"max"))},e._insertTextureInBatch=function(t,e){if(this._batchTextures[e._activeIndex]!==e){var i=-1,r=(this._lastTextureInsert+1)%this._batchTextureCount,n=r;do{if(this._batchTextures[n]._batchID!=this._batchID&&!this._slotBlacklist[n]){i=n;break}n=(n+1)%this._batchTextureCount}while(n!==r);-1===i&&(this.batchReason="textureOverflow",this._drawBuffers(t),this.batchCardCount=0,i=r),this._batchTextures[i]=e,e._activeIndex=i,(s=e._imageData)&&s._invalid&&void 0!==e._drawID?this._updateTextureImageData(t,s):(t.activeTexture(t.TEXTURE0+i),t.bindTexture(t.TEXTURE_2D,e),this.setTextureParams(t)),this._lastTextureInsert=i}else{var s=e._imageData;null!=e._storeID&&s&&s._invalid&&this._updateTextureImageData(t,s)}e._drawID=this._drawID,e._batchID=this._batchID},e._killTextureObject=function(t){if(t){var e=this._webGLContext;if(void 0!==t._storeID&&t._storeID>=0){for(var i in this._textureDictionary[t._storeID]=void 0,this._textureIDs)this._textureIDs[i]==t._storeID&&delete this._textureIDs[i];t._imageData&&(t._imageData._storeID=void 0),t._imageData=t._storeID=void 0}void 0!==t._activeIndex&&this._batchTextures[t._activeIndex]===t&&(this._batchTextures[t._activeIndex]=this._baseTextures[t._activeIndex]);try{t._frameBuffer&&e.deleteFramebuffer(t._frameBuffer),t._frameBuffer=void 0}catch(t){this.vocalDebug&&console.log(t)}try{e.deleteTexture(t)}catch(t){this.vocalDebug&&console.log(t)}}},e._backupBatchTextures=function(t,e){var i=this._webGLContext;this._backupTextures||(this._backupTextures=[]),void 0===e&&(e=this._backupTextures);for(var r=0;r<this._batchTextureCount;r++)i.activeTexture(i.TEXTURE0+r),t?this._batchTextures[r]=e[r]:(e[r]=this._batchTextures[r],this._batchTextures[r]=this._baseTextures[r]),i.bindTexture(i.TEXTURE_2D,this._batchTextures[r]),this.setTextureParams(i,this._batchTextures[r].isPOT);t&&e===this._backupTextures&&(this._backupTextures=[])},e._batchDraw=function(t,e,i){this._isDrawing>0&&this._drawBuffers(e),this._isDrawing++,this._drawID++,this.batchCardCount=0,this.depth=0,this._appendToBatchGroup(t,e,new createjs.Matrix2D,this.alpha,i),this.batchReason="drawFinish",this._drawBuffers(e),this._isDrawing--},e._cacheDraw=function(t,e,i,r){var n,s=this._activeShader,a=this._slotBlacklist,o=this._maxTextureSlots-1,c=this._viewportWidth,u=this._viewportHeight;this.protectTextureSlot(o,!0);var l=e.getMatrix();(l=l.clone()).scale(1/r.scale,1/r.scale),(l=l.invert()).translate(-r.offX/r.scale*e.scaleX,-r.offY/r.scale*e.scaleY);var h=this._cacheContainer;h.children=[e],h.transformMatrix=l,this._backupBatchTextures(!1),i&&i.length?this._drawFilters(e,i,r):this.isCacheControlled?(t.clear(t.COLOR_BUFFER_BIT),this._batchDraw(h,t,!0)):(t.activeTexture(t.TEXTURE0+o),e.cacheCanvas=this.getTargetRenderTexture(e,r._drawWidth,r._drawHeight),n=e.cacheCanvas,t.bindFramebuffer(t.FRAMEBUFFER,n._frameBuffer),this.updateViewport(r._drawWidth,r._drawHeight),this._projectionMatrix=this._projectionMatrixFlip,t.clear(t.COLOR_BUFFER_BIT),this._batchDraw(h,t,!0),t.bindFramebuffer(t.FRAMEBUFFER,null),this.updateViewport(c,u)),this._backupBatchTextures(!0),this.protectTextureSlot(o,!1),this._activeShader=s,this._slotBlacklist=a},e._drawFilters=function(t,e,i){var r,n=this._webGLContext,s=this._maxTextureSlots-1,a=this._viewportWidth,o=this._viewportHeight,c=this._cacheContainer,u=e.length;n.activeTexture(n.TEXTURE0+s),r=this.getTargetRenderTexture(t,i._drawWidth,i._drawHeight),n.bindFramebuffer(n.FRAMEBUFFER,r._frameBuffer),this.updateViewport(i._drawWidth,i._drawHeight),n.clear(n.COLOR_BUFFER_BIT),this._batchDraw(c,n,!0),n.activeTexture(n.TEXTURE0),n.bindTexture(n.TEXTURE_2D,r),this.setTextureParams(n);var l=!1,h=0,d=e[h];do{this._activeShader=this.getFilterShader(d),this._activeShader&&(n.activeTexture(n.TEXTURE0+s),r=this.getTargetRenderTexture(t,i._drawWidth,i._drawHeight),n.bindFramebuffer(n.FRAMEBUFFER,r._frameBuffer),n.viewport(0,0,i._drawWidth,i._drawHeight),n.clear(n.COLOR_BUFFER_BIT),this._drawCover(n,l),n.activeTexture(n.TEXTURE0),n.bindTexture(n.TEXTURE_2D,r),this.setTextureParams(n),(u>1||e[0]._multiPass)&&(l=!l),d=null!==d._multiPass?d._multiPass:e[++h])}while(d);this.isCacheControlled?(n.bindFramebuffer(n.FRAMEBUFFER,null),this.updateViewport(a,o),this._activeShader=this.getFilterShader(this),n.clear(n.COLOR_BUFFER_BIT),this._drawCover(n,l)):(l&&(n.activeTexture(n.TEXTURE0+s),r=this.getTargetRenderTexture(t,i._drawWidth,i._drawHeight),n.bindFramebuffer(n.FRAMEBUFFER,r._frameBuffer),this._activeShader=this.getFilterShader(this),n.viewport(0,0,i._drawWidth,i._drawHeight),n.clear(n.COLOR_BUFFER_BIT),this._drawCover(n,!l)),n.bindFramebuffer(n.FRAMEBUFFER,null),this.updateViewport(a,o),t.cacheCanvas=r)},e._appendToBatchGroup=function(e,i,r,n,s){e._glMtx||(e._glMtx=new createjs.Matrix2D);var a,o,c,u,l=e._glMtx;l.copy(r),e.transformMatrix?l.appendMatrix(e.transformMatrix):l.appendTransform(e.x,e.y,e.scaleX,e.scaleY,e.rotation,e.skewX,e.skewY,e.regX,e.regY);for(var h=e.children.length,d=0;d<h;d++){var f=e.children[d];if(f.visible&&n)if(f.cacheCanvas&&!s||(f._updateState&&f._updateState(),!f.children)){this.batchCardCount+1>this._maxCardsPerBatch&&(this.batchReason="vertexOverflow",this._drawBuffers(i),this.batchCardCount=0),f._glMtx||(f._glMtx=new createjs.Matrix2D);var p,_,v,m,g,y,b=f._glMtx;b.copy(l),f.transformMatrix?b.appendMatrix(f.transformMatrix):b.appendTransform(f.x,f.y,f.scaleX,f.scaleY,f.rotation,f.skewX,f.skewY,f.regX,f.regY);var w=f.cacheCanvas&&!s;if(2===f._webGLRenderStyle||w)v=!s&&f.cacheCanvas||f.image;else{if(1!==f._webGLRenderStyle)continue;if(null===(m=f.spriteSheet.getFrame(f.currentFrame)))continue;v=m.image}var T=this._uvs,E=this._vertices,S=this._indices,x=this._alphas;if(v){if(void 0===v._storeID)g=this._loadTextureImage(i,v),this._insertTextureInBatch(i,g);else{if(!(g=this._textureDictionary[v._storeID])){this.vocalDebug&&console.log("Texture should not be looked up while not being stored.");continue}g._batchID!==this._batchID&&this._insertTextureInBatch(i,g)}if(_=g._activeIndex,2===f._webGLRenderStyle||w)!w&&f.sourceRect?(f._uvRect||(f._uvRect={}),y=f.sourceRect,(p=f._uvRect).t=y.y/v.height,p.l=y.x/v.width,p.b=(y.y+y.height)/v.height,p.r=(y.x+y.width)/v.width,a=0,o=0,c=y.width+a,u=y.height+o):(p=t.UV_RECT,w?(a=(y=f.bitmapCache).x+y._filterOffX/y.scale,o=y.y+y._filterOffY/y.scale,c=y._drawWidth/y.scale+a,u=y._drawHeight/y.scale+o):(a=0,o=0,c=v.width+a,u=v.height+o));else if(1===f._webGLRenderStyle){var j=m.rect;(p=m.uvRect)||(p=t.buildUVRects(f.spriteSheet,f.currentFrame,!1)),a=-m.regX,o=-m.regY,c=j.width-m.regX,u=j.height-m.regY}var A=this.batchCardCount*t.INDICIES_PER_CARD,L=2*A;E[L]=a*b.a+o*b.c+b.tx,E[L+1]=a*b.b+o*b.d+b.ty,E[L+2]=a*b.a+u*b.c+b.tx,E[L+3]=a*b.b+u*b.d+b.ty,E[L+4]=c*b.a+o*b.c+b.tx,E[L+5]=c*b.b+o*b.d+b.ty,E[L+6]=E[L+2],E[L+7]=E[L+3],E[L+8]=E[L+4],E[L+9]=E[L+5],E[L+10]=c*b.a+u*b.c+b.tx,E[L+11]=c*b.b+u*b.d+b.ty,T[L]=p.l,T[L+1]=p.t,T[L+2]=p.l,T[L+3]=p.b,T[L+4]=p.r,T[L+5]=p.t,T[L+6]=p.l,T[L+7]=p.b,T[L+8]=p.r,T[L+9]=p.t,T[L+10]=p.r,T[L+11]=p.b,S[A]=S[A+1]=S[A+2]=S[A+3]=S[A+4]=S[A+5]=_,x[A]=x[A+1]=x[A+2]=x[A+3]=x[A+4]=x[A+5]=f.alpha*n,this.batchCardCount++}}else this._appendToBatchGroup(f,i,l,f.alpha*n)}},e._drawBuffers=function(e){if(!(this.batchCardCount<=0)){this.vocalDebug&&console.log("Draw["+this._drawID+":"+this._batchID+"] : "+this.batchReason);var i=this._activeShader,r=this._vertexPositionBuffer,n=this._textureIndexBuffer,s=this._uvPositionBuffer,a=this._alphaBuffer;e.useProgram(i),e.bindBuffer(e.ARRAY_BUFFER,r),e.vertexAttribPointer(i.vertexPositionAttribute,r.itemSize,e.FLOAT,!1,0,0),e.bufferSubData(e.ARRAY_BUFFER,0,this._vertices),e.bindBuffer(e.ARRAY_BUFFER,n),e.vertexAttribPointer(i.textureIndexAttribute,n.itemSize,e.FLOAT,!1,0,0),e.bufferSubData(e.ARRAY_BUFFER,0,this._indices),e.bindBuffer(e.ARRAY_BUFFER,s),e.vertexAttribPointer(i.uvPositionAttribute,s.itemSize,e.FLOAT,!1,0,0),e.bufferSubData(e.ARRAY_BUFFER,0,this._uvs),e.bindBuffer(e.ARRAY_BUFFER,a),e.vertexAttribPointer(i.alphaAttribute,a.itemSize,e.FLOAT,!1,0,0),e.bufferSubData(e.ARRAY_BUFFER,0,this._alphas),e.uniformMatrix4fv(i.pMatrixUniform,e.FALSE,this._projectionMatrix);for(var o=0;o<this._batchTextureCount;o++){var c=this._batchTextures[o];e.activeTexture(e.TEXTURE0+o),e.bindTexture(e.TEXTURE_2D,c),this.setTextureParams(e,c.isPOT)}e.drawArrays(e.TRIANGLES,0,this.batchCardCount*t.INDICIES_PER_CARD),this._batchID++}},e._drawCover=function(e,i){this._isDrawing>0&&this._drawBuffers(e),this.vocalDebug&&console.log("Draw["+this._drawID+":"+this._batchID+"] : Cover");var r=this._activeShader,n=this._vertexPositionBuffer,s=this._uvPositionBuffer;e.clear(e.COLOR_BUFFER_BIT),e.useProgram(r),e.bindBuffer(e.ARRAY_BUFFER,n),e.vertexAttribPointer(r.vertexPositionAttribute,n.itemSize,e.FLOAT,!1,0,0),e.bufferSubData(e.ARRAY_BUFFER,0,t.COVER_VERT),e.bindBuffer(e.ARRAY_BUFFER,s),e.vertexAttribPointer(r.uvPositionAttribute,s.itemSize,e.FLOAT,!1,0,0),e.bufferSubData(e.ARRAY_BUFFER,0,i?t.COVER_UV_FLIP:t.COVER_UV),e.uniform1i(r.samplerUniform,0),e.uniform1f(r.uprightUniform,i?0:1),e.drawArrays(e.TRIANGLES,0,t.INDICIES_PER_CARD)},createjs.StageGL=createjs.promote(t,"Stage")}(),window.createjs=window.createjs||{},function(){function t(t){this.DisplayObject_constructor(),"string"==typeof t?(this.image=document.createElement("img"),this.image.src=t):this.image=t,this.sourceRect=null,this._webGLRenderStyle=createjs.DisplayObject._StageGL_BITMAP}var e=createjs.extend(t,createjs.DisplayObject);e.initialize=t,e.isVisible=function(){var t=this.image,e=this.cacheCanvas||t&&(t.naturalWidth||t.getContext||t.readyState>=2);return!!(this.visible&&this.alpha>0&&0!=this.scaleX&&0!=this.scaleY&&e)},e.draw=function(t,e){if(this.DisplayObject_draw(t,e))return!0;var i=this.image,r=this.sourceRect;if(i.getImage&&(i=i.getImage()),!i)return!0;if(r){var n=r.x,s=r.y,a=n+r.width,o=s+r.height,c=0,u=0,l=i.width,h=i.height;n<0&&(c-=n,n=0),a>l&&(a=l),s<0&&(u-=s,s=0),o>h&&(o=h),t.drawImage(i,n,s,a-n,o-s,c,u,a-n,o-s)}else t.drawImage(i,0,0);return!0},e.getBounds=function(){var t=this.DisplayObject_getBounds();if(t)return t;var e=this.image,i=this.sourceRect||e;return e&&(e.naturalWidth||e.getContext||e.readyState>=2)?this._rectangle.setValues(0,0,i.width,i.height):null},e.clone=function(e){var i=this.image;i&&e&&(i=i.cloneNode());var r=new t(i);return this.sourceRect&&(r.sourceRect=this.sourceRect.clone()),this._cloneProps(r),r},e.toString=function(){return"[Bitmap (name="+this.name+")]"},createjs.Bitmap=createjs.promote(t,"DisplayObject")}(),window.createjs=window.createjs||{},function(){"use strict";function t(t,e){this.DisplayObject_constructor(),this.currentFrame=0,this.currentAnimation=null,this.paused=!0,this.spriteSheet=t,this.currentAnimationFrame=0,this.framerate=0,this._animation=null,this._currentFrame=null,this._skipAdvance=!1,this._webGLRenderStyle=createjs.DisplayObject._StageGL_SPRITE,null!=e&&this.gotoAndPlay(e)}var e=createjs.extend(t,createjs.DisplayObject);e.initialize=t,e.isVisible=function(){var t=this.cacheCanvas||this.spriteSheet.complete;return!!(this.visible&&this.alpha>0&&0!=this.scaleX&&0!=this.scaleY&&t)},e.draw=function(t,e){if(this.DisplayObject_draw(t,e))return!0;this._normalizeFrame();var i=this.spriteSheet.getFrame(0|this._currentFrame);if(!i)return!1;var r=i.rect;return r.width&&r.height&&t.drawImage(i.image,r.x,r.y,r.width,r.height,-i.regX,-i.regY,r.width,r.height),!0},e.play=function(){this.paused=!1},e.stop=function(){this.paused=!0},e.gotoAndPlay=function(t){this.paused=!1,this._skipAdvance=!0,this._goto(t)},e.gotoAndStop=function(t){this.paused=!0,this._goto(t)},e.advance=function(t){var e=this.framerate||this.spriteSheet.framerate,i=e&&null!=t?t/(1e3/e):1;this._normalizeFrame(i)},e.getBounds=function(){return this.DisplayObject_getBounds()||this.spriteSheet.getFrameBounds(this.currentFrame,this._rectangle)},e.clone=function(){return this._cloneProps(new t(this.spriteSheet))},e.toString=function(){return"[Sprite (name="+this.name+")]"},e._cloneProps=function(t){return this.DisplayObject__cloneProps(t),t.currentFrame=this.currentFrame,t.currentAnimation=this.currentAnimation,t.paused=this.paused,t.currentAnimationFrame=this.currentAnimationFrame,t.framerate=this.framerate,t._animation=this._animation,t._currentFrame=this._currentFrame,t._skipAdvance=this._skipAdvance,t},e._tick=function(t){this.paused||(this._skipAdvance||this.advance(t&&t.delta),this._skipAdvance=!1),this.DisplayObject__tick(t)},e._normalizeFrame=function(t){t=t||0;var e,i=this._animation,r=this.paused,n=this._currentFrame;if(i){var s=i.speed||1,a=this.currentAnimationFrame;if(a+t*s>=(e=i.frames.length)){var o=i.next;if(this._dispatchAnimationEnd(i,n,r,o,e-1))return;if(o)return this._goto(o,t-(e-a)/s);this.paused=!0,a=i.frames.length-1}else a+=t*s;this.currentAnimationFrame=a,this._currentFrame=i.frames[0|a]}else if((n=this._currentFrame+=t)>=(e=this.spriteSheet.getNumFrames())&&e>0&&!this._dispatchAnimationEnd(i,n,r,e-1)&&(this._currentFrame-=e)>=e)return this._normalizeFrame();n=0|this._currentFrame,this.currentFrame!=n&&(this.currentFrame=n,this.dispatchEvent("change"))},e._dispatchAnimationEnd=function(t,e,i,r,n){var s=t?t.name:null;if(this.hasEventListener("animationend")){var a=new createjs.Event("animationend");a.name=s,a.next=r,this.dispatchEvent(a)}var o=this._animation!=t||this._currentFrame!=e;return o||i||!this.paused||(this.currentAnimationFrame=n,o=!0),o},e._goto=function(t,e){if(this.currentAnimationFrame=0,isNaN(t)){var i=this.spriteSheet.getAnimation(t);i&&(this._animation=i,this.currentAnimation=t,this._normalizeFrame(e))}else this.currentAnimation=this._animation=null,this._currentFrame=t,this._normalizeFrame()},createjs.Sprite=createjs.promote(t,"DisplayObject")}(),window.createjs=window.createjs||{},function(){"use strict";function t(t){this.DisplayObject_constructor(),this.graphics=t||new createjs.Graphics}var e=createjs.extend(t,createjs.DisplayObject);e.isVisible=function(){var t=this.cacheCanvas||this.graphics&&!this.graphics.isEmpty();return!!(this.visible&&this.alpha>0&&0!=this.scaleX&&0!=this.scaleY&&t)},e.draw=function(t,e){return this.DisplayObject_draw(t,e)||this.graphics.draw(t,this),!0},e.clone=function(e){var i=e&&this.graphics?this.graphics.clone():this.graphics;return this._cloneProps(new t(i))},e.toString=function(){return"[Shape (name="+this.name+")]"},createjs.Shape=createjs.promote(t,"DisplayObject")}(),window.createjs=window.createjs||{},function(){"use strict";function t(t,e,i){this.DisplayObject_constructor(),this.text=t,this.font=e,this.color=i,this.textAlign="left",this.textBaseline="top",this.maxWidth=null,this.outline=0,this.lineHeight=0,this.lineWidth=null}var e=createjs.extend(t,createjs.DisplayObject),i=createjs.createCanvas?createjs.createCanvas():document.createElement("canvas");i.getContext&&(t._workingContext=i.getContext("2d"),i.width=i.height=1),t.H_OFFSETS={start:0,left:0,center:-.5,end:-1,right:-1},t.V_OFFSETS={top:0,hanging:-.01,middle:-.4,alphabetic:-.8,ideographic:-.85,bottom:-1},e.isVisible=function(){var t=this.cacheCanvas||null!=this.text&&""!==this.text;return!!(this.visible&&this.alpha>0&&0!=this.scaleX&&0!=this.scaleY&&t)},e.draw=function(t,e){if(this.DisplayObject_draw(t,e))return!0;var i=this.color||"#000";return this.outline?(t.strokeStyle=i,t.lineWidth=1*this.outline):t.fillStyle=i,this._drawText(this._prepContext(t)),!0},e.getMeasuredWidth=function(){return this._getMeasuredWidth(this.text)},e.getMeasuredLineHeight=function(){return 1.2*this._getMeasuredWidth("M")},e.getMeasuredHeight=function(){return this._drawText(null,{}).height},e.getBounds=function(){var e=this.DisplayObject_getBounds();if(e)return e;if(null==this.text||""===this.text)return null;var i=this._drawText(null,{}),r=this.maxWidth&&this.maxWidth<i.width?this.maxWidth:i.width,n=r*t.H_OFFSETS[this.textAlign||"left"],s=(this.lineHeight||this.getMeasuredLineHeight())*t.V_OFFSETS[this.textBaseline||"top"];return this._rectangle.setValues(n,s,r,i.height)},e.getMetrics=function(){var e={lines:[]};return e.lineHeight=this.lineHeight||this.getMeasuredLineHeight(),e.vOffset=e.lineHeight*t.V_OFFSETS[this.textBaseline||"top"],this._drawText(null,e,e.lines)},e.clone=function(){return this._cloneProps(new t(this.text,this.font,this.color))},e.toString=function(){return"[Text (text="+(this.text.length>20?this.text.substr(0,17)+"...":this.text)+")]"},e._cloneProps=function(t){return this.DisplayObject__cloneProps(t),t.textAlign=this.textAlign,t.textBaseline=this.textBaseline,t.maxWidth=this.maxWidth,t.outline=this.outline,t.lineHeight=this.lineHeight,t.lineWidth=this.lineWidth,t},e._prepContext=function(t){return t.font=this.font||"10px sans-serif",t.textAlign=this.textAlign||"left",t.textBaseline=this.textBaseline||"top",t.lineJoin="miter",t.miterLimit=2.5,t},e._drawText=function(e,i,r){var n=!!e;n||((e=t._workingContext).save(),this._prepContext(e));for(var s=this.lineHeight||this.getMeasuredLineHeight(),a=0,o=0,c=String(this.text).split(/(?:\r\n|\r|\n)/),u=0,l=c.length;u<l;u++){var h=c[u],d=null;if(null!=this.lineWidth&&(d=e.measureText(h).width)>this.lineWidth){var f=h.split(/(\s)/);h=f[0],d=e.measureText(h).width;for(var p=1,_=f.length;p<_;p+=2){var v=e.measureText(f[p]+f[p+1]).width;d+v>this.lineWidth?(n&&this._drawTextLine(e,h,o*s),r&&r.push(h),d>a&&(a=d),h=f[p+1],d=e.measureText(h).width,o++):(h+=f[p]+f[p+1],d+=v)}}n&&this._drawTextLine(e,h,o*s),r&&r.push(h),i&&null==d&&(d=e.measureText(h).width),d>a&&(a=d),o++}return i&&(i.width=a,i.height=o*s),n||e.restore(),i},e._drawTextLine=function(t,e,i){this.outline?t.strokeText(e,0,i,this.maxWidth||65535):t.fillText(e,0,i,this.maxWidth||65535)},e._getMeasuredWidth=function(e){var i=t._workingContext;i.save();var r=this._prepContext(i).measureText(e).width;return i.restore(),r},createjs.Text=createjs.promote(t,"DisplayObject")}(),window.createjs=window.createjs||{},function(){"use strict";function t(t,e){this.Container_constructor(),this.text=t||"",this.spriteSheet=e,this.lineHeight=0,this.letterSpacing=0,this.spaceWidth=0,this._oldProps={text:0,spriteSheet:0,lineHeight:0,letterSpacing:0,spaceWidth:0},this._oldStage=null,this._drawAction=null}var e=createjs.extend(t,createjs.Container);t.maxPoolSize=100,t._spritePool=[],e.draw=function(t,e){this.DisplayObject_draw(t,e)||(this._updateState(),this.Container_draw(t,e))},e.getBounds=function(){return this._updateText(),this.Container_getBounds()},e.isVisible=function(){var t=this.cacheCanvas||this.spriteSheet&&this.spriteSheet.complete&&this.text;return!!(this.visible&&this.alpha>0&&0!==this.scaleX&&0!==this.scaleY&&t)},e.clone=function(){return this._cloneProps(new t(this.text,this.spriteSheet))},e.addChild=e.addChildAt=e.removeChild=e.removeChildAt=e.removeAllChildren=function(){},e._updateState=function(){this._updateText()},e._cloneProps=function(t){return this.Container__cloneProps(t),t.lineHeight=this.lineHeight,t.letterSpacing=this.letterSpacing,t.spaceWidth=this.spaceWidth,t},e._getFrameIndex=function(t,e){var i,r=e.getAnimation(t);return r||(t!=(i=t.toUpperCase())||t!=(i=t.toLowerCase())||(i=null),i&&(r=e.getAnimation(i))),r&&r.frames[0]},e._getFrame=function(t,e){var i=this._getFrameIndex(t,e);return null==i?i:e.getFrame(i)},e._getLineHeight=function(t){var e=this._getFrame("1",t)||this._getFrame("T",t)||this._getFrame("L",t)||t.getFrame(0);return e?e.rect.height:1},e._getSpaceWidth=function(t){var e=this._getFrame("1",t)||this._getFrame("l",t)||this._getFrame("e",t)||this._getFrame("a",t)||t.getFrame(0);return e?e.rect.width:1},e._updateText=function(){var e,i=0,r=0,n=this._oldProps,s=!1,a=this.spaceWidth,o=this.lineHeight,c=this.spriteSheet,u=t._spritePool,l=this.children,h=0,d=l.length;for(var f in n)n[f]!=this[f]&&(n[f]=this[f],s=!0);if(s){var p=!!this._getFrame(" ",c);p||a||(a=this._getSpaceWidth(c)),o||(o=this._getLineHeight(c));for(var _=0,v=this.text.length;_<v;_++){var m=this.text.charAt(_);if(" "!=m||p)if("\n"!=m&&"\r"!=m){var g=this._getFrameIndex(m,c);null!=g&&(h<d?e=l[h]:(l.push(e=u.length?u.pop():new createjs.Sprite),e.parent=this,d++),e.spriteSheet=c,e.gotoAndStop(g),e.x=i,e.y=r,h++,i+=e.getBounds().width+this.letterSpacing)}else"\r"==m&&"\n"==this.text.charAt(_+1)&&_++,i=0,r+=o;else i+=a}for(;d>h;)u.push(e=l.pop()),e.parent=null,d--;u.length>t.maxPoolSize&&(u.length=t.maxPoolSize)}},createjs.BitmapText=createjs.promote(t,"Container")}(),window.createjs=window.createjs||{},function(){"use strict";function t(e){var i,r,n,s;this.Container_constructor(),!t.inited&&t.init(),e instanceof String||arguments.length>1?(i=e,r=arguments[1],s=arguments[3],null==(n=arguments[2])&&(n=-1),e=null):e&&(i=e.mode,r=e.startPosition,n=e.loop,s=e.labels),e||(e={labels:s}),this.mode=i||t.INDEPENDENT,this.startPosition=r||0,this.loop=!0===n?-1:n||0,this.currentFrame=0,this.paused=e.paused||!1,this.actionsEnabled=!0,this.autoReset=!0,this.frameBounds=this.frameBounds||e.frameBounds,this.framerate=null,e.useTicks=e.paused=!0,this.timeline=new createjs.Timeline(e),this._synchOffset=0,this._rawPosition=-1,this._bound_resolveState=this._resolveState.bind(this),this._t=0,this._managed={}}var e=createjs.extend(t,createjs.Container);t.INDEPENDENT="independent",t.SINGLE_FRAME="single",t.SYNCHED="synched",t.inited=!1,t.init=function(){t.inited||(i.install(),t.inited=!0)},e._getLabels=function(){return this.timeline.getLabels()},e.getLabels=createjs.deprecate(e._getLabels,"MovieClip.getLabels"),e._getCurrentLabel=function(){return this.timeline.currentLabel},e.getCurrentLabel=createjs.deprecate(e._getCurrentLabel,"MovieClip.getCurrentLabel"),e._getDuration=function(){return this.timeline.duration},e.getDuration=createjs.deprecate(e._getDuration,"MovieClip.getDuration");try{Object.defineProperties(e,{labels:{get:e._getLabels},currentLabel:{get:e._getCurrentLabel},totalFrames:{get:e._getDuration},duration:{get:e._getDuration}})}catch(t){}function i(){throw"MovieClipPlugin cannot be instantiated."}e.initialize=t,e.isVisible=function(){return!!(this.visible&&this.alpha>0&&0!=this.scaleX&&0!=this.scaleY)},e.draw=function(t,e){return this.DisplayObject_draw(t,e)||(this._updateState(),this.Container_draw(t,e)),!0},e.play=function(){this.paused=!1},e.stop=function(){this.paused=!0},e.gotoAndPlay=function(t){this.paused=!1,this._goto(t)},e.gotoAndStop=function(t){this.paused=!0,this._goto(t)},e.advance=function(e){var i=t.INDEPENDENT;if(this.mode===i){for(var r=this,n=r.framerate;(r=r.parent)&&null===n;)r.mode===i&&(n=r._framerate);if(this._framerate=n,!this.paused){var s=null!==n&&-1!==n&&null!==e?e/(1e3/n)+this._t:1,a=0|s;for(this._t=s-a;a--;)this._updateTimeline(this._rawPosition+1,!1)}}},e.clone=function(){throw"MovieClip cannot be cloned."},e.toString=function(){return"[MovieClip (name="+this.name+")]"},e._updateState=function(){-1!==this._rawPosition&&this.mode===t.INDEPENDENT||this._updateTimeline(-1)},e._tick=function(t){this.advance(t&&t.delta),this.Container__tick(t)},e._goto=function(t){var e=this.timeline.resolve(t);null!=e&&(this._t=0,this._updateTimeline(e,!0))},e._reset=function(){this._rawPosition=-1,this._t=this.currentFrame=0,this.paused=!1},e._updateTimeline=function(e,i){var r=this.mode!==t.INDEPENDENT,n=this.timeline;r&&(e=this.startPosition+(this.mode===t.SINGLE_FRAME?0:this._synchOffset)),e<0&&(e=0),(this._rawPosition!==e||r)&&(this._rawPosition=e,n.loop=this.loop,n.setPosition(e,r||!this.actionsEnabled,i,this._bound_resolveState))},e._renderFirstFrame=function(){var t=this.timeline,e=t.rawPosition;t.setPosition(0,!0,!0,this._bound_resolveState),t.rawPosition=e},e._resolveState=function(){var t=this.timeline;for(var e in this.currentFrame=t.position,this._managed)this._managed[e]=1;for(var i=t.tweens,r=0,n=i.length;r<n;r++){var s=i[r],a=s.target;if(a!==this&&!s.passive){var o=s._stepPosition;a instanceof createjs.DisplayObject?this._addManagedChild(a,o):this._setState(a.state,o)}}var c=this.children;for(r=c.length-1;r>=0;r--){var u=c[r].id;1===this._managed[u]&&(this.removeChildAt(r),delete this._managed[u])}},e._setState=function(t,e){if(t)for(var i=t.length-1;i>=0;i--){var r=t[i],n=r.t,s=r.p;for(var a in s)n[a]=s[a];this._addManagedChild(n,e)}},e._addManagedChild=function(e,i){e._off||(this.addChildAt(e,0),e instanceof t&&(e._synchOffset=i,e.mode===t.INDEPENDENT&&e.autoReset&&!this._managed[e.id]&&e._reset()),this._managed[e.id]=2)},e._getBounds=function(t,e){var i=this.DisplayObject_getBounds();return i||this.frameBounds&&(i=this._rectangle.copy(this.frameBounds[this.currentFrame])),i?this._transformBounds(i,t,e):this.Container__getBounds(t,e)},createjs.MovieClip=createjs.promote(t,"Container"),i.priority=100,i.ID="MovieClip",i.install=function(){createjs.Tween._installPlugin(i)},i.init=function(e,r,n){"startPosition"===r&&e.target instanceof t&&e._addPlugin(i)},i.step=function(t,e,i){},i.change=function(t,e,i,r,n,s){if("startPosition"===i)return 1===n?e.props[i]:e.prev.props[i]}}(),window.createjs=window.createjs||{},function(){"use strict";function t(){throw"SpriteSheetUtils cannot be instantiated"}var e=createjs.createCanvas?createjs.createCanvas():document.createElement("canvas");e.getContext&&(t._workingCanvas=e,t._workingContext=e.getContext("2d"),e.width=e.height=1),t.extractFrame=function(e,i){isNaN(i)&&(i=e.getAnimation(i).frames[0]);var r=e.getFrame(i);if(!r)return null;var n=r.rect,s=t._workingCanvas;s.width=n.width,s.height=n.height,t._workingContext.drawImage(r.image,n.x,n.y,n.width,n.height,0,0,n.width,n.height);var a=document.createElement("img");return a.src=s.toDataURL("image/png"),a},t.addFlippedFrames=createjs.deprecate(null,"SpriteSheetUtils.addFlippedFrames"),t.mergeAlpha=createjs.deprecate(null,"SpriteSheetUtils.mergeAlpha"),t._flip=function(e,i,r,n){for(var s=e._images,a=t._workingCanvas,o=t._workingContext,c=s.length/i,u=0;u<c;u++){var l=s[u];l.__tmp=u,o.setTransform(1,0,0,1,0,0),o.clearRect(0,0,a.width+1,a.height+1),a.width=l.width,a.height=l.height,o.setTransform(r?-1:1,0,0,n?-1:1,r?l.width:0,n?l.height:0),o.drawImage(l,0,0);var h=document.createElement("img");h.src=a.toDataURL("image/png"),h.width=l.width||l.naturalWidth,h.height=l.height||l.naturalHeight,s.push(h)}var d=e._frames,f=d.length/i;for(u=0;u<f;u++){var p=(l=d[u]).rect.clone(),_={image:h=s[l.image.__tmp+c*i],rect:p,regX:l.regX,regY:l.regY};r&&(p.x=(h.width||h.naturalWidth)-p.x-p.width,_.regX=p.width-l.regX),n&&(p.y=(h.height||h.naturalHeight)-p.y-p.height,_.regY=p.height-l.regY),d.push(_)}var v="_"+(r?"h":"")+(n?"v":""),m=e._animations,g=e._data,y=m.length/i;for(u=0;u<y;u++){var b=m[u],w={name:b+v,speed:(l=g[b]).speed,next:l.next,frames:[]};l.next&&(w.next+=v);for(var T=0,E=(d=l.frames).length;T<E;T++)w.frames.push(d[T]+f*i);g[w.name]=w,m.push(w.name)}},createjs.SpriteSheetUtils=t}(),window.createjs=window.createjs||{},function(){"use strict";function t(t){this.EventDispatcher_constructor(),this.maxWidth=2048,this.maxHeight=2048,this.spriteSheet=null,this.scale=1,this.padding=1,this.timeSlice=.3,this.progress=-1,this.framerate=t||0,this._frames=[],this._animations={},this._data=null,this._nextFrameIndex=0,this._index=0,this._timerID=null,this._scale=1}var e=createjs.extend(t,createjs.EventDispatcher);t.ERR_DIMENSIONS="frame dimensions exceed max spritesheet dimensions",t.ERR_RUNNING="a build is already running",e.addFrame=function(e,i,r,n,s){if(this._data)throw t.ERR_RUNNING;var a=i||e.bounds||e.nominalBounds;return!a&&e.getBounds&&(a=e.getBounds()),a?(r=r||1,this._frames.push({source:e,sourceRect:a,scale:r,funct:n,data:s,index:this._frames.length,height:a.height*r})-1):null},e.addAnimation=function(e,i,r,n){if(this._data)throw t.ERR_RUNNING;this._animations[e]={frames:i,next:r,speed:n}},e.addMovieClip=function(e,i,r,n,s,a){if(this._data)throw t.ERR_RUNNING;var o=e.frameBounds,c=i||e.bounds||e.nominalBounds;if(!c&&e.getBounds&&(c=e.getBounds()),c||o){var u,l,h=this._frames.length,d=e.timeline.duration;for(u=0;u<d;u++){var f=o&&o[u]?o[u]:c;this.addFrame(e,f,r,this._setupMovieClipFrame,{i:u,f:n,d:s})}var p=e.timeline._labels,_=[];for(var v in p)_.push({index:p[v],label:v});if(_.length)for(_.sort((function(t,e){return t.index-e.index})),u=0,l=_.length;u<l;u++){for(var m=_[u].label,g=h+_[u].index,y=h+(u==l-1?d:_[u+1].index),b=[],w=g;w<y;w++)b.push(w);a&&!(m=a(m,e,g,y))||this.addAnimation(m,b,!0)}}},e.build=function(){if(this._data)throw t.ERR_RUNNING;for(this._startBuild();this._drawNext(););return this._endBuild(),this.spriteSheet},e.buildAsync=function(e){if(this._data)throw t.ERR_RUNNING;this.timeSlice=e,this._startBuild();var i=this;this._timerID=setTimeout((function(){i._run()}),50-50*Math.max(.01,Math.min(.99,this.timeSlice||.3)))},e.stopAsync=function(){clearTimeout(this._timerID),this._data=null},e.clone=function(){throw"SpriteSheetBuilder cannot be cloned."},e.toString=function(){return"[SpriteSheetBuilder]"},e._startBuild=function(){var e=this.padding||0;this.progress=0,this.spriteSheet=null,this._index=0,this._scale=this.scale;var i=[];this._data={images:[],frames:i,framerate:this.framerate,animations:this._animations};var r=this._frames.slice();if(r.sort((function(t,e){return t.height<=e.height?-1:1})),r[r.length-1].height+2*e>this.maxHeight)throw t.ERR_DIMENSIONS;for(var n=0,s=0,a=0;r.length;){var o=this._fillRow(r,n,a,i,e);if(o.w>s&&(s=o.w),n+=o.h,!o.h||!r.length){var c=createjs.createCanvas?createjs.createCanvas():document.createElement("canvas");c.width=this._getSize(s,this.maxWidth),c.height=this._getSize(n,this.maxHeight),this._data.images[a]=c,o.h||(s=n=0,a++)}}},e._setupMovieClipFrame=function(t,e){var i=t.actionsEnabled;t.actionsEnabled=!1,t.gotoAndStop(e.i),t.actionsEnabled=i,e.f&&e.f(t,e.d,e.i)},e._getSize=function(t,e){for(var i=4;Math.pow(2,++i)<t;);return Math.min(e,Math.pow(2,i))},e._fillRow=function(e,i,r,n,s){for(var a=this.maxWidth,o=this.maxHeight-(i+=s),c=s,u=0,l=e.length-1;l>=0;l--){var h=e[l],d=this._scale*h.scale,f=h.sourceRect,p=h.source,_=Math.floor(d*f.x-s),v=Math.floor(d*f.y-s),m=Math.ceil(d*f.height+2*s),g=Math.ceil(d*f.width+2*s);if(g>a)throw t.ERR_DIMENSIONS;m>o||c+g>a||(h.img=r,h.rect=new createjs.Rectangle(c,i,g,m),u=u||m,e.splice(l,1),n[h.index]=[c,i,g,m,r,Math.round(-_+d*p.regX-s),Math.round(-v+d*p.regY-s)],c+=g)}return{w:c,h:u}},e._endBuild=function(){this.spriteSheet=new createjs.SpriteSheet(this._data),this._data=null,this.progress=1,this.dispatchEvent("complete")},e._run=function(){for(var t=50*Math.max(.01,Math.min(.99,this.timeSlice||.3)),e=(new Date).getTime()+t,i=!1;e>(new Date).getTime();)if(!this._drawNext()){i=!0;break}if(i)this._endBuild();else{var r=this;this._timerID=setTimeout((function(){r._run()}),50-t)}var n=this.progress=this._index/this._frames.length;if(this.hasEventListener("progress")){var s=new createjs.Event("progress");s.progress=n,this.dispatchEvent(s)}},e._drawNext=function(){var t=this._frames[this._index],e=t.scale*this._scale,i=t.rect,r=t.sourceRect,n=this._data.images[t.img].getContext("2d");return t.funct&&t.funct(t.source,t.data),n.save(),n.beginPath(),n.rect(i.x,i.y,i.width,i.height),n.clip(),n.translate(Math.ceil(i.x-r.x*e),Math.ceil(i.y-r.y*e)),n.scale(e,e),t.source.draw(n),n.restore(),++this._index<this._frames.length},createjs.SpriteSheetBuilder=createjs.promote(t,"EventDispatcher")}(),window.createjs=window.createjs||{},function(){"use strict";function t(t){this.DisplayObject_constructor(),"string"==typeof t&&(t=document.getElementById(t)),this.mouseEnabled=!1;var e=t.style;e.position="absolute",e.transformOrigin=e.WebkitTransformOrigin=e.msTransformOrigin=e.MozTransformOrigin=e.OTransformOrigin="0% 0%",this.htmlElement=t,this._oldProps=null,this._oldStage=null,this._drawAction=null}var e=createjs.extend(t,createjs.DisplayObject);e.isVisible=function(){return null!=this.htmlElement},e.draw=function(t,e){return!0},e.cache=function(){},e.uncache=function(){},e.updateCache=function(){},e.hitTest=function(){},e.localToGlobal=function(){},e.globalToLocal=function(){},e.localToLocal=function(){},e.clone=function(){throw"DOMElement cannot be cloned."},e.toString=function(){return"[DOMElement (name="+this.name+")]"},e._tick=function(t){var e=this.stage;e&&e!==this._oldStage&&(this._drawAction&&e.off("drawend",this._drawAction),this._drawAction=e.on("drawend",this._handleDrawEnd,this),this._oldStage=e),this.DisplayObject__tick(t)},e._handleDrawEnd=function(t){var e=this.htmlElement;if(e){var i=e.style,r=this.getConcatenatedDisplayProps(this._props),n=r.matrix,s=r.visible?"visible":"hidden";if(s!=i.visibility&&(i.visibility=s),r.visible){var a=this._oldProps,o=a&&a.matrix,c=1e4;if(!o||!o.equals(n)){var u="matrix("+(n.a*c|0)/c+","+(n.b*c|0)/c+","+(n.c*c|0)/c+","+(n.d*c|0)/c+","+(n.tx+.5|0);i.transform=i.WebkitTransform=i.OTransform=i.msTransform=u+","+(n.ty+.5|0)+")",i.MozTransform=u+"px,"+(n.ty+.5|0)+"px)",a||(a=this._oldProps=new createjs.DisplayProps(!0,null)),a.matrix.copy(n)}a.alpha!=r.alpha&&(i.opacity=""+(r.alpha*c|0)/c,a.alpha=r.alpha)}}},createjs.DOMElement=createjs.promote(t,"DisplayObject")}(),window.createjs=window.createjs||{},function(){"use strict";function t(){this.usesContext=!1,this._multiPass=null,this.VTX_SHADER_BODY=null,this.FRAG_SHADER_BODY=null}var e=t.prototype;e.getBounds=function(t){return t},e.shaderParamSetup=function(t,e,i){},e.applyFilter=function(t,e,i,r,n,s,a,o){s=s||t,null==a&&(a=e),null==o&&(o=i);try{var c=t.getImageData(e,i,r,n)}catch(t){return!1}return!!this._applyFilter(c)&&(s.putImageData(c,a,o),!0)},e.toString=function(){return"[Filter]"},e.clone=function(){return new t},e._applyFilter=function(t){return!0},createjs.Filter=t}(),window.createjs=window.createjs||{},function(){"use strict";function t(){this.width=void 0,this.height=void 0,this.x=void 0,this.y=void 0,this.scale=1,this.offX=0,this.offY=0,this.cacheID=0,this._filterOffX=0,this._filterOffY=0,this._cacheDataURLID=0,this._cacheDataURL=null,this._drawWidth=0,this._drawHeight=0}var e=t.prototype;t.getFilterBounds=function(t,e){e||(e=new createjs.Rectangle);var i=t.filters,r=i&&i.length;if(!!r<=0)return e;for(var n=0;n<r;n++){var s=i[n];if(s&&s.getBounds){var a=s.getBounds();a&&(0==n?e.setValues(a.x,a.y,a.width,a.height):e.extend(a.x,a.y,a.width,a.height))}}return e},e.toString=function(){return"[BitmapCache]"},e.define=function(t,e,i,r,n,s,a){if(!t)throw"No symbol to cache";this._options=a,this.target=t,this.width=r>=1?r:1,this.height=n>=1?n:1,this.x=e||0,this.y=i||0,this.scale=s||1,this.update()},e.update=function(e){if(!this.target)throw"define() must be called before update()";var i=t.getFilterBounds(this.target),r=this.target.cacheCanvas;this._drawWidth=Math.ceil(this.width*this.scale)+i.width,this._drawHeight=Math.ceil(this.height*this.scale)+i.height,r&&this._drawWidth==r.width&&this._drawHeight==r.height||this._updateSurface(),this._filterOffX=i.x,this._filterOffY=i.y,this.offX=this.x*this.scale+this._filterOffX,this.offY=this.y*this.scale+this._filterOffY,this._drawToCache(e),this.cacheID=this.cacheID?this.cacheID+1:1},e.release=function(){if(this._webGLCache)this._webGLCache.isCacheControlled||(this.__lastRT&&(this.__lastRT=void 0),this.__rtA&&this._webGLCache._killTextureObject(this.__rtA),this.__rtB&&this._webGLCache._killTextureObject(this.__rtB),this.target&&this.target.cacheCanvas&&this._webGLCache._killTextureObject(this.target.cacheCanvas)),this._webGLCache=!1;else{var t=this.target.stage;t instanceof createjs.StageGL&&t.releaseTexture(this.target.cacheCanvas)}this.target=this.target.cacheCanvas=null,this.cacheID=this._cacheDataURLID=this._cacheDataURL=void 0,this.width=this.height=this.x=this.y=this.offX=this.offY=0,this.scale=1},e.getCacheDataURL=function(){var t=this.target&&this.target.cacheCanvas;return t?(this.cacheID!=this._cacheDataURLID&&(this._cacheDataURLID=this.cacheID,this._cacheDataURL=t.toDataURL?t.toDataURL():null),this._cacheDataURL):null},e.draw=function(t){return!!this.target&&(t.drawImage(this.target.cacheCanvas,this.x+this._filterOffX/this.scale,this.y+this._filterOffY/this.scale,this._drawWidth/this.scale,this._drawHeight/this.scale),!0)},e._updateSurface=function(){if(!this._options||!this._options.useGL)return(e=this.target.cacheCanvas)||(e=this.target.cacheCanvas=createjs.createCanvas?createjs.createCanvas():document.createElement("canvas")),e.width=this._drawWidth,void(e.height=this._drawHeight);if(!this._webGLCache)if("stage"===this._options.useGL){if(!this.target.stage||!this.target.stage.isWebGL){var t="Cannot use 'stage' for cache because the object's parent stage is ";throw t+=this.target.stage?"non WebGL.":"not set, please addChild to the correct stage."}this.target.cacheCanvas=!0,this._webGLCache=this.target.stage}else if("new"===this._options.useGL)this.target.cacheCanvas=document.createElement("canvas"),this._webGLCache=new createjs.StageGL(this.target.cacheCanvas,{antialias:!0,transparent:!0,autoPurge:-1}),this._webGLCache.isCacheControlled=!0;else{if(!(this._options.useGL instanceof createjs.StageGL))throw"Invalid option provided to useGL, expected ['stage', 'new', StageGL, undefined], got "+this._options.useGL;this.target.cacheCanvas=!0,this._webGLCache=this._options.useGL,this._webGLCache.isCacheControlled=!0}var e=this.target.cacheCanvas,i=this._webGLCache;i.isCacheControlled&&(e.width=this._drawWidth,e.height=this._drawHeight,i.updateViewport(this._drawWidth,this._drawHeight)),this.target.filters?(i.getTargetRenderTexture(this.target,this._drawWidth,this._drawHeight),i.getTargetRenderTexture(this.target,this._drawWidth,this._drawHeight)):i.isCacheControlled||i.getTargetRenderTexture(this.target,this._drawWidth,this._drawHeight)},e._drawToCache=function(t){var e=this.target.cacheCanvas,i=this.target,r=this._webGLCache;if(r)r.cacheDraw(i,i.filters,this),(e=this.target.cacheCanvas).width=this._drawWidth,e.height=this._drawHeight;else{var n=e.getContext("2d");t||n.clearRect(0,0,this._drawWidth+1,this._drawHeight+1),n.save(),n.globalCompositeOperation=t,n.setTransform(this.scale,0,0,this.scale,-this._filterOffX,-this._filterOffY),n.translate(-this.x,-this.y),i.draw(n,!0),n.restore(),i.filters&&i.filters.length&&this._applyFilters(n)}e._invalid=!0},e._applyFilters=function(t){var e,i=this.target.filters,r=this._drawWidth,n=this._drawHeight,s=0,a=i[s];do{a.usesContext?(e&&(t.putImageData(e,0,0),e=null),a.applyFilter(t,0,0,r,n)):(e||(e=t.getImageData(0,0,r,n)),a._applyFilter(e)),a=null!==a._multiPass?a._multiPass:i[++s]}while(a);e&&t.putImageData(e,0,0)},createjs.BitmapCache=t}(),window.createjs=window.createjs||{},function(){"use strict";function t(t,e,i){this.Filter_constructor(),this._blurX=t,this._blurXTable=[],this._lastBlurX=null,this._blurY=e,this._blurYTable=[],this._lastBlurY=null,this._quality,this._lastQuality=null,this.FRAG_SHADER_TEMPLATE="uniform float xWeight[{{blurX}}];uniform float yWeight[{{blurY}}];uniform vec2 textureOffset;void main(void) {vec4 color = vec4(0.0);float xAdj = ({{blurX}}.0-1.0)/2.0;float yAdj = ({{blurY}}.0-1.0)/2.0;vec2 sampleOffset;for(int i=0; i<{{blurX}}; i++) {for(int j=0; j<{{blurY}}; j++) {sampleOffset = vRenderCoord + (textureOffset * vec2(float(i)-xAdj, float(j)-yAdj));color += texture2D(uSampler, sampleOffset) * (xWeight[i] * yWeight[j]);}}gl_FragColor = color.rgba;}",(isNaN(i)||i<1)&&(i=1),this.setQuality(0|i)}var e=createjs.extend(t,createjs.Filter);e.getBlurX=function(){return this._blurX},e.getBlurY=function(){return this._blurY},e.setBlurX=function(t){(isNaN(t)||t<0)&&(t=0),this._blurX=t},e.setBlurY=function(t){(isNaN(t)||t<0)&&(t=0),this._blurY=t},e.getQuality=function(){return this._quality},e.setQuality=function(t){(isNaN(t)||t<0)&&(t=0),this._quality=0|t},e._getShader=function(){var t=this._lastBlurX!==this._blurX,e=this._lastBlurY!==this._blurY,i=this._lastQuality!==this._quality;return t||e||i?((t||i)&&(this._blurXTable=this._getTable(this._blurX*this._quality)),(e||i)&&(this._blurYTable=this._getTable(this._blurY*this._quality)),this._updateShader(),this._lastBlurX=this._blurX,this._lastBlurY=this._blurY,void(this._lastQuality=this._quality)):this._compiledShader},e._setShader=function(){this._compiledShader};try{Object.defineProperties(e,{blurX:{get:e.getBlurX,set:e.setBlurX},blurY:{get:e.getBlurY,set:e.setBlurY},quality:{get:e.getQuality,set:e.setQuality},_builtShader:{get:e._getShader,set:e._setShader}})}catch(t){console.log(t)}e._getTable=function(t){if(t<=1)return[1];for(var e=[],i=Math.ceil(2*t),r=(i+=i%2?0:1)/2|0,n=-r;n<=r;n++){var s=n/r*4.2;e.push(1/Math.sqrt(2*Math.PI)*Math.pow(Math.E,-Math.pow(s,2)/4))}var a=e.reduce((function(t,e){return t+e}));return e.map((function(t,e,i){return t/a}))},e._updateShader=function(){if(void 0!==this._blurX&&void 0!==this._blurY){var t=this.FRAG_SHADER_TEMPLATE;t=(t=t.replace(/\{\{blurX\}\}/g,this._blurXTable.length.toFixed(0))).replace(/\{\{blurY\}\}/g,this._blurYTable.length.toFixed(0)),this.FRAG_SHADER_BODY=t}},e.shaderParamSetup=function(t,e,i){t.uniform1fv(t.getUniformLocation(i,"xWeight"),this._blurXTable),t.uniform1fv(t.getUniformLocation(i,"yWeight"),this._blurYTable),t.uniform2f(t.getUniformLocation(i,"textureOffset"),2/(e._viewportWidth*this._quality),2/(e._viewportHeight*this._quality))},t.MUL_TABLE=[1,171,205,293,57,373,79,137,241,27,391,357,41,19,283,265,497,469,443,421,25,191,365,349,335,161,155,149,9,278,269,261,505,245,475,231,449,437,213,415,405,395,193,377,369,361,353,345,169,331,325,319,313,307,301,37,145,285,281,69,271,267,263,259,509,501,493,243,479,118,465,459,113,446,55,435,429,423,209,413,51,403,199,393,97,3,379,375,371,367,363,359,355,351,347,43,85,337,333,165,327,323,5,317,157,311,77,305,303,75,297,294,73,289,287,71,141,279,277,275,68,135,67,133,33,262,260,129,511,507,503,499,495,491,61,121,481,477,237,235,467,232,115,457,227,451,7,445,221,439,218,433,215,427,425,211,419,417,207,411,409,203,202,401,399,396,197,49,389,387,385,383,95,189,47,187,93,185,23,183,91,181,45,179,89,177,11,175,87,173,345,343,341,339,337,21,167,83,331,329,327,163,81,323,321,319,159,79,315,313,39,155,309,307,153,305,303,151,75,299,149,37,295,147,73,291,145,289,287,143,285,71,141,281,35,279,139,69,275,137,273,17,271,135,269,267,133,265,33,263,131,261,130,259,129,257,1],t.SHG_TABLE=[0,9,10,11,9,12,10,11,12,9,13,13,10,9,13,13,14,14,14,14,10,13,14,14,14,13,13,13,9,14,14,14,15,14,15,14,15,15,14,15,15,15,14,15,15,15,15,15,14,15,15,15,15,15,15,12,14,15,15,13,15,15,15,15,16,16,16,15,16,14,16,16,14,16,13,16,16,16,15,16,13,16,15,16,14,9,16,16,16,16,16,16,16,16,16,13,14,16,16,15,16,16,10,16,15,16,14,16,16,14,16,16,14,16,16,14,15,16,16,16,14,15,14,15,13,16,16,15,17,17,17,17,17,17,14,15,17,17,16,16,17,16,15,17,16,17,11,17,16,17,16,17,16,17,17,16,17,17,16,17,17,16,16,17,17,17,16,14,17,17,17,17,15,16,14,16,15,16,13,16,15,16,14,16,15,16,12,16,15,16,17,17,17,17,17,13,16,15,17,17,17,16,15,17,17,17,16,15,17,17,14,16,17,17,16,17,17,16,15,17,16,14,17,16,15,17,16,17,17,16,17,15,16,17,14,17,16,15,17,16,17,13,17,16,17,17,16,17,14,17,16,17,16,17,16,17,9],e.getBounds=function(t){var e=0|this.blurX,i=0|this.blurY;if(e<=0&&i<=0)return t;var r=Math.pow(this.quality,.2);return(t||new createjs.Rectangle).pad(i*r+1,e*r+1,i*r+1,e*r+1)},e.clone=function(){return new t(this.blurX,this.blurY,this.quality)},e.toString=function(){return"[BlurFilter]"},e._applyFilter=function(e){var i=this._blurX>>1;if(isNaN(i)||i<0)return!1;var r=this._blurY>>1;if(isNaN(r)||r<0)return!1;if(0==i&&0==r)return!1;var n=this.quality;(isNaN(n)||n<1)&&(n=1),(n|=0)>3&&(n=3),n<1&&(n=1);var s=e.data,a=0,o=0,c=0,u=0,l=0,h=0,d=0,f=0,p=0,_=0,v=0,m=0,g=0,y=0,b=0,w=i+i+1|0,T=r+r+1|0,E=0|e.width,S=0|e.height,x=E-1|0,j=S-1|0,A=i+1|0,L=r+1|0,R={r:0,b:0,g:0,a:0},P=R;for(c=1;c<w;c++)P=P.n={r:0,b:0,g:0,a:0};P.n=R;var C={r:0,b:0,g:0,a:0},k=C;for(c=1;c<T;c++)k=k.n={r:0,b:0,g:0,a:0};k.n=C;for(var I=null,O=0|t.MUL_TABLE[i],M=0|t.SHG_TABLE[i],D=0|t.MUL_TABLE[r],F=0|t.SHG_TABLE[r];n-- >0;){d=h=0;var N=O,B=M;for(o=S;--o>-1;){for(f=A*(m=s[0|h]),p=A*(g=s[h+1|0]),_=A*(y=s[h+2|0]),v=A*(b=s[h+3|0]),P=R,c=A;--c>-1;)P.r=m,P.g=g,P.b=y,P.a=b,P=P.n;for(c=1;c<A;c++)u=h+((x<c?x:c)<<2)|0,f+=P.r=s[u],p+=P.g=s[u+1],_+=P.b=s[u+2],v+=P.a=s[u+3],P=P.n;for(I=R,a=0;a<E;a++)s[h++]=f*N>>>B,s[h++]=p*N>>>B,s[h++]=_*N>>>B,s[h++]=v*N>>>B,u=d+((u=a+i+1)<x?u:x)<<2,f-=I.r-(I.r=s[u]),p-=I.g-(I.g=s[u+1]),_-=I.b-(I.b=s[u+2]),v-=I.a-(I.a=s[u+3]),I=I.n;d+=E}for(N=D,B=F,a=0;a<E;a++){for(f=L*(m=s[h=a<<2])|0,p=L*(g=s[h+1|0])|0,_=L*(y=s[h+2|0])|0,v=L*(b=s[h+3|0])|0,k=C,c=0;c<L;c++)k.r=m,k.g=g,k.b=y,k.a=b,k=k.n;for(l=E,c=1;c<=r;c++)h=l+a<<2,f+=k.r=s[h],p+=k.g=s[h+1],_+=k.b=s[h+2],v+=k.a=s[h+3],k=k.n,c<j&&(l+=E);if(h=a,I=C,n>0)for(o=0;o<S;o++)s[(u=h<<2)+3]=b=v*N>>>B,b>0?(s[u]=f*N>>>B,s[u+1]=p*N>>>B,s[u+2]=_*N>>>B):s[u]=s[u+1]=s[u+2]=0,u=a+((u=o+L)<j?u:j)*E<<2,f-=I.r-(I.r=s[u]),p-=I.g-(I.g=s[u+1]),_-=I.b-(I.b=s[u+2]),v-=I.a-(I.a=s[u+3]),I=I.n,h+=E;else for(o=0;o<S;o++)s[(u=h<<2)+3]=b=v*N>>>B,b>0?(b=255/b,s[u]=(f*N>>>B)*b,s[u+1]=(p*N>>>B)*b,s[u+2]=(_*N>>>B)*b):s[u]=s[u+1]=s[u+2]=0,u=a+((u=o+L)<j?u:j)*E<<2,f-=I.r-(I.r=s[u]),p-=I.g-(I.g=s[u+1]),_-=I.b-(I.b=s[u+2]),v-=I.a-(I.a=s[u+3]),I=I.n,h+=E}}return!0},createjs.BlurFilter=createjs.promote(t,"Filter")}(),window.createjs=window.createjs||{},function(){"use strict";function t(t){this.Filter_constructor(),this.alphaMap=t,this._alphaMap=null,this._mapData=null,this._mapTexture=null,this.FRAG_SHADER_BODY="uniform sampler2D uAlphaSampler;void main(void) {vec4 color = texture2D(uSampler, vRenderCoord);vec4 alphaMap = texture2D(uAlphaSampler, vTextureCoord);gl_FragColor = vec4(color.rgb, color.a * (alphaMap.r * ceil(alphaMap.a)));}"}var e=createjs.extend(t,createjs.Filter);e.shaderParamSetup=function(t,e,i){this._mapTexture||(this._mapTexture=t.createTexture()),t.activeTexture(t.TEXTURE1),t.bindTexture(t.TEXTURE_2D,this._mapTexture),e.setTextureParams(t),t.texImage2D(t.TEXTURE_2D,0,t.RGBA,t.RGBA,t.UNSIGNED_BYTE,this.alphaMap),t.uniform1i(t.getUniformLocation(i,"uAlphaSampler"),1)},e.clone=function(){var e=new t(this.alphaMap);return e._alphaMap=this._alphaMap,e._mapData=this._mapData,e},e.toString=function(){return"[AlphaMapFilter]"},e._applyFilter=function(t){if(!this.alphaMap)return!0;if(!this._prepAlphaMap())return!1;for(var e=t.data,i=this._mapData,r=0,n=e.length;r<n;r+=4)e[r+3]=i[r]||0;return!0},e._prepAlphaMap=function(){if(!this.alphaMap)return!1;if(this.alphaMap==this._alphaMap&&this._mapData)return!0;this._mapData=null;var t,e=this._alphaMap=this.alphaMap,i=e;e instanceof HTMLCanvasElement?t=i.getContext("2d"):((i=createjs.createCanvas?createjs.createCanvas():document.createElement("canvas")).width=e.width,i.height=e.height,(t=i.getContext("2d")).drawImage(e,0,0));try{var r=t.getImageData(0,0,e.width,e.height)}catch(t){return!1}return this._mapData=r.data,!0},createjs.AlphaMapFilter=createjs.promote(t,"Filter")}(),window.createjs=window.createjs||{},function(){"use strict";function t(t){this.Filter_constructor(),this.mask=t,this.usesContext=!0,this.FRAG_SHADER_BODY="uniform sampler2D uAlphaSampler;void main(void) {vec4 color = texture2D(uSampler, vRenderCoord);vec4 alphaMap = texture2D(uAlphaSampler, vTextureCoord);gl_FragColor = vec4(color.rgb, color.a * alphaMap.a);}"}var e=createjs.extend(t,createjs.Filter);e.shaderParamSetup=function(t,e,i){this._mapTexture||(this._mapTexture=t.createTexture()),t.activeTexture(t.TEXTURE1),t.bindTexture(t.TEXTURE_2D,this._mapTexture),e.setTextureParams(t),t.texImage2D(t.TEXTURE_2D,0,t.RGBA,t.RGBA,t.UNSIGNED_BYTE,this.mask),t.uniform1i(t.getUniformLocation(i,"uAlphaSampler"),1)},e.applyFilter=function(t,e,i,r,n,s,a,o){return!this.mask||(null==a&&(a=e),null==o&&(o=i),(s=s||t).save(),t==s&&(s.globalCompositeOperation="destination-in",s.drawImage(this.mask,a,o),s.restore(),!0))},e.clone=function(){return new t(this.mask)},e.toString=function(){return"[AlphaMaskFilter]"},createjs.AlphaMaskFilter=createjs.promote(t,"Filter")}(),window.createjs=window.createjs||{},function(){"use strict";function t(t,e,i,r,n,s,a,o){this.Filter_constructor(),this.redMultiplier=null!=t?t:1,this.greenMultiplier=null!=e?e:1,this.blueMultiplier=null!=i?i:1,this.alphaMultiplier=null!=r?r:1,this.redOffset=n||0,this.greenOffset=s||0,this.blueOffset=a||0,this.alphaOffset=o||0,this.FRAG_SHADER_BODY="uniform vec4 uColorMultiplier;uniform vec4 uColorOffset;void main(void) {vec4 color = texture2D(uSampler, vRenderCoord);gl_FragColor = (color * uColorMultiplier) + uColorOffset;}"}var e=createjs.extend(t,createjs.Filter);e.shaderParamSetup=function(t,e,i){t.uniform4f(t.getUniformLocation(i,"uColorMultiplier"),this.redMultiplier,this.greenMultiplier,this.blueMultiplier,this.alphaMultiplier),t.uniform4f(t.getUniformLocation(i,"uColorOffset"),this.redOffset/255,this.greenOffset/255,this.blueOffset/255,this.alphaOffset/255)},e.toString=function(){return"[ColorFilter]"},e.clone=function(){return new t(this.redMultiplier,this.greenMultiplier,this.blueMultiplier,this.alphaMultiplier,this.redOffset,this.greenOffset,this.blueOffset,this.alphaOffset)},e._applyFilter=function(t){for(var e=t.data,i=e.length,r=0;r<i;r+=4)e[r]=e[r]*this.redMultiplier+this.redOffset,e[r+1]=e[r+1]*this.greenMultiplier+this.greenOffset,e[r+2]=e[r+2]*this.blueMultiplier+this.blueOffset,e[r+3]=e[r+3]*this.alphaMultiplier+this.alphaOffset;return!0},createjs.ColorFilter=createjs.promote(t,"Filter")}(),window.createjs=window.createjs||{},function(){"use strict";function t(t,e,i,r){this.setColor(t,e,i,r)}var e=t.prototype;t.DELTA_INDEX=[0,.01,.02,.04,.05,.06,.07,.08,.1,.11,.12,.14,.15,.16,.17,.18,.2,.21,.22,.24,.25,.27,.28,.3,.32,.34,.36,.38,.4,.42,.44,.46,.48,.5,.53,.56,.59,.62,.65,.68,.71,.74,.77,.8,.83,.86,.89,.92,.95,.98,1,1.06,1.12,1.18,1.24,1.3,1.36,1.42,1.48,1.54,1.6,1.66,1.72,1.78,1.84,1.9,1.96,2,2.12,2.25,2.37,2.5,2.62,2.75,2.87,3,3.2,3.4,3.6,3.8,4,4.3,4.7,4.9,5,5.5,6,6.5,6.8,7,7.3,7.5,7.8,8,8.4,8.7,9,9.4,9.6,9.8,10],t.IDENTITY_MATRIX=[1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1],t.LENGTH=t.IDENTITY_MATRIX.length,e.setColor=function(t,e,i,r){return this.reset().adjustColor(t,e,i,r)},e.reset=function(){return this.copy(t.IDENTITY_MATRIX)},e.adjustColor=function(t,e,i,r){return this.adjustHue(r),this.adjustContrast(e),this.adjustBrightness(t),this.adjustSaturation(i)},e.adjustBrightness=function(t){return 0==t||isNaN(t)||(t=this._cleanValue(t,255),this._multiplyMatrix([1,0,0,0,t,0,1,0,0,t,0,0,1,0,t,0,0,0,1,0,0,0,0,0,1])),this},e.adjustContrast=function(e){return 0==e||isNaN(e)||(i=(e=this._cleanValue(e,100))<0?127+e/100*127:127*(i=0==(i=e%1)?t.DELTA_INDEX[e]:t.DELTA_INDEX[e|0]*(1-i)+t.DELTA_INDEX[1+(e|0)]*i)+127,this._multiplyMatrix([i/127,0,0,0,.5*(127-i),0,i/127,0,0,.5*(127-i),0,0,i/127,0,.5*(127-i),0,0,0,1,0,0,0,0,0,1])),this;var i},e.adjustSaturation=function(t){if(0==t||isNaN(t))return this;var e=1+((t=this._cleanValue(t,100))>0?3*t/100:t/100),i=.3086,r=.6094,n=.082;return this._multiplyMatrix([i*(1-e)+e,r*(1-e),n*(1-e),0,0,i*(1-e),r*(1-e)+e,n*(1-e),0,0,i*(1-e),r*(1-e),n*(1-e)+e,0,0,0,0,0,1,0,0,0,0,0,1]),this},e.adjustHue=function(t){if(0==t||isNaN(t))return this;t=this._cleanValue(t,180)/180*Math.PI;var e=Math.cos(t),i=Math.sin(t),r=.213,n=.715,s=.072;return this._multiplyMatrix([r+.787*e+i*-r,n+e*-n+i*-n,s+e*-s+.928*i,0,0,r+e*-r+.143*i,n+e*(1-n)+.14*i,s+e*-s+-.283*i,0,0,r+e*-r+-.787*i,n+e*-n+i*n,s+.928*e+i*s,0,0,0,0,0,1,0,0,0,0,0,1]),this},e.concat=function(e){return(e=this._fixMatrix(e)).length!=t.LENGTH||this._multiplyMatrix(e),this},e.clone=function(){return(new t).copy(this)},e.toArray=function(){for(var e=[],i=0,r=t.LENGTH;i<r;i++)e[i]=this[i];return e},e.copy=function(e){for(var i=t.LENGTH,r=0;r<i;r++)this[r]=e[r];return this},e.toString=function(){return"[ColorMatrix]"},e._multiplyMatrix=function(t){var e,i,r,n=[];for(e=0;e<5;e++){for(i=0;i<5;i++)n[i]=this[i+5*e];for(i=0;i<5;i++){var s=0;for(r=0;r<5;r++)s+=t[i+5*r]*n[r];this[i+5*e]=s}}},e._cleanValue=function(t,e){return Math.min(e,Math.max(-e,t))},e._fixMatrix=function(e){return e instanceof t&&(e=e.toArray()),e.length<t.LENGTH?e=e.slice(0,e.length).concat(t.IDENTITY_MATRIX.slice(e.length,t.LENGTH)):e.length>t.LENGTH&&(e=e.slice(0,t.LENGTH)),e},createjs.ColorMatrix=t}(),window.createjs=window.createjs||{},function(){"use strict";function t(t){this.Filter_constructor(),this.matrix=t,this.FRAG_SHADER_BODY="uniform mat4 uColorMatrix;uniform vec4 uColorMatrixOffset;void main(void) {vec4 color = texture2D(uSampler, vRenderCoord);mat4 m = uColorMatrix;vec4 newColor = vec4(0,0,0,0);newColor.r = color.r*m[0][0] + color.g*m[0][1] + color.b*m[0][2] + color.a*m[0][3];newColor.g = color.r*m[1][0] + color.g*m[1][1] + color.b*m[1][2] + color.a*m[1][3];newColor.b = color.r*m[2][0] + color.g*m[2][1] + color.b*m[2][2] + color.a*m[2][3];newColor.a = color.r*m[3][0] + color.g*m[3][1] + color.b*m[3][2] + color.a*m[3][3];gl_FragColor = newColor + uColorMatrixOffset;}"}var e=createjs.extend(t,createjs.Filter);e.shaderParamSetup=function(t,e,i){var r=this.matrix,n=new Float32Array([r[0],r[1],r[2],r[3],r[5],r[6],r[7],r[8],r[10],r[11],r[12],r[13],r[15],r[16],r[17],r[18]]);t.uniformMatrix4fv(t.getUniformLocation(i,"uColorMatrix"),!1,n),t.uniform4f(t.getUniformLocation(i,"uColorMatrixOffset"),r[4]/255,r[9]/255,r[14]/255,r[19]/255)},e.toString=function(){return"[ColorMatrixFilter]"},e.clone=function(){return new t(this.matrix)},e._applyFilter=function(t){for(var e,i,r,n,s=t.data,a=s.length,o=this.matrix,c=o[0],u=o[1],l=o[2],h=o[3],d=o[4],f=o[5],p=o[6],_=o[7],v=o[8],m=o[9],g=o[10],y=o[11],b=o[12],w=o[13],T=o[14],E=o[15],S=o[16],x=o[17],j=o[18],A=o[19],L=0;L<a;L+=4)e=s[L],i=s[L+1],r=s[L+2],n=s[L+3],s[L]=e*c+i*u+r*l+n*h+d,s[L+1]=e*f+i*p+r*_+n*v+m,s[L+2]=e*g+i*y+r*b+n*w+T,s[L+3]=e*E+i*S+r*x+n*j+A;return!0},createjs.ColorMatrixFilter=createjs.promote(t,"Filter")}(),window.createjs=window.createjs||{},function(){"use strict";function t(){throw"Touch cannot be instantiated"}t.isSupported=function(){return!!("ontouchstart"in window||window.navigator.msPointerEnabled&&window.navigator.msMaxTouchPoints>0||window.navigator.pointerEnabled&&window.navigator.maxTouchPoints>0)},t.enable=function(e,i,r){return!!(e&&e.canvas&&t.isSupported())&&(e.__touch||(e.__touch={pointers:{},multitouch:!i,preventDefault:!r,count:0},"ontouchstart"in window?t._IOS_enable(e):(window.navigator.msPointerEnabled||window.navigator.pointerEnabled)&&t._IE_enable(e)),!0)},t.disable=function(e){e&&("ontouchstart"in window?t._IOS_disable(e):(window.navigator.msPointerEnabled||window.navigator.pointerEnabled)&&t._IE_disable(e),delete e.__touch)},t._IOS_enable=function(e){var i=e.canvas,r=e.__touch.f=function(i){t._IOS_handleEvent(e,i)};i.addEventListener("touchstart",r,!1),i.addEventListener("touchmove",r,!1),i.addEventListener("touchend",r,!1),i.addEventListener("touchcancel",r,!1)},t._IOS_disable=function(t){var e=t.canvas;if(e){var i=t.__touch.f;e.removeEventListener("touchstart",i,!1),e.removeEventListener("touchmove",i,!1),e.removeEventListener("touchend",i,!1),e.removeEventListener("touchcancel",i,!1)}},t._IOS_handleEvent=function(t,e){if(t){t.__touch.preventDefault&&e.preventDefault&&e.preventDefault();for(var i=e.changedTouches,r=e.type,n=0,s=i.length;n<s;n++){var a=i[n],o=a.identifier;a.target==t.canvas&&("touchstart"==r?this._handleStart(t,o,e,a.pageX,a.pageY):"touchmove"==r?this._handleMove(t,o,e,a.pageX,a.pageY):"touchend"!=r&&"touchcancel"!=r||this._handleEnd(t,o,e))}}},t._IE_enable=function(e){var i=e.canvas,r=e.__touch.f=function(i){t._IE_handleEvent(e,i)};void 0===window.navigator.pointerEnabled?(i.addEventListener("MSPointerDown",r,!1),window.addEventListener("MSPointerMove",r,!1),window.addEventListener("MSPointerUp",r,!1),window.addEventListener("MSPointerCancel",r,!1),e.__touch.preventDefault&&(i.style.msTouchAction="none")):(i.addEventListener("pointerdown",r,!1),window.addEventListener("pointermove",r,!1),window.addEventListener("pointerup",r,!1),window.addEventListener("pointercancel",r,!1),e.__touch.preventDefault&&(i.style.touchAction="none")),e.__touch.activeIDs={}},t._IE_disable=function(t){var e=t.__touch.f;void 0===window.navigator.pointerEnabled?(window.removeEventListener("MSPointerMove",e,!1),window.removeEventListener("MSPointerUp",e,!1),window.removeEventListener("MSPointerCancel",e,!1),t.canvas&&t.canvas.removeEventListener("MSPointerDown",e,!1)):(window.removeEventListener("pointermove",e,!1),window.removeEventListener("pointerup",e,!1),window.removeEventListener("pointercancel",e,!1),t.canvas&&t.canvas.removeEventListener("pointerdown",e,!1))},t._IE_handleEvent=function(t,e){if(t){t.__touch.preventDefault&&e.preventDefault&&e.preventDefault();var i=e.type,r=e.pointerId,n=t.__touch.activeIDs;if("MSPointerDown"==i||"pointerdown"==i){if(e.srcElement!=t.canvas)return;n[r]=!0,this._handleStart(t,r,e,e.pageX,e.pageY)}else n[r]&&("MSPointerMove"==i||"pointermove"==i?this._handleMove(t,r,e,e.pageX,e.pageY):"MSPointerUp"!=i&&"MSPointerCancel"!=i&&"pointerup"!=i&&"pointercancel"!=i||(delete n[r],this._handleEnd(t,r,e)))}},t._handleStart=function(t,e,i,r,n){var s=t.__touch;if(s.multitouch||!s.count){var a=s.pointers;a[e]||(a[e]=!0,s.count++,t._handlePointerDown(e,i,r,n))}},t._handleMove=function(t,e,i,r,n){t.__touch.pointers[e]&&t._handlePointerMove(e,i,r,n)},t._handleEnd=function(t,e,i){var r=t.__touch,n=r.pointers;n[e]&&(r.count--,t._handlePointerUp(e,i,!0),delete n[e])},createjs.Touch=t}(),window.createjs=window.createjs||{},function(){"use strict";var t=createjs.EaselJS=createjs.EaselJS||{};t.version="1.0.0",t.buildDate="Thu, 14 Sep 2017 19:47:53 GMT"}()},681:function(t,e,i){var r;t=i.nmd(t),window.createjs=window.createjs||{},function(){"use strict";var t=createjs.PreloadJS=createjs.PreloadJS||{};t.version="1.0.0",t.buildDate="Thu, 14 Sep 2017 19:47:47 GMT"}(),window.createjs=window.createjs||{},createjs.extend=function(t,e){"use strict";function i(){this.constructor=t}return i.prototype=e.prototype,t.prototype=new i},window.createjs=window.createjs||{},createjs.promote=function(t,e){"use strict";var i=t.prototype,r=Object.getPrototypeOf&&Object.getPrototypeOf(i)||i.__proto__;if(r)for(var n in i[(e+="_")+"constructor"]=r.constructor,r)i.hasOwnProperty(n)&&"function"==typeof r[n]&&(i[e+n]=r[n]);return t},window.createjs=window.createjs||{},createjs.deprecate=function(t,e){"use strict";return function(){var i="Deprecated property or method '"+e+"'. See docs for info.";return console&&(console.warn?console.warn(i):console.log(i)),t&&t.apply(this,arguments)}},window.createjs=window.createjs||{},function(){"use strict";createjs.proxy=function(t,e){var i=Array.prototype.slice.call(arguments,2);return function(){return t.apply(e,Array.prototype.slice.call(arguments,0).concat(i))}}}(),window.createjs=window.createjs||{},createjs.indexOf=function(t,e){"use strict";for(var i=0,r=t.length;i<r;i++)if(e===t[i])return i;return-1},window.createjs=window.createjs||{},function(){"use strict";function t(t,e,i){this.type=t,this.target=null,this.currentTarget=null,this.eventPhase=0,this.bubbles=!!e,this.cancelable=!!i,this.timeStamp=(new Date).getTime(),this.defaultPrevented=!1,this.propagationStopped=!1,this.immediatePropagationStopped=!1,this.removed=!1}var e=t.prototype;e.preventDefault=function(){this.defaultPrevented=this.cancelable&&!0},e.stopPropagation=function(){this.propagationStopped=!0},e.stopImmediatePropagation=function(){this.immediatePropagationStopped=this.propagationStopped=!0},e.remove=function(){this.removed=!0},e.clone=function(){return new t(this.type,this.bubbles,this.cancelable)},e.set=function(t){for(var e in t)this[e]=t[e];return this},e.toString=function(){return"[Event (type="+this.type+")]"},createjs.Event=t}(),window.createjs=window.createjs||{},function(){"use strict";function t(t,e,i){this.Event_constructor("error"),this.title=t,this.message=e,this.data=i}createjs.extend(t,createjs.Event).clone=function(){return new createjs.ErrorEvent(this.title,this.message,this.data)},createjs.ErrorEvent=createjs.promote(t,"Event")}(),window.createjs=window.createjs||{},function(){"use strict";function t(){this._listeners=null,this._captureListeners=null}var e=t.prototype;t.initialize=function(t){t.addEventListener=e.addEventListener,t.on=e.on,t.removeEventListener=t.off=e.removeEventListener,t.removeAllEventListeners=e.removeAllEventListeners,t.hasEventListener=e.hasEventListener,t.dispatchEvent=e.dispatchEvent,t._dispatchEvent=e._dispatchEvent,t.willTrigger=e.willTrigger},e.addEventListener=function(t,e,i){var r,n=(r=i?this._captureListeners=this._captureListeners||{}:this._listeners=this._listeners||{})[t];return n&&this.removeEventListener(t,e,i),(n=r[t])?n.push(e):r[t]=[e],e},e.on=function(t,e,i,r,n,s){return e.handleEvent&&(i=i||e,e=e.handleEvent),i=i||this,this.addEventListener(t,(function(t){e.call(i,t,n),r&&t.remove()}),s)},e.removeEventListener=function(t,e,i){var r=i?this._captureListeners:this._listeners;if(r){var n=r[t];if(n)for(var s=0,a=n.length;s<a;s++)if(n[s]==e){1==a?delete r[t]:n.splice(s,1);break}}},e.off=e.removeEventListener,e.removeAllEventListeners=function(t){t?(this._listeners&&delete this._listeners[t],this._captureListeners&&delete this._captureListeners[t]):this._listeners=this._captureListeners=null},e.dispatchEvent=function(t,e,i){if("string"==typeof t){var r=this._listeners;if(!(e||r&&r[t]))return!0;t=new createjs.Event(t,e,i)}else t.target&&t.clone&&(t=t.clone());try{t.target=this}catch(t){}if(t.bubbles&&this.parent){for(var n=this,s=[n];n.parent;)s.push(n=n.parent);var a,o=s.length;for(a=o-1;a>=0&&!t.propagationStopped;a--)s[a]._dispatchEvent(t,1+(0==a));for(a=1;a<o&&!t.propagationStopped;a++)s[a]._dispatchEvent(t,3)}else this._dispatchEvent(t,2);return!t.defaultPrevented},e.hasEventListener=function(t){var e=this._listeners,i=this._captureListeners;return!!(e&&e[t]||i&&i[t])},e.willTrigger=function(t){for(var e=this;e;){if(e.hasEventListener(t))return!0;e=e.parent}return!1},e.toString=function(){return"[EventDispatcher]"},e._dispatchEvent=function(t,e){var i,r,n=e<=2?this._captureListeners:this._listeners;if(t&&n&&(r=n[t.type])&&(i=r.length)){try{t.currentTarget=this}catch(t){}try{t.eventPhase=0|e}catch(t){}t.removed=!1,r=r.slice();for(var s=0;s<i&&!t.immediatePropagationStopped;s++){var a=r[s];a.handleEvent?a.handleEvent(t):a(t),t.removed&&(this.off(t.type,a,1==e),t.removed=!1)}}2===e&&this._dispatchEvent(t,2.1)},createjs.EventDispatcher=t}(),window.createjs=window.createjs||{},function(t){"use strict";function e(t,e){this.Event_constructor("progress"),this.loaded=t,this.total=null==e?1:e,this.progress=0==e?0:this.loaded/this.total}createjs.extend(e,createjs.Event).clone=function(){return new createjs.ProgressEvent(this.loaded,this.total)},createjs.ProgressEvent=createjs.promote(e,"Event")}(window),function(){var n=i.amdO,s={function:!0,object:!0},a=s[typeof e]&&e&&!e.nodeType&&e,o=s[typeof window]&&window||this,c=a&&s.object&&t&&!t.nodeType&&"object"==typeof i.g&&i.g;function u(t,e){t||(t=o.Object()),e||(e=o.Object());var i=t.Number||o.Number,r=t.String||o.String,n=t.Object||o.Object,a=t.Date||o.Date,c=t.SyntaxError||o.SyntaxError,l=t.TypeError||o.TypeError,h=t.Math||o.Math,d=t.JSON||o.JSON;"object"==typeof d&&d&&(e.stringify=d.stringify,e.parse=d.parse);var f,p,_,v=n.prototype,m=v.toString,g=new a(-0xc782b5b800cec);try{g=-109252==g.getUTCFullYear()&&0===g.getUTCMonth()&&1===g.getUTCDate()&&10==g.getUTCHours()&&37==g.getUTCMinutes()&&6==g.getUTCSeconds()&&708==g.getUTCMilliseconds()}catch(t){}function y(t){if(y[t]!==_)return y[t];var n;if("bug-string-char-index"==t)n="a"!="a"[0];else if("json"==t)n=y("json-stringify")&&y("json-parse");else{var s,o='{"a":[1,true,false,null,"\\u0000\\b\\n\\f\\r\\t"]}';if("json-stringify"==t){var c=e.stringify,u="function"==typeof c&&g;if(u){(s=function(){return 1}).toJSON=s;try{u="0"===c(0)&&"0"===c(new i)&&'""'==c(new r)&&c(m)===_&&c(_)===_&&c()===_&&"1"===c(s)&&"[1]"==c([s])&&"[null]"==c([_])&&"null"==c(null)&&"[null,null,null]"==c([_,m,null])&&c({a:[s,!0,!1,null,"\0\b\n\f\r\t"]})==o&&"1"===c(null,s)&&"[\n 1,\n 2\n]"==c([1,2],null,1)&&'"-271821-04-20T00:00:00.000Z"'==c(new a(-864e13))&&'"+275760-09-13T00:00:00.000Z"'==c(new a(864e13))&&'"-000001-01-01T00:00:00.000Z"'==c(new a(-621987552e5))&&'"1969-12-31T23:59:59.999Z"'==c(new a(-1))}catch(t){u=!1}}n=u}if("json-parse"==t){var l=e.parse;if("function"==typeof l)try{if(0===l("0")&&!l(!1)){var h=5==(s=l(o)).a.length&&1===s.a[0];if(h){try{h=!l('"\t"')}catch(t){}if(h)try{h=1!==l("01")}catch(t){}if(h)try{h=1!==l("1.")}catch(t){}}}}catch(t){h=!1}n=h}}return y[t]=!!n}if(!y("json")){var b="[object Function]",w="[object Number]",T="[object String]",E="[object Array]",S=y("bug-string-char-index");if(!g)var x=h.floor,j=[0,31,59,90,120,151,181,212,243,273,304,334],A=function(t,e){return j[e]+365*(t-1970)+x((t-1969+(e=+(e>1)))/4)-x((t-1901+e)/100)+x((t-1601+e)/400)};if((f=v.hasOwnProperty)||(f=function(t){var e,i={};return(i.__proto__=null,i.__proto__={toString:1},i).toString!=m?f=function(t){var e=this.__proto__,i=t in(this.__proto__=null,this);return this.__proto__=e,i}:(e=i.constructor,f=function(t){var i=(this.constructor||e).prototype;return t in this&&!(t in i&&this[t]===i[t])}),i=null,f.call(this,t)}),p=function(t,e){var i,r,n,a=0;for(n in(i=function(){this.valueOf=0}).prototype.valueOf=0,r=new i)f.call(r,n)&&a++;return i=r=null,a?p=2==a?function(t,e){var i,r={},n=m.call(t)==b;for(i in t)n&&"prototype"==i||f.call(r,i)||!(r[i]=1)||!f.call(t,i)||e(i)}:function(t,e){var i,r,n=m.call(t)==b;for(i in t)n&&"prototype"==i||!f.call(t,i)||(r="constructor"===i)||e(i);(r||f.call(t,i="constructor"))&&e(i)}:(r=["valueOf","toString","toLocaleString","propertyIsEnumerable","isPrototypeOf","hasOwnProperty","constructor"],p=function(t,e){var i,n,a=m.call(t)==b,o=!a&&"function"!=typeof t.constructor&&s[typeof t.hasOwnProperty]&&t.hasOwnProperty||f;for(i in t)a&&"prototype"==i||!o.call(t,i)||e(i);for(n=r.length;i=r[--n];o.call(t,i)&&e(i));}),p(t,e)},!y("json-stringify")){var L={92:"\\\\",34:'\\"',8:"\\b",12:"\\f",10:"\\n",13:"\\r",9:"\\t"},R=function(t,e){return("000000"+(e||0)).slice(-t)},P=function(t){for(var e='"',i=0,r=t.length,n=!S||r>10,s=n&&(S?t.split(""):t);i<r;i++){var a=t.charCodeAt(i);switch(a){case 8:case 9:case 10:case 12:case 13:case 34:case 92:e+=L[a];break;default:if(a<32){e+="\\u00"+R(2,a.toString(16));break}e+=n?s[i]:t.charAt(i)}}return e+'"'},C=function(t,e,i,r,n,s,a){var o,c,u,h,d,v,g,y,b,S,j,L,k,I,O,M;try{o=e[t]}catch(t){}if("object"==typeof o&&o)if("[object Date]"!=(c=m.call(o))||f.call(o,"toJSON"))"function"==typeof o.toJSON&&(c!=w&&c!=T&&c!=E||f.call(o,"toJSON"))&&(o=o.toJSON(t));else if(o>-1/0&&o<1/0){if(A){for(d=x(o/864e5),u=x(d/365.2425)+1970-1;A(u+1,0)<=d;u++);for(h=x((d-A(u,0))/30.42);A(u,h+1)<=d;h++);d=1+d-A(u,h),g=x((v=(o%864e5+864e5)%864e5)/36e5)%24,y=x(v/6e4)%60,b=x(v/1e3)%60,S=v%1e3}else u=o.getUTCFullYear(),h=o.getUTCMonth(),d=o.getUTCDate(),g=o.getUTCHours(),y=o.getUTCMinutes(),b=o.getUTCSeconds(),S=o.getUTCMilliseconds();o=(u<=0||u>=1e4?(u<0?"-":"+")+R(6,u<0?-u:u):R(4,u))+"-"+R(2,h+1)+"-"+R(2,d)+"T"+R(2,g)+":"+R(2,y)+":"+R(2,b)+"."+R(3,S)+"Z"}else o=null;if(i&&(o=i.call(e,t,o)),null===o)return"null";if("[object Boolean]"==(c=m.call(o)))return""+o;if(c==w)return o>-1/0&&o<1/0?""+o:"null";if(c==T)return P(""+o);if("object"==typeof o){for(I=a.length;I--;)if(a[I]===o)throw l();if(a.push(o),j=[],O=s,s+=n,c==E){for(k=0,I=o.length;k<I;k++)L=C(k,o,i,r,n,s,a),j.push(L===_?"null":L);M=j.length?n?"[\n"+s+j.join(",\n"+s)+"\n"+O+"]":"["+j.join(",")+"]":"[]"}else p(r||o,(function(t){var e=C(t,o,i,r,n,s,a);e!==_&&j.push(P(t)+":"+(n?" ":"")+e)})),M=j.length?n?"{\n"+s+j.join(",\n"+s)+"\n"+O+"}":"{"+j.join(",")+"}":"{}";return a.pop(),M}};e.stringify=function(t,e,i){var r,n,a,o;if(s[typeof e]&&e)if((o=m.call(e))==b)n=e;else if(o==E){a={};for(var c,u=0,l=e.length;u<l;c=e[u++],((o=m.call(c))==T||o==w)&&(a[c]=1));}if(i)if((o=m.call(i))==w){if((i-=i%1)>0)for(r="",i>10&&(i=10);r.length<i;r+=" ");}else o==T&&(r=i.length<=10?i:i.slice(0,10));return C("",((c={})[""]=t,c),n,a,r,"",[])}}if(!y("json-parse")){var k,I,O=r.fromCharCode,M={92:"\\",34:'"',47:"/",98:"\b",116:"\t",110:"\n",102:"\f",114:"\r"},D=function(){throw k=I=null,c()},F=function(){for(var t,e,i,r,n,s=I,a=s.length;k<a;)switch(n=s.charCodeAt(k)){case 9:case 10:case 13:case 32:k++;break;case 123:case 125:case 91:case 93:case 58:case 44:return t=S?s.charAt(k):s[k],k++,t;case 34:for(t="@",k++;k<a;)if((n=s.charCodeAt(k))<32)D();else if(92==n)switch(n=s.charCodeAt(++k)){case 92:case 34:case 47:case 98:case 116:case 110:case 102:case 114:t+=M[n],k++;break;case 117:for(e=++k,i=k+4;k<i;k++)(n=s.charCodeAt(k))>=48&&n<=57||n>=97&&n<=102||n>=65&&n<=70||D();t+=O("0x"+s.slice(e,k));break;default:D()}else{if(34==n)break;for(n=s.charCodeAt(k),e=k;n>=32&&92!=n&&34!=n;)n=s.charCodeAt(++k);t+=s.slice(e,k)}if(34==s.charCodeAt(k))return k++,t;D();default:if(e=k,45==n&&(r=!0,n=s.charCodeAt(++k)),n>=48&&n<=57){for(48==n&&((n=s.charCodeAt(k+1))>=48&&n<=57)&&D(),r=!1;k<a&&((n=s.charCodeAt(k))>=48&&n<=57);k++);if(46==s.charCodeAt(k)){for(i=++k;i<a&&((n=s.charCodeAt(i))>=48&&n<=57);i++);i==k&&D(),k=i}if(101==(n=s.charCodeAt(k))||69==n){for(43!=(n=s.charCodeAt(++k))&&45!=n||k++,i=k;i<a&&((n=s.charCodeAt(i))>=48&&n<=57);i++);i==k&&D(),k=i}return+s.slice(e,k)}if(r&&D(),"true"==s.slice(k,k+4))return k+=4,!0;if("false"==s.slice(k,k+5))return k+=5,!1;if("null"==s.slice(k,k+4))return k+=4,null;D()}return"$"},N=function(t){var e,i;if("$"==t&&D(),"string"==typeof t){if("@"==(S?t.charAt(0):t[0]))return t.slice(1);if("["==t){for(e=[];"]"!=(t=F());i||(i=!0))i&&(","==t?"]"==(t=F())&&D():D()),","==t&&D(),e.push(N(t));return e}if("{"==t){for(e={};"}"!=(t=F());i||(i=!0))i&&(","==t?"}"==(t=F())&&D():D()),","!=t&&"string"==typeof t&&"@"==(S?t.charAt(0):t[0])&&":"==F()||D(),e[t.slice(1)]=N(F());return e}D()}return t},B=function(t,e,i){var r=U(t,e,i);r===_?delete t[e]:t[e]=r},U=function(t,e,i){var r,n=t[e];if("object"==typeof n&&n)if(m.call(n)==E)for(r=n.length;r--;)B(n,r,i);else p(n,(function(t){B(n,t,i)}));return i.call(t,e,n)};e.parse=function(t,e){var i,r;return k=0,I=""+t,i=N(F()),"$"!=F()&&D(),k=I=null,e&&m.call(e)==b?U(((r={})[""]=i,r),"",e):i}}}return e.runInContext=u,e}if(!c||c.global!==c&&c.window!==c&&c.self!==c||(o=c),a&&!n)u(o,a);else{var l=o.JSON,h=o.JSON3,d=!1,f=u(o,o.JSON3={noConflict:function(){return d||(d=!0,o.JSON=l,o.JSON3=h,l=h=null),f}});o.JSON={parse:f.parse,stringify:f.stringify}}n&&(void 0===(r=function(){return f}.call(e,i,e,t))||(t.exports=r))}.call(this),function(){var t={a:function(){return t.el("a")},svg:function(){return t.el("svg")},object:function(){return t.el("object")},image:function(){return t.el("image")},img:function(){return t.el("img")},style:function(){return t.el("style")},link:function(){return t.el("link")},script:function(){return t.el("script")},audio:function(){return t.el("audio")},video:function(){return t.el("video")},text:function(t){return document.createTextNode(t)},el:function(t){return document.createElement(t)}};createjs.Elements=t}(),function(){var t={ABSOLUTE_PATT:/^(?:\w+:)?\/{2}/i,RELATIVE_PATT:/^[./]*?\//i,EXTENSION_PATT:/\/?[^/]+\.(\w{1,5})$/i,parseURI:function(e){var i={absolute:!1,relative:!1,protocol:null,hostname:null,port:null,pathname:null,search:null,hash:null,host:null};if(null==e)return i;var r=createjs.Elements.a();for(var n in r.href=e,i)n in r&&(i[n]=r[n]);var s,a=e.indexOf("?");return a>-1&&(e=e.substr(0,a)),t.ABSOLUTE_PATT.test(e)?i.absolute=!0:t.RELATIVE_PATT.test(e)&&(i.relative=!0),(s=e.match(t.EXTENSION_PATT))&&(i.extension=s[1].toLowerCase()),i},formatQueryString:function(t,e){if(null==t)throw new Error("You must specify data.");var i=[];for(var r in t)i.push(r+"="+escape(t[r]));return e&&(i=i.concat(e)),i.join("&")},buildURI:function(t,e){if(null==e)return t;var i=[],r=t.indexOf("?");if(-1!=r){var n=t.slice(r+1);i=i.concat(n.split("&"))}return-1!=r?t.slice(0,r)+"?"+this.formatQueryString(e,i):t+"?"+this.formatQueryString(e,i)},isCrossDomain:function(t){var e=createjs.Elements.a();e.href=t.src;var i=createjs.Elements.a();return i.href=location.href,""!=e.hostname&&(e.port!=i.port||e.protocol!=i.protocol||e.hostname!=i.hostname)},isLocal:function(t){var e=createjs.Elements.a();return e.href=t.src,""==e.hostname&&"file:"==e.protocol}};createjs.URLUtils=t}(),function(){var t={container:null,appendToHead:function(e){t.getHead().appendChild(e)},appendToBody:function(e){if(null==t.container){t.container=document.createElement("div"),t.container.id="preloadjs-container";var i=t.container.style;i.visibility="hidden",i.position="absolute",i.width=t.container.style.height="10px",i.overflow="hidden",i.transform=i.msTransform=i.webkitTransform=i.oTransform="translate(-10px, -10px)",t.getBody().appendChild(t.container)}t.container.appendChild(e)},getHead:function(){return document.head||document.getElementsByTagName("head")[0]},getBody:function(){return document.body||document.getElementsByTagName("body")[0]},removeChild:function(t){t.parent&&t.parent.removeChild(t)},isImageTag:function(t){return t instanceof HTMLImageElement},isAudioTag:function(t){return!!window.HTMLAudioElement&&t instanceof HTMLAudioElement},isVideoTag:function(t){return!!window.HTMLVideoElement&&t instanceof HTMLVideoElement}};createjs.DomUtils=t}(),function(){var t={parseXML:function(t){var e=null;try{if(window.DOMParser)e=(new DOMParser).parseFromString(t,"text/xml")}catch(t){}if(!e)try{(e=new ActiveXObject("Microsoft.XMLDOM")).async=!1,e.loadXML(t)}catch(t){e=null}return e},parseJSON:function(t){if(null==t)return null;try{return JSON.parse(t)}catch(t){throw t}}};createjs.DataUtils=t}(),window.createjs=window.createjs||{},function(){var t={BINARY:"binary",CSS:"css",FONT:"font",FONTCSS:"fontcss",IMAGE:"image",JAVASCRIPT:"javascript",JSON:"json",JSONP:"jsonp",MANIFEST:"manifest",SOUND:"sound",VIDEO:"video",SPRITESHEET:"spritesheet",SVG:"svg",TEXT:"text",XML:"xml"};createjs.Types=t}(),window.createjs=window.createjs||{},function(){var t={POST:"POST",GET:"GET"};createjs.Methods=t}(),window.createjs=window.createjs||{},function(){"use strict";function t(){this.src=null,this.type=null,this.id=null,this.maintainOrder=!1,this.callback=null,this.data=null,this.method=createjs.Methods.GET,this.values=null,this.headers=null,this.withCredentials=!1,this.mimeType=null,this.crossOrigin=null,this.loadTimeout=i.LOAD_TIMEOUT_DEFAULT}var e=t.prototype={},i=t;i.LOAD_TIMEOUT_DEFAULT=8e3,i.create=function(e){if("string"==typeof e){var r=new t;return r.src=e,r}if(e instanceof i)return e;if(e instanceof Object&&e.src)return null==e.loadTimeout&&(e.loadTimeout=i.LOAD_TIMEOUT_DEFAULT),e;throw new Error("Type not recognized.")},e.set=function(t){for(var e in t)this[e]=t[e];return this},createjs.LoadItem=i}(),function(){var t={isBinary:function(t){switch(t){case createjs.Types.IMAGE:case createjs.Types.BINARY:return!0;default:return!1}},isText:function(t){switch(t){case createjs.Types.TEXT:case createjs.Types.JSON:case createjs.Types.MANIFEST:case createjs.Types.XML:case createjs.Types.CSS:case createjs.Types.SVG:case createjs.Types.JAVASCRIPT:case createjs.Types.SPRITESHEET:return!0;default:return!1}},getTypeByExtension:function(t){if(null==t)return createjs.Types.TEXT;switch(t.toLowerCase()){case"jpeg":case"jpg":case"gif":case"png":case"webp":case"bmp":return createjs.Types.IMAGE;case"ogg":case"mp3":case"webm":return createjs.Types.SOUND;case"mp4":case"webm":case"ts":return createjs.Types.VIDEO;case"json":return createjs.Types.JSON;case"xml":return createjs.Types.XML;case"css":return createjs.Types.CSS;case"js":return createjs.Types.JAVASCRIPT;case"svg":return createjs.Types.SVG;default:return createjs.Types.TEXT}}};createjs.RequestUtils=t}(),window.createjs=window.createjs||{},function(){"use strict";function t(t,e,i){this.EventDispatcher_constructor(),this.loaded=!1,this.canceled=!1,this.progress=0,this.type=i,this.resultFormatter=null,this._item=t?createjs.LoadItem.create(t):null,this._preferXHR=e,this._result=null,this._rawResult=null,this._loadedItems=null,this._tagSrcAttribute=null,this._tag=null}var e=createjs.extend(t,createjs.EventDispatcher),i=t;try{Object.defineProperties(i,{POST:{get:createjs.deprecate((function(){return createjs.Methods.POST}),"AbstractLoader.POST")},GET:{get:createjs.deprecate((function(){return createjs.Methods.GET}),"AbstractLoader.GET")},BINARY:{get:createjs.deprecate((function(){return createjs.Types.BINARY}),"AbstractLoader.BINARY")},CSS:{get:createjs.deprecate((function(){return createjs.Types.CSS}),"AbstractLoader.CSS")},FONT:{get:createjs.deprecate((function(){return createjs.Types.FONT}),"AbstractLoader.FONT")},FONTCSS:{get:createjs.deprecate((function(){return createjs.Types.FONTCSS}),"AbstractLoader.FONTCSS")},IMAGE:{get:createjs.deprecate((function(){return createjs.Types.IMAGE}),"AbstractLoader.IMAGE")},JAVASCRIPT:{get:createjs.deprecate((function(){return createjs.Types.JAVASCRIPT}),"AbstractLoader.JAVASCRIPT")},JSON:{get:createjs.deprecate((function(){return createjs.Types.JSON}),"AbstractLoader.JSON")},JSONP:{get:createjs.deprecate((function(){return createjs.Types.JSONP}),"AbstractLoader.JSONP")},MANIFEST:{get:createjs.deprecate((function(){return createjs.Types.MANIFEST}),"AbstractLoader.MANIFEST")},SOUND:{get:createjs.deprecate((function(){return createjs.Types.SOUND}),"AbstractLoader.SOUND")},VIDEO:{get:createjs.deprecate((function(){return createjs.Types.VIDEO}),"AbstractLoader.VIDEO")},SPRITESHEET:{get:createjs.deprecate((function(){return createjs.Types.SPRITESHEET}),"AbstractLoader.SPRITESHEET")},SVG:{get:createjs.deprecate((function(){return createjs.Types.SVG}),"AbstractLoader.SVG")},TEXT:{get:createjs.deprecate((function(){return createjs.Types.TEXT}),"AbstractLoader.TEXT")},XML:{get:createjs.deprecate((function(){return createjs.Types.XML}),"AbstractLoader.XML")}})}catch(t){}e.getItem=function(){return this._item},e.getResult=function(t){return t?this._rawResult:this._result},e.getTag=function(){return this._tag},e.setTag=function(t){this._tag=t},e.load=function(){this._createRequest(),this._request.on("complete",this,this),this._request.on("progress",this,this),this._request.on("loadStart",this,this),this._request.on("abort",this,this),this._request.on("timeout",this,this),this._request.on("error",this,this);var t=new createjs.Event("initialize");t.loader=this._request,this.dispatchEvent(t),this._request.load()},e.cancel=function(){this.canceled=!0,this.destroy()},e.destroy=function(){this._request&&(this._request.removeAllEventListeners(),this._request.destroy()),this._request=null,this._item=null,this._rawResult=null,this._result=null,this._loadItems=null,this.removeAllEventListeners()},e.getLoadedItems=function(){return this._loadedItems},e._createRequest=function(){this._preferXHR?this._request=new createjs.XHRRequest(this._item):this._request=new createjs.TagRequest(this._item,this._tag||this._createTag(),this._tagSrcAttribute)},e._createTag=function(t){return null},e._sendLoadStart=function(){this._isCanceled()||this.dispatchEvent("loadstart")},e._sendProgress=function(t){if(!this._isCanceled()){var e=null;"number"==typeof t?(this.progress=t,e=new createjs.ProgressEvent(this.progress)):(e=t,this.progress=t.loaded/t.total,e.progress=this.progress,(isNaN(this.progress)||this.progress==1/0)&&(this.progress=0)),this.hasEventListener("progress")&&this.dispatchEvent(e)}},e._sendComplete=function(){if(!this._isCanceled()){this.loaded=!0;var t=new createjs.Event("complete");t.rawResult=this._rawResult,null!=this._result&&(t.result=this._result),this.dispatchEvent(t)}},e._sendError=function(t){!this._isCanceled()&&this.hasEventListener("error")&&(null==t&&(t=new createjs.ErrorEvent("PRELOAD_ERROR_EMPTY")),this.dispatchEvent(t))},e._isCanceled=function(){return!(null!=window.createjs&&!this.canceled)},e.resultFormatter=null,e.handleEvent=function(t){switch(t.type){case"complete":this._rawResult=t.target._response;var e=this.resultFormatter&&this.resultFormatter(this);e instanceof Function?e.call(this,createjs.proxy(this._resultFormatSuccess,this),createjs.proxy(this._resultFormatFailed,this)):(this._result=e||this._rawResult,this._sendComplete());break;case"progress":this._sendProgress(t);break;case"error":this._sendError(t);break;case"loadstart":this._sendLoadStart();break;case"abort":case"timeout":this._isCanceled()||this.dispatchEvent(new createjs.ErrorEvent("PRELOAD_"+t.type.toUpperCase()+"_ERROR"))}},e._resultFormatSuccess=function(t){this._result=t,this._sendComplete()},e._resultFormatFailed=function(t){this._sendError(t)},e.toString=function(){return"[PreloadJS AbstractLoader]"},createjs.AbstractLoader=createjs.promote(t,"EventDispatcher")}(),window.createjs=window.createjs||{},function(){"use strict";function t(t,e,i){this.AbstractLoader_constructor(t,e,i),this.resultFormatter=this._formatResult,this._tagSrcAttribute="src",this.on("initialize",this._updateXHR,this)}var e=createjs.extend(t,createjs.AbstractLoader);e.load=function(){this._tag||(this._tag=this._createTag(this._item.src)),this._tag.preload="auto",this._tag.load(),this.AbstractLoader_load()},e._createTag=function(){},e._createRequest=function(){this._preferXHR?this._request=new createjs.XHRRequest(this._item):this._request=new createjs.MediaTagRequest(this._item,this._tag||this._createTag(),this._tagSrcAttribute)},e._updateXHR=function(t){t.loader.setResponseType&&t.loader.setResponseType("blob")},e._formatResult=function(t){if(this._tag.removeEventListener&&this._tag.removeEventListener("canplaythrough",this._loadedHandler),this._tag.onstalled=null,this._preferXHR){var e=window.URL||window.webkitURL,i=t.getResult(!0);t.getTag().src=e.createObjectURL(i)}return t.getTag()},createjs.AbstractMediaLoader=createjs.promote(t,"AbstractLoader")}(),window.createjs=window.createjs||{},function(){"use strict";var t=function(t){this._item=t},e=createjs.extend(t,createjs.EventDispatcher);e.load=function(){},e.destroy=function(){},e.cancel=function(){},createjs.AbstractRequest=createjs.promote(t,"EventDispatcher")}(),window.createjs=window.createjs||{},function(){"use strict";function t(t,e,i){this.AbstractRequest_constructor(t),this._tag=e,this._tagSrcAttribute=i,this._loadedHandler=createjs.proxy(this._handleTagComplete,this),this._addedToDOM=!1}var e=createjs.extend(t,createjs.AbstractRequest);e.load=function(){this._tag.onload=createjs.proxy(this._handleTagComplete,this),this._tag.onreadystatechange=createjs.proxy(this._handleReadyStateChange,this),this._tag.onerror=createjs.proxy(this._handleError,this);var t=new createjs.Event("initialize");t.loader=this._tag,this.dispatchEvent(t),this._loadTimeout=setTimeout(createjs.proxy(this._handleTimeout,this),this._item.loadTimeout),this._tag[this._tagSrcAttribute]=this._item.src,null==this._tag.parentNode&&(createjs.DomUtils.appendToBody(this._tag),this._addedToDOM=!0)},e.destroy=function(){this._clean(),this._tag=null,this.AbstractRequest_destroy()},e._handleReadyStateChange=function(){clearTimeout(this._loadTimeout);var t=this._tag;"loaded"!=t.readyState&&"complete"!=t.readyState||this._handleTagComplete()},e._handleError=function(){this._clean(),this.dispatchEvent("error")},e._handleTagComplete=function(){this._rawResult=this._tag,this._result=this.resultFormatter&&this.resultFormatter(this)||this._rawResult,this._clean(),this.dispatchEvent("complete")},e._handleTimeout=function(){this._clean(),this.dispatchEvent(new createjs.Event("timeout"))},e._clean=function(){this._tag.onload=null,this._tag.onreadystatechange=null,this._tag.onerror=null,this._addedToDOM&&null!=this._tag.parentNode&&this._tag.parentNode.removeChild(this._tag),clearTimeout(this._loadTimeout)},e._handleStalled=function(){},createjs.TagRequest=createjs.promote(t,"AbstractRequest")}(),window.createjs=window.createjs||{},function(){"use strict";function t(t,e,i){this.AbstractRequest_constructor(t),this._tag=e,this._tagSrcAttribute=i,this._loadedHandler=createjs.proxy(this._handleTagComplete,this)}var e=createjs.extend(t,createjs.TagRequest);e.load=function(){var t=createjs.proxy(this._handleStalled,this);this._stalledCallback=t;var e=createjs.proxy(this._handleProgress,this);this._handleProgress=e,this._tag.addEventListener("stalled",t),this._tag.addEventListener("progress",e),this._tag.addEventListener&&this._tag.addEventListener("canplaythrough",this._loadedHandler,!1),this.TagRequest_load()},e._handleReadyStateChange=function(){clearTimeout(this._loadTimeout);var t=this._tag;"loaded"!=t.readyState&&"complete"!=t.readyState||this._handleTagComplete()},e._handleStalled=function(){},e._handleProgress=function(t){if(t&&!(t.loaded>0&&0==t.total)){var e=new createjs.ProgressEvent(t.loaded,t.total);this.dispatchEvent(e)}},e._clean=function(){this._tag.removeEventListener&&this._tag.removeEventListener("canplaythrough",this._loadedHandler),this._tag.removeEventListener("stalled",this._stalledCallback),this._tag.removeEventListener("progress",this._progressCallback),this.TagRequest__clean()},createjs.MediaTagRequest=createjs.promote(t,"TagRequest")}(),window.createjs=window.createjs||{},function(){"use strict";function t(t){this.AbstractRequest_constructor(t),this._request=null,this._loadTimeout=null,this._xhrLevel=1,this._response=null,this._rawResponse=null,this._canceled=!1,this._handleLoadStartProxy=createjs.proxy(this._handleLoadStart,this),this._handleProgressProxy=createjs.proxy(this._handleProgress,this),this._handleAbortProxy=createjs.proxy(this._handleAbort,this),this._handleErrorProxy=createjs.proxy(this._handleError,this),this._handleTimeoutProxy=createjs.proxy(this._handleTimeout,this),this._handleLoadProxy=createjs.proxy(this._handleLoad,this),this._handleReadyStateChangeProxy=createjs.proxy(this._handleReadyStateChange,this),this._createXHR(t)}var e=createjs.extend(t,createjs.AbstractRequest);t.ACTIVEX_VERSIONS=["Msxml2.XMLHTTP.6.0","Msxml2.XMLHTTP.5.0","Msxml2.XMLHTTP.4.0","MSXML2.XMLHTTP.3.0","MSXML2.XMLHTTP","Microsoft.XMLHTTP"],e.getResult=function(t){return t&&this._rawResponse?this._rawResponse:this._response},e.cancel=function(){this.canceled=!0,this._clean(),this._request.abort()},e.load=function(){if(null!=this._request){null!=this._request.addEventListener?(this._request.addEventListener("loadstart",this._handleLoadStartProxy,!1),this._request.addEventListener("progress",this._handleProgressProxy,!1),this._request.addEventListener("abort",this._handleAbortProxy,!1),this._request.addEventListener("error",this._handleErrorProxy,!1),this._request.addEventListener("timeout",this._handleTimeoutProxy,!1),this._request.addEventListener("load",this._handleLoadProxy,!1),this._request.addEventListener("readystatechange",this._handleReadyStateChangeProxy,!1)):(this._request.onloadstart=this._handleLoadStartProxy,this._request.onprogress=this._handleProgressProxy,this._request.onabort=this._handleAbortProxy,this._request.onerror=this._handleErrorProxy,this._request.ontimeout=this._handleTimeoutProxy,this._request.onload=this._handleLoadProxy,this._request.onreadystatechange=this._handleReadyStateChangeProxy),1==this._xhrLevel&&(this._loadTimeout=setTimeout(createjs.proxy(this._handleTimeout,this),this._item.loadTimeout));try{this._item.values?this._request.send(createjs.URLUtils.formatQueryString(this._item.values)):this._request.send()}catch(t){this.dispatchEvent(new createjs.ErrorEvent("XHR_SEND",null,t))}}else this._handleError()},e.setResponseType=function(t){"blob"===t&&(t=window.URL?"blob":"arraybuffer",this._responseType=t),this._request.responseType=t},e.getAllResponseHeaders=function(){return this._request.getAllResponseHeaders instanceof Function?this._request.getAllResponseHeaders():null},e.getResponseHeader=function(t){return this._request.getResponseHeader instanceof Function?this._request.getResponseHeader(t):null},e._handleProgress=function(t){if(t&&!(t.loaded>0&&0==t.total)){var e=new createjs.ProgressEvent(t.loaded,t.total);this.dispatchEvent(e)}},e._handleLoadStart=function(t){clearTimeout(this._loadTimeout),this.dispatchEvent("loadstart")},e._handleAbort=function(t){this._clean(),this.dispatchEvent(new createjs.ErrorEvent("XHR_ABORTED",null,t))},e._handleError=function(t){this._clean(),this.dispatchEvent(new createjs.ErrorEvent(t.message))},e._handleReadyStateChange=function(t){4==this._request.readyState&&this._handleLoad()},e._handleLoad=function(t){if(!this.loaded){this.loaded=!0;var e=this._checkError();if(e)this._handleError(e);else{if(this._response=this._getResponse(),"arraybuffer"===this._responseType)try{this._response=new Blob([this._response])}catch(t){if(window.BlobBuilder=window.BlobBuilder||window.WebKitBlobBuilder||window.MozBlobBuilder||window.MSBlobBuilder,"TypeError"===t.name&&window.BlobBuilder){var i=new BlobBuilder;i.append(this._response),this._response=i.getBlob()}}this._clean(),this.dispatchEvent(new createjs.Event("complete"))}}},e._handleTimeout=function(t){this._clean(),this.dispatchEvent(new createjs.ErrorEvent("PRELOAD_TIMEOUT",null,t))},e._checkError=function(){var t=parseInt(this._request.status);return t>=400&&t<=599?new Error(t):0==t&&/^https?:/.test(location.protocol)?new Error(0):null},e._getResponse=function(){if(null!=this._response)return this._response;if(null!=this._request.response)return this._request.response;try{if(null!=this._request.responseText)return this._request.responseText}catch(t){}try{if(null!=this._request.responseXML)return this._request.responseXML}catch(t){}return null},e._createXHR=function(t){var e=createjs.URLUtils.isCrossDomain(t),i={},r=null;if(window.XMLHttpRequest)r=new XMLHttpRequest,e&&void 0===r.withCredentials&&window.XDomainRequest&&(r=new XDomainRequest);else{for(var n=0,a=s.ACTIVEX_VERSIONS.length;n<a;n++){var o=s.ACTIVEX_VERSIONS[n];try{r=new ActiveXObject(o);break}catch(t){}}if(null==r)return!1}null==t.mimeType&&createjs.RequestUtils.isText(t.type)&&(t.mimeType="text/plain; charset=utf-8"),t.mimeType&&r.overrideMimeType&&r.overrideMimeType(t.mimeType),this._xhrLevel="string"==typeof r.responseType?2:1;var c=null;if(c=t.method==createjs.Methods.GET?createjs.URLUtils.buildURI(t.src,t.values):t.src,r.open(t.method||createjs.Methods.GET,c,!0),e&&r instanceof XMLHttpRequest&&1==this._xhrLevel&&(i.Origin=location.origin),t.values&&t.method==createjs.Methods.POST&&(i["Content-Type"]="application/x-www-form-urlencoded"),e||i["X-Requested-With"]||(i["X-Requested-With"]="XMLHttpRequest"),t.headers)for(var u in t.headers)i[u]=t.headers[u];for(u in i)r.setRequestHeader(u,i[u]);return r instanceof XMLHttpRequest&&void 0!==t.withCredentials&&(r.withCredentials=t.withCredentials),this._request=r,!0},e._clean=function(){clearTimeout(this._loadTimeout),null!=this._request.removeEventListener?(this._request.removeEventListener("loadstart",this._handleLoadStartProxy),this._request.removeEventListener("progress",this._handleProgressProxy),this._request.removeEventListener("abort",this._handleAbortProxy),this._request.removeEventListener("error",this._handleErrorProxy),this._request.removeEventListener("timeout",this._handleTimeoutProxy),this._request.removeEventListener("load",this._handleLoadProxy),this._request.removeEventListener("readystatechange",this._handleReadyStateChangeProxy)):(this._request.onloadstart=null,this._request.onprogress=null,this._request.onabort=null,this._request.onerror=null,this._request.ontimeout=null,this._request.onload=null,this._request.onreadystatechange=null)},e.toString=function(){return"[PreloadJS XHRRequest]"},createjs.XHRRequest=createjs.promote(t,"AbstractRequest")}(),window.createjs=window.createjs||{},function(){"use strict";function t(t,e,i){this.AbstractLoader_constructor(),this._plugins=[],this._typeCallbacks={},this._extensionCallbacks={},this.next=null,this.maintainScriptOrder=!0,this.stopOnError=!1,this._maxConnections=1,this._availableLoaders=[createjs.FontLoader,createjs.ImageLoader,createjs.JavaScriptLoader,createjs.CSSLoader,createjs.JSONLoader,createjs.JSONPLoader,createjs.SoundLoader,createjs.ManifestLoader,createjs.SpriteSheetLoader,createjs.XMLLoader,createjs.SVGLoader,createjs.BinaryLoader,createjs.VideoLoader,createjs.TextLoader],this._defaultLoaderLength=this._availableLoaders.length,this.init(t,e,i)}var e=createjs.extend(t,createjs.AbstractLoader),i=t;try{Object.defineProperties(i,{POST:{get:createjs.deprecate((function(){return createjs.Methods.POST}),"AbstractLoader.POST")},GET:{get:createjs.deprecate((function(){return createjs.Methods.GET}),"AbstractLoader.GET")},BINARY:{get:createjs.deprecate((function(){return createjs.Types.BINARY}),"AbstractLoader.BINARY")},CSS:{get:createjs.deprecate((function(){return createjs.Types.CSS}),"AbstractLoader.CSS")},FONT:{get:createjs.deprecate((function(){return createjs.Types.FONT}),"AbstractLoader.FONT")},FONTCSS:{get:createjs.deprecate((function(){return createjs.Types.FONTCSS}),"AbstractLoader.FONTCSS")},IMAGE:{get:createjs.deprecate((function(){return createjs.Types.IMAGE}),"AbstractLoader.IMAGE")},JAVASCRIPT:{get:createjs.deprecate((function(){return createjs.Types.JAVASCRIPT}),"AbstractLoader.JAVASCRIPT")},JSON:{get:createjs.deprecate((function(){return createjs.Types.JSON}),"AbstractLoader.JSON")},JSONP:{get:createjs.deprecate((function(){return createjs.Types.JSONP}),"AbstractLoader.JSONP")},MANIFEST:{get:createjs.deprecate((function(){return createjs.Types.MANIFEST}),"AbstractLoader.MANIFEST")},SOUND:{get:createjs.deprecate((function(){return createjs.Types.SOUND}),"AbstractLoader.SOUND")},VIDEO:{get:createjs.deprecate((function(){return createjs.Types.VIDEO}),"AbstractLoader.VIDEO")},SPRITESHEET:{get:createjs.deprecate((function(){return createjs.Types.SPRITESHEET}),"AbstractLoader.SPRITESHEET")},SVG:{get:createjs.deprecate((function(){return createjs.Types.SVG}),"AbstractLoader.SVG")},TEXT:{get:createjs.deprecate((function(){return createjs.Types.TEXT}),"AbstractLoader.TEXT")},XML:{get:createjs.deprecate((function(){return createjs.Types.XML}),"AbstractLoader.XML")}})}catch(t){}e.init=function(t,e,i){this.preferXHR=!0,this._preferXHR=!0,this.setPreferXHR(t),this._paused=!1,this._basePath=e,this._crossOrigin=i,this._loadStartWasDispatched=!1,this._currentlyLoadingScript=null,this._currentLoads=[],this._loadQueue=[],this._loadQueueBackup=[],this._loadItemsById={},this._loadItemsBySrc={},this._loadedResults={},this._loadedRawResults={},this._numItems=0,this._numItemsLoaded=0,this._scriptOrder=[],this._loadedScripts=[],this._lastProgress=NaN},e.registerLoader=function(t){if(!t||!t.canLoadItem)throw new Error("loader is of an incorrect type.");if(-1!=this._availableLoaders.indexOf(t))throw new Error("loader already exists.");this._availableLoaders.unshift(t)},e.unregisterLoader=function(t){var e=this._availableLoaders.indexOf(t);-1!=e&&e<this._defaultLoaderLength-1&&this._availableLoaders.splice(e,1)},e.setPreferXHR=function(t){return this.preferXHR=0!=t&&null!=window.XMLHttpRequest,this.preferXHR},e.removeAll=function(){this.remove()},e.remove=function(t){var e=null;if(t&&!Array.isArray(t))e=[t];else if(t)e=t;else if(arguments.length>0)return;var i=!1;if(e){for(;e.length;){var r=e.pop(),n=this.getResult(r);for(s=this._loadQueue.length-1;s>=0;s--)if((a=this._loadQueue[s].getItem()).id==r||a.src==r){this._loadQueue.splice(s,1)[0].cancel();break}for(s=this._loadQueueBackup.length-1;s>=0;s--)if((a=this._loadQueueBackup[s].getItem()).id==r||a.src==r){this._loadQueueBackup.splice(s,1)[0].cancel();break}if(n)this._disposeItem(this.getItem(r));else for(var s=this._currentLoads.length-1;s>=0;s--){var a=this._currentLoads[s].getItem();if(a.id==r||a.src==r){this._currentLoads.splice(s,1)[0].cancel(),i=!0;break}}}i&&this._loadNext()}else{for(var o in this.close(),this._loadItemsById)this._disposeItem(this._loadItemsById[o]);this.init(this.preferXHR,this._basePath)}},e.reset=function(){for(var t in this.close(),this._loadItemsById)this._disposeItem(this._loadItemsById[t]);for(var e=[],i=0,r=this._loadQueueBackup.length;i<r;i++)e.push(this._loadQueueBackup[i].getItem());this.loadManifest(e,!1)},e.installPlugin=function(t){if(null!=t&&null!=t.getPreloadHandlers){this._plugins.push(t);var e=t.getPreloadHandlers();if(e.scope=t,null!=e.types)for(var i=0,r=e.types.length;i<r;i++)this._typeCallbacks[e.types[i]]=e;if(null!=e.extensions)for(i=0,r=e.extensions.length;i<r;i++)this._extensionCallbacks[e.extensions[i]]=e}},e.setMaxConnections=function(t){this._maxConnections=t,!this._paused&&this._loadQueue.length>0&&this._loadNext()},e.loadFile=function(t,e,i){if(null!=t)this._addItem(t,null,i),!1!==e?this.setPaused(!1):this.setPaused(!0);else{var r=new createjs.ErrorEvent("PRELOAD_NO_FILE");this._sendError(r)}},e.loadManifest=function(t,e,r){var n=null,s=null;if(Array.isArray(t)){if(0==t.length){var a=new createjs.ErrorEvent("PRELOAD_MANIFEST_EMPTY");return void this._sendError(a)}n=t}else if("string"==typeof t)n=[{src:t,type:i.MANIFEST}];else{if("object"!=typeof t){a=new createjs.ErrorEvent("PRELOAD_MANIFEST_NULL");return void this._sendError(a)}if(void 0!==t.src){if(null==t.type)t.type=i.MANIFEST;else if(t.type!=i.MANIFEST){var a=new createjs.ErrorEvent("PRELOAD_MANIFEST_TYPE");this._sendError(a)}n=[t]}else void 0!==t.manifest&&(n=t.manifest,s=t.path)}for(var o=0,c=n.length;o<c;o++)this._addItem(n[o],s,r);!1!==e?this.setPaused(!1):this.setPaused(!0)},e.load=function(){this.setPaused(!1)},e.getItem=function(t){return this._loadItemsById[t]||this._loadItemsBySrc[t]},e.getResult=function(t,e){var i=this._loadItemsById[t]||this._loadItemsBySrc[t];if(null==i)return null;var r=i.id;return e&&this._loadedRawResults[r]?this._loadedRawResults[r]:this._loadedResults[r]},e.getItems=function(t){var e=[];for(var i in this._loadItemsById){var r=this._loadItemsById[i],n=this.getResult(i);!0===t&&null==n||e.push({item:r,result:n,rawResult:this.getResult(i,!0)})}return e},e.setPaused=function(t){this._paused=t,this._paused||this._loadNext()},e.close=function(){for(;this._currentLoads.length;)this._currentLoads.pop().cancel();this._scriptOrder.length=0,this._loadedScripts.length=0,this.loadStartWasDispatched=!1,this._itemCount=0,this._lastProgress=NaN},e._addItem=function(t,e,i){var r=this._createLoadItem(t,e,i);if(null!=r){var n=this._createLoader(r);null!=n&&("plugins"in n&&(n.plugins=this._plugins),r._loader=n,this._loadQueue.push(n),this._loadQueueBackup.push(n),this._numItems++,this._updateProgress(),(this.maintainScriptOrder&&r.type==createjs.Types.JAVASCRIPT||!0===r.maintainOrder)&&(this._scriptOrder.push(r),this._loadedScripts.push(null)))}},e._createLoadItem=function(t,e,i){var r=createjs.LoadItem.create(t);if(null==r)return null;var n="",s=i||this._basePath;if(r.src instanceof Object){if(!r.type)return null;if(e){n=e;var a=createjs.URLUtils.parseURI(e);null==s||a.absolute||a.relative||(n=s+n)}else null!=s&&(n=s)}else{var o=createjs.URLUtils.parseURI(r.src);o.extension&&(r.ext=o.extension),null==r.type&&(r.type=createjs.RequestUtils.getTypeByExtension(r.ext));var c=r.src;if(!o.absolute&&!o.relative)if(e){n=e;a=createjs.URLUtils.parseURI(e);c=e+c,null==s||a.absolute||a.relative||(n=s+n)}else null!=s&&(n=s);r.src=n+r.src}r.path=n,void 0!==r.id&&null!==r.id&&""!==r.id||(r.id=c);var u=this._typeCallbacks[r.type]||this._extensionCallbacks[r.ext];if(u){var l=u.callback.call(u.scope,r,this);if(!1===l)return null;!0===l||null!=l&&(r._loader=l),null!=(o=createjs.URLUtils.parseURI(r.src)).extension&&(r.ext=o.extension)}return this._loadItemsById[r.id]=r,this._loadItemsBySrc[r.src]=r,null==r.crossOrigin&&(r.crossOrigin=this._crossOrigin),r},e._createLoader=function(t){if(null!=t._loader)return t._loader;for(var e=this.preferXHR,i=0;i<this._availableLoaders.length;i++){var r=this._availableLoaders[i];if(r&&r.canLoadItem(t))return new r(t,e)}return null},e._loadNext=function(){if(!this._paused){this._loadStartWasDispatched||(this._sendLoadStart(),this._loadStartWasDispatched=!0),this._numItems==this._numItemsLoaded?(this.loaded=!0,this._sendComplete(),this.next&&this.next.load&&this.next.load()):this.loaded=!1;for(var t=0;t<this._loadQueue.length&&!(this._currentLoads.length>=this._maxConnections);t++){var e=this._loadQueue[t];this._canStartLoad(e)&&(this._loadQueue.splice(t,1),t--,this._loadItem(e))}}},e._loadItem=function(t){t.on("fileload",this._handleFileLoad,this),t.on("progress",this._handleProgress,this),t.on("complete",this._handleFileComplete,this),t.on("error",this._handleError,this),t.on("fileerror",this._handleFileError,this),this._currentLoads.push(t),this._sendFileStart(t.getItem()),t.load()},e._handleFileLoad=function(t){t.target=null,this.dispatchEvent(t)},e._handleFileError=function(t){var e=new createjs.ErrorEvent("FILE_LOAD_ERROR",null,t.item);this._sendError(e)},e._handleError=function(t){var e=t.target;this._numItemsLoaded++,this._finishOrderedItem(e,!0),this._updateProgress();var i=new createjs.ErrorEvent("FILE_LOAD_ERROR",null,e.getItem());this._sendError(i),this.stopOnError?this.setPaused(!0):(this._removeLoadItem(e),this._cleanLoadItem(e),this._loadNext())},e._handleFileComplete=function(t){var e=t.target,i=e.getItem(),r=e.getResult();this._loadedResults[i.id]=r;var n=e.getResult(!0);null!=n&&n!==r&&(this._loadedRawResults[i.id]=n),this._saveLoadedItems(e),this._removeLoadItem(e),this._finishOrderedItem(e)||this._processFinishedLoad(i,e),this._cleanLoadItem(e)},e._saveLoadedItems=function(t){var e=t.getLoadedItems();if(null!==e)for(var i=0;i<e.length;i++){var r=e[i].item;this._loadItemsBySrc[r.src]=r,this._loadItemsById[r.id]=r,this._loadedResults[r.id]=e[i].result,this._loadedRawResults[r.id]=e[i].rawResult}},e._finishOrderedItem=function(t,e){var i=t.getItem();if(this.maintainScriptOrder&&i.type==createjs.Types.JAVASCRIPT||i.maintainOrder){t instanceof createjs.JavaScriptLoader&&(this._currentlyLoadingScript=!1);var r=createjs.indexOf(this._scriptOrder,i);return-1!=r&&(this._loadedScripts[r]=!0===e||i,this._checkScriptLoadOrder(),!0)}return!1},e._checkScriptLoadOrder=function(){for(var t=this._loadedScripts.length,e=0;e<t;e++){var i=this._loadedScripts[e];if(null===i)break;if(!0!==i){var r=this._loadedResults[i.id];i.type==createjs.Types.JAVASCRIPT&&createjs.DomUtils.appendToHead(r);var n=i._loader;this._processFinishedLoad(i,n),this._loadedScripts[e]=!0}}},e._processFinishedLoad=function(t,e){if(this._numItemsLoaded++,!this.maintainScriptOrder&&t.type==createjs.Types.JAVASCRIPT){var i=e.getTag();createjs.DomUtils.appendToHead(i)}this._updateProgress(),this._sendFileComplete(t,e),this._loadNext()},e._canStartLoad=function(t){if(!this.maintainScriptOrder||t.preferXHR)return!0;var e=t.getItem();if(e.type!=createjs.Types.JAVASCRIPT)return!0;if(this._currentlyLoadingScript)return!1;for(var i=this._scriptOrder.indexOf(e),r=0;r<i;){if(null==this._loadedScripts[r])return!1;r++}return this._currentlyLoadingScript=!0,!0},e._removeLoadItem=function(t){for(var e=this._currentLoads.length,i=0;i<e;i++)if(this._currentLoads[i]==t){this._currentLoads.splice(i,1);break}},e._cleanLoadItem=function(t){var e=t.getItem();e&&delete e._loader},e._handleProgress=function(t){var e=t.target;this._sendFileProgress(e.getItem(),e.progress),this._updateProgress()},e._updateProgress=function(){var t=this._numItemsLoaded/this._numItems,e=this._numItems-this._numItemsLoaded;if(e>0){for(var i=0,r=0,n=this._currentLoads.length;r<n;r++)i+=this._currentLoads[r].progress;t+=i/e*(e/this._numItems)}this._lastProgress!=t&&(this._sendProgress(t),this._lastProgress=t)},e._disposeItem=function(t){delete this._loadedResults[t.id],delete this._loadedRawResults[t.id],delete this._loadItemsById[t.id],delete this._loadItemsBySrc[t.src]},e._sendFileProgress=function(t,e){if(!this._isCanceled()&&!this._paused&&this.hasEventListener("fileprogress")){var i=new createjs.Event("fileprogress");i.progress=e,i.loaded=e,i.total=1,i.item=t,this.dispatchEvent(i)}},e._sendFileComplete=function(t,e){if(!this._isCanceled()&&!this._paused){var i=new createjs.Event("fileload");i.loader=e,i.item=t,i.result=this._loadedResults[t.id],i.rawResult=this._loadedRawResults[t.id],t.completeHandler&&t.completeHandler(i),this.hasEventListener("fileload")&&this.dispatchEvent(i)}},e._sendFileStart=function(t){var e=new createjs.Event("filestart");e.item=t,this.hasEventListener("filestart")&&this.dispatchEvent(e)},e.toString=function(){return"[PreloadJS LoadQueue]"},createjs.LoadQueue=createjs.promote(t,"AbstractLoader")}(),window.createjs=window.createjs||{},function(){"use strict";function t(t){this.AbstractLoader_constructor(t,!0,createjs.Types.TEXT)}createjs.extend(t,createjs.AbstractLoader);t.canLoadItem=function(t){return t.type==createjs.Types.TEXT},createjs.TextLoader=createjs.promote(t,"AbstractLoader")}(),window.createjs=window.createjs||{},function(){"use strict";function t(t){this.AbstractLoader_constructor(t,!0,createjs.Types.BINARY),this.on("initialize",this._updateXHR,this)}var e=createjs.extend(t,createjs.AbstractLoader);t.canLoadItem=function(t){return t.type==createjs.Types.BINARY},e._updateXHR=function(t){t.loader.setResponseType("arraybuffer")},createjs.BinaryLoader=createjs.promote(t,"AbstractLoader")}(),window.createjs=window.createjs||{},function(){"use strict";function t(t,e){this.AbstractLoader_constructor(t,e,createjs.Types.CSS),this.resultFormatter=this._formatResult,this._tagSrcAttribute="href",this._tag=e?createjs.Elements.style():createjs.Elements.link(),this._tag.rel="stylesheet",this._tag.type="text/css"}var e=createjs.extend(t,createjs.AbstractLoader);t.canLoadItem=function(t){return t.type==createjs.Types.CSS},e._formatResult=function(t){if(this._preferXHR){var e=t.getTag();if(e.styleSheet)e.styleSheet.cssText=t.getResult(!0);else{var i=createjs.Elements.text(t.getResult(!0));e.appendChild(i)}}else e=this._tag;return createjs.DomUtils.appendToHead(e),e},createjs.CSSLoader=createjs.promote(t,"AbstractLoader")}(),window.createjs=window.createjs||{},function(){"use strict";function t(t,e){this.AbstractLoader_constructor(t,e,t.type),this._faces={},this._watched=[],this._count=0,this._watchInterval=null,this._loadTimeout=null,this._injectCSS=void 0===t.injectCSS||t.injectCSS,this.dispatchEvent("initialize")}var e=createjs.extend(t,createjs.AbstractLoader);t.canLoadItem=function(t){return t.type==createjs.Types.FONT||t.type==createjs.Types.FONTCSS},t.sampleText="abcdefghijklmnopqrstuvwxyz ABCDEFGHIJKLMNOPQRSTUVWXYZ",t._ctx=document.createElement("canvas").getContext("2d"),t._referenceFonts=["serif","monospace"],t.WEIGHT_REGEX=/[- ._]*(thin|normal|book|regular|medium|black|heavy|[1-9]00|(?:extra|ultra|semi|demi)?[- ._]*(?:light|bold))[- ._]*/gi,t.STYLE_REGEX=/[- ._]*(italic|oblique)[- ._]*/gi,t.FONT_FORMAT={woff2:"woff2",woff:"woff",ttf:"truetype",otf:"truetype"},t.FONT_WEIGHT={thin:100,extralight:200,ultralight:200,light:300,semilight:300,demilight:300,book:"normal",regular:"normal",semibold:600,demibold:600,extrabold:800,ultrabold:800,black:900,heavy:900},t.WATCH_DURATION=10,e.load=function(){if(this.type==createjs.Types.FONTCSS){if(!this._watchCSS())return void this.AbstractLoader_load()}else if(this._item.src instanceof Array)this._watchFontArray();else{var t=this._defFromSrc(this._item.src);this._watchFont(t),this._injectStyleTag(this._cssFromDef(t))}this._loadTimeout=setTimeout(createjs.proxy(this._handleTimeout,this),this._item.loadTimeout),this.dispatchEvent("loadstart")},e._handleTimeout=function(){this._stopWatching(),this.dispatchEvent(new createjs.ErrorEvent("PRELOAD_TIMEOUT"))},e._createRequest=function(){return this._request},e.handleEvent=function(t){switch(t.type){case"complete":this._rawResult=t.target._response,this._result=!0,this._parseCSS(this._rawResult);break;case"error":this._stopWatching(),this.AbstractLoader_handleEvent(t)}},e._watchCSS=function(){var t=this._item.src;return t instanceof HTMLStyleElement&&(this._injectCSS&&!t.parentNode&&(document.head||document.getElementsByTagName("head")[0]).appendChild(t),this._injectCSS=!1,t="\n"+t.textContent),-1!==t.search(/\n|\r|@font-face/i)?(this._parseCSS(t),!0):(this._request=new createjs.XHRRequest(this._item),!1)},e._parseCSS=function(t){for(var e=/@font-face\s*\{([^}]+)}/g;;){var i=e.exec(t);if(!i)break;this._watchFont(this._parseFontFace(i[1]))}this._injectStyleTag(t)},e._watchFontArray=function(){for(var t,e=this._item.src,i="",r=e.length-1;r>=0;r--){var n=e[r];t="string"==typeof n?this._defFromSrc(n):this._defFromObj(n),this._watchFont(t),i+=this._cssFromDef(t)+"\n"}this._injectStyleTag(i)},e._injectStyleTag=function(t){if(this._injectCSS){var e=document.head||document.getElementsByTagName("head")[0],i=document.createElement("style");i.type="text/css",i.styleSheet?i.styleSheet.cssText=t:i.appendChild(document.createTextNode(t)),e.appendChild(i)}},e._parseFontFace=function(t){var e=this._getCSSValue(t,"font-family"),i=this._getCSSValue(t,"src");return e&&i?this._defFromObj({family:e,src:i,style:this._getCSSValue(t,"font-style"),weight:this._getCSSValue(t,"font-weight")}):null},e._watchFont=function(t){t&&!this._faces[t.id]&&(this._faces[t.id]=t,this._watched.push(t),this._count++,this._calculateReferenceSizes(t),this._startWatching())},e._startWatching=function(){null==this._watchInterval&&(this._watchInterval=setInterval(createjs.proxy(this._watch,this),t.WATCH_DURATION))},e._stopWatching=function(){clearInterval(this._watchInterval),clearTimeout(this._loadTimeout),this._watchInterval=null},e._watch=function(){for(var e=this._watched,i=t._referenceFonts,r=e.length,n=r-1;n>=0;n--)for(var s=e[n],a=s.refs,o=a.length-1;o>=0;o--){if(this._getTextWidth(s.family+","+i[o],s.weight,s.style)!=a[o]){var c=new createjs.Event("fileload");s.type="font-family",c.item=s,this.dispatchEvent(c),e.splice(n,1);break}}if(r!==e.length){c=new createjs.ProgressEvent(this._count-e.length,this._count);this.dispatchEvent(c)}0===r&&(this._stopWatching(),this._sendComplete())},e._calculateReferenceSizes=function(e){for(var i=t._referenceFonts,r=e.refs=[],n=0;n<i.length;n++)r[n]=this._getTextWidth(i[n],e.weight,e.style)},e._defFromSrc=function(e){var i,r=/[- ._]+/g,n=e,s=null;-1!==(i=n.search(/[?#]/))&&(n=n.substr(0,i)),-1!==(i=n.lastIndexOf("."))&&(s=n.substr(i+1),n=n.substr(0,i)),-1!==(i=n.lastIndexOf("/"))&&(n=n.substr(i+1));var a=n,o=a.match(t.WEIGHT_REGEX);o&&(o=o[0],a=a.replace(o,""),o=o.replace(r,"").toLowerCase());var c=n.match(t.STYLE_REGEX);c&&(a=a.replace(c[0],""),c="italic"),a=a.replace(r,"");var u="local('"+n.replace(r," ")+"'), url('"+e+"')",l=t.FONT_FORMAT[s];return l&&(u+=" format('"+l+"')"),this._defFromObj({family:a,weight:t.FONT_WEIGHT[o]||o,style:c,src:u})},e._defFromObj=function(t){var e={family:t.family,src:t.src,style:t.style||"normal",weight:t.weight||"normal"};return e.id=e.family+";"+e.style+";"+e.weight,e},e._cssFromDef=function(t){return"@font-face {\n\tfont-family: '"+t.family+"';\n\tfont-style: "+t.style+";\n\tfont-weight: "+t.weight+";\n\tsrc: "+t.src+";\n}"},e._getTextWidth=function(e,i,r){var n=t._ctx;return n.font=r+" "+i+" 72px "+e,n.measureText(t.sampleText).width},e._getCSSValue=function(t,e){var i=new RegExp(e+":s*([^;}]+?)s*[;}]").exec(t);return i&&i[1]?i[1]:null},createjs.FontLoader=createjs.promote(t,"AbstractLoader")}(),window.createjs=window.createjs||{},function(){"use strict";function t(t,e){this.AbstractLoader_constructor(t,e,createjs.Types.IMAGE),this.resultFormatter=this._formatResult,this._tagSrcAttribute="src",createjs.DomUtils.isImageTag(t)?this._tag=t:createjs.DomUtils.isImageTag(t.src)?this._tag=t.src:createjs.DomUtils.isImageTag(t.tag)&&(this._tag=t.tag),null!=this._tag?this._preferXHR=!1:this._tag=createjs.Elements.img(),this.on("initialize",this._updateXHR,this)}var e=createjs.extend(t,createjs.AbstractLoader);t.canLoadItem=function(t){return t.type==createjs.Types.IMAGE},e.load=function(){if(""!=this._tag.src&&this._tag.complete)this._sendComplete();else{var t=this._item.crossOrigin;1==t&&(t="Anonymous"),null==t||createjs.URLUtils.isLocal(this._item)||(this._tag.crossOrigin=t),this.AbstractLoader_load()}},e._updateXHR=function(t){t.loader.mimeType="text/plain; charset=x-user-defined-binary",t.loader.setResponseType&&t.loader.setResponseType("blob")},e._formatResult=function(t){return this._formatImage},e._formatImage=function(t,e){var i=this._tag,r=window.URL||window.webkitURL;if(this._preferXHR)if(r){var n=r.createObjectURL(this.getResult(!0));i.src=n,i.addEventListener("load",this._cleanUpURL,!1),i.addEventListener("error",this._cleanUpURL,!1)}else i.src=this._item.src;else;i.complete?t(i):(i.onload=createjs.proxy((function(){t(this._tag),i.onload=i.onerror=null}),this),i.onerror=createjs.proxy((function(t){e(new createjs.ErrorEvent("IMAGE_FORMAT",null,t)),i.onload=i.onerror=null}),this))},e._cleanUpURL=function(t){(window.URL||window.webkitURL).revokeObjectURL(t.target.src)},createjs.ImageLoader=createjs.promote(t,"AbstractLoader")}(),window.createjs=window.createjs||{},function(){"use strict";function t(t,e){this.AbstractLoader_constructor(t,e,createjs.Types.JAVASCRIPT),this.resultFormatter=this._formatResult,this._tagSrcAttribute="src",this.setTag(createjs.Elements.script())}var e=createjs.extend(t,createjs.AbstractLoader);t.canLoadItem=function(t){return t.type==createjs.Types.JAVASCRIPT},e._formatResult=function(t){var e=t.getTag();return this._preferXHR&&(e.text=t.getResult(!0)),e},createjs.JavaScriptLoader=createjs.promote(t,"AbstractLoader")}(),window.createjs=window.createjs||{},function(){"use strict";function t(t){this.AbstractLoader_constructor(t,!0,createjs.Types.JSON),this.resultFormatter=this._formatResult}var e=createjs.extend(t,createjs.AbstractLoader);t.canLoadItem=function(t){return t.type==createjs.Types.JSON},e._formatResult=function(t){var e=null;try{e=createjs.DataUtils.parseJSON(t.getResult(!0))}catch(t){var i=new createjs.ErrorEvent("JSON_FORMAT",null,t);return this._sendError(i),t}return e},createjs.JSONLoader=createjs.promote(t,"AbstractLoader")}(),window.createjs=window.createjs||{},function(){"use strict";function t(t){this.AbstractLoader_constructor(t,!1,createjs.Types.JSONP),this.setTag(createjs.Elements.script()),this.getTag().type="text/javascript"}var e=createjs.extend(t,createjs.AbstractLoader);t.canLoadItem=function(t){return t.type==createjs.Types.JSONP},e.cancel=function(){this.AbstractLoader_cancel(),this._dispose()},e.load=function(){if(null==this._item.callback)throw new Error("callback is required for loading JSONP requests.");if(null!=window[this._item.callback])throw new Error("JSONP callback '"+this._item.callback+"' already exists on window. You need to specify a different callback or re-name the current one.");window[this._item.callback]=createjs.proxy(this._handleLoad,this),createjs.DomUtils.appendToBody(this._tag),this._loadTimeout=setTimeout(createjs.proxy(this._handleTimeout,this),this._item.loadTimeout),this._tag.src=this._item.src},e._handleLoad=function(t){this._result=this._rawResult=t,this._sendComplete(),this._dispose()},e._handleTimeout=function(){this._dispose(),this.dispatchEvent(new createjs.ErrorEvent("timeout"))},e._dispose=function(){createjs.DomUtils.removeChild(this._tag),delete window[this._item.callback],clearTimeout(this._loadTimeout)},createjs.JSONPLoader=createjs.promote(t,"AbstractLoader")}(),window.createjs=window.createjs||{},function(){"use strict";function t(t,e){this.AbstractLoader_constructor(t,e,createjs.Types.MANIFEST),this.plugins=null,this._manifestQueue=null}var e=createjs.extend(t,createjs.AbstractLoader),i=t;i.MANIFEST_PROGRESS=.25,i.canLoadItem=function(t){return t.type==createjs.Types.MANIFEST},e.load=function(){this.AbstractLoader_load()},e._createRequest=function(){var t=this._item.callback;this._request=null!=t?new createjs.JSONPLoader(this._item):new createjs.JSONLoader(this._item)},e.handleEvent=function(t){switch(t.type){case"complete":return this._rawResult=t.target.getResult(!0),this._result=t.target.getResult(),this._sendProgress(i.MANIFEST_PROGRESS),void this._loadManifest(this._result);case"progress":return t.loaded*=i.MANIFEST_PROGRESS,this.progress=t.loaded/t.total,(isNaN(this.progress)||this.progress==1/0)&&(this.progress=0),void this._sendProgress(t)}this.AbstractLoader_handleEvent(t)},e.destroy=function(){this.AbstractLoader_destroy(),this._manifestQueue.close()},e._loadManifest=function(t){if(t&&t.manifest){var e=this._manifestQueue=new createjs.LoadQueue(this._preferXHR);e.on("fileload",this._handleManifestFileLoad,this),e.on("progress",this._handleManifestProgress,this),e.on("complete",this._handleManifestComplete,this,!0),e.on("error",this._handleManifestError,this,!0);for(var i=0,r=this.plugins.length;i<r;i++)e.installPlugin(this.plugins[i]);e.loadManifest(t)}else this._sendComplete()},e._handleManifestFileLoad=function(t){t.target=null,this.dispatchEvent(t)},e._handleManifestComplete=function(t){this._loadedItems=this._manifestQueue.getItems(!0),this._sendComplete()},e._handleManifestProgress=function(t){this.progress=t.progress*(1-i.MANIFEST_PROGRESS)+i.MANIFEST_PROGRESS,this._sendProgress(this.progress)},e._handleManifestError=function(t){var e=new createjs.Event("fileerror");e.item=t.data,this.dispatchEvent(e)},createjs.ManifestLoader=createjs.promote(t,"AbstractLoader")}(),window.createjs=window.createjs||{},function(){"use strict";function t(t,e){this.AbstractMediaLoader_constructor(t,e,createjs.Types.SOUND),createjs.DomUtils.isAudioTag(t)||createjs.DomUtils.isAudioTag(t.src)?this._tag=t:createjs.DomUtils.isAudioTag(t.tag)&&(this._tag=createjs.DomUtils.isAudioTag(t)?t:t.src),null!=this._tag&&(this._preferXHR=!1)}var e=createjs.extend(t,createjs.AbstractMediaLoader);t.canLoadItem=function(t){return t.type==createjs.Types.SOUND},e._createTag=function(t){var e=createjs.Elements.audio();return e.autoplay=!1,e.preload="none",e.src=t,e},createjs.SoundLoader=createjs.promote(t,"AbstractMediaLoader")}(),window.createjs=window.createjs||{},function(){"use strict";function t(t,e){this.AbstractMediaLoader_constructor(t,e,createjs.Types.VIDEO),createjs.DomUtils.isVideoTag(t)||createjs.DomUtils.isVideoTag(t.src)?(this.setTag(createjs.DomUtils.isVideoTag(t)?t:t.src),this._preferXHR=!1):this.setTag(this._createTag())}var e=t;createjs.extend(t,createjs.AbstractMediaLoader)._createTag=function(){return createjs.Elements.video()},e.canLoadItem=function(t){return t.type==createjs.Types.VIDEO},createjs.VideoLoader=createjs.promote(t,"AbstractMediaLoader")}(),window.createjs=window.createjs||{},function(){"use strict";function t(t,e){this.AbstractLoader_constructor(t,e,createjs.Types.SPRITESHEET),this._manifestQueue=null}var e=createjs.extend(t,createjs.AbstractLoader),i=t;i.SPRITESHEET_PROGRESS=.25,i.canLoadItem=function(t){return t.type==createjs.Types.SPRITESHEET},e.destroy=function(){this.AbstractLoader_destroy(),this._manifestQueue.close()},e._createRequest=function(){var t=this._item.callback;this._request=null!=t?new createjs.JSONPLoader(this._item):new createjs.JSONLoader(this._item)},e.handleEvent=function(t){switch(t.type){case"complete":return this._rawResult=t.target.getResult(!0),this._result=t.target.getResult(),this._sendProgress(i.SPRITESHEET_PROGRESS),void this._loadManifest(this._result);case"progress":return t.loaded*=i.SPRITESHEET_PROGRESS,this.progress=t.loaded/t.total,(isNaN(this.progress)||this.progress==1/0)&&(this.progress=0),void this._sendProgress(t)}this.AbstractLoader_handleEvent(t)},e._loadManifest=function(t){if(t&&t.images){var e=this._manifestQueue=new createjs.LoadQueue(this._preferXHR,this._item.path,this._item.crossOrigin);e.on("complete",this._handleManifestComplete,this,!0),e.on("fileload",this._handleManifestFileLoad,this),e.on("progress",this._handleManifestProgress,this),e.on("error",this._handleManifestError,this,!0),e.loadManifest(t.images)}},e._handleManifestFileLoad=function(t){var e=t.result;if(null!=e){var i=this.getResult().images,r=i.indexOf(t.item.src);i[r]=e}},e._handleManifestComplete=function(t){this._result=new createjs.SpriteSheet(this._result),this._loadedItems=this._manifestQueue.getItems(!0),this._sendComplete()},e._handleManifestProgress=function(t){this.progress=t.progress*(1-i.SPRITESHEET_PROGRESS)+i.SPRITESHEET_PROGRESS,this._sendProgress(this.progress)},e._handleManifestError=function(t){var e=new createjs.Event("fileerror");e.item=t.data,this.dispatchEvent(e)},createjs.SpriteSheetLoader=createjs.promote(t,"AbstractLoader")}(),window.createjs=window.createjs||{},function(){"use strict";function t(t,e){this.AbstractLoader_constructor(t,e,createjs.Types.SVG),this.resultFormatter=this._formatResult,this._tagSrcAttribute="data",e?this.setTag(createjs.Elements.svg()):(this.setTag(createjs.Elements.object()),this.getTag().type="image/svg+xml")}var e=createjs.extend(t,createjs.AbstractLoader);t.canLoadItem=function(t){return t.type==createjs.Types.SVG},e._formatResult=function(t){var e=createjs.DataUtils.parseXML(t.getResult(!0)),i=t.getTag();if(!this._preferXHR&&document.body.contains(i)&&document.body.removeChild(i),null!=e.documentElement){var r=e.documentElement;return document.importNode&&(r=document.importNode(r,!0)),i.appendChild(r),i}return e},createjs.SVGLoader=createjs.promote(t,"AbstractLoader")}(),window.createjs=window.createjs||{},function(){"use strict";function t(t){this.AbstractLoader_constructor(t,!0,createjs.Types.XML),this.resultFormatter=this._formatResult}var e=createjs.extend(t,createjs.AbstractLoader);t.canLoadItem=function(t){return t.type==createjs.Types.XML},e._formatResult=function(t){return createjs.DataUtils.parseXML(t.getResult(!0))},createjs.XMLLoader=createjs.promote(t,"AbstractLoader")}()},941:()=>{window.createjs=window.createjs||{},function(){var t=createjs.SoundJS=createjs.SoundJS||{};t.version="1.0.0",t.buildDate="Tue, 10 Oct 2017 12:24:35 GMT"}(),window.createjs=window.createjs||{},createjs.extend=function(t,e){"use strict";function i(){this.constructor=t}return i.prototype=e.prototype,t.prototype=new i},window.createjs=window.createjs||{},createjs.promote=function(t,e){"use strict";var i=t.prototype,r=Object.getPrototypeOf&&Object.getPrototypeOf(i)||i.__proto__;if(r)for(var n in i[(e+="_")+"constructor"]=r.constructor,r)i.hasOwnProperty(n)&&"function"==typeof r[n]&&(i[e+n]=r[n]);return t},window.createjs=window.createjs||{},createjs.deprecate=function(t,e){"use strict";return function(){var i="Deprecated property or method '"+e+"'. See docs for info.";return console&&(console.warn?console.warn(i):console.log(i)),t&&t.apply(this,arguments)}},window.createjs=window.createjs||{},createjs.indexOf=function(t,e){"use strict";for(var i=0,r=t.length;i<r;i++)if(e===t[i])return i;return-1},window.createjs=window.createjs||{},function(){"use strict";createjs.proxy=function(t,e){var i=Array.prototype.slice.call(arguments,2);return function(){return t.apply(e,Array.prototype.slice.call(arguments,0).concat(i))}}}(),window.createjs=window.createjs||{},function(){"use strict";function t(){throw"BrowserDetect cannot be instantiated"}var e=t.agent=window.navigator.userAgent;t.isWindowPhone=e.indexOf("IEMobile")>-1||e.indexOf("Windows Phone")>-1,t.isFirefox=e.indexOf("Firefox")>-1,t.isOpera=null!=window.opera,t.isChrome=e.indexOf("Chrome")>-1,t.isIOS=(e.indexOf("iPod")>-1||e.indexOf("iPhone")>-1||e.indexOf("iPad")>-1)&&!t.isWindowPhone,t.isAndroid=e.indexOf("Android")>-1&&!t.isWindowPhone,t.isBlackberry=e.indexOf("Blackberry")>-1,createjs.BrowserDetect=t}(),window.createjs=window.createjs||{},function(){"use strict";function t(){this._listeners=null,this._captureListeners=null}var e=t.prototype;t.initialize=function(t){t.addEventListener=e.addEventListener,t.on=e.on,t.removeEventListener=t.off=e.removeEventListener,t.removeAllEventListeners=e.removeAllEventListeners,t.hasEventListener=e.hasEventListener,t.dispatchEvent=e.dispatchEvent,t._dispatchEvent=e._dispatchEvent,t.willTrigger=e.willTrigger},e.addEventListener=function(t,e,i){var r,n=(r=i?this._captureListeners=this._captureListeners||{}:this._listeners=this._listeners||{})[t];return n&&this.removeEventListener(t,e,i),(n=r[t])?n.push(e):r[t]=[e],e},e.on=function(t,e,i,r,n,s){return e.handleEvent&&(i=i||e,e=e.handleEvent),i=i||this,this.addEventListener(t,(function(t){e.call(i,t,n),r&&t.remove()}),s)},e.removeEventListener=function(t,e,i){var r=i?this._captureListeners:this._listeners;if(r){var n=r[t];if(n)for(var s=0,a=n.length;s<a;s++)if(n[s]==e){1==a?delete r[t]:n.splice(s,1);break}}},e.off=e.removeEventListener,e.removeAllEventListeners=function(t){t?(this._listeners&&delete this._listeners[t],this._captureListeners&&delete this._captureListeners[t]):this._listeners=this._captureListeners=null},e.dispatchEvent=function(t,e,i){if("string"==typeof t){var r=this._listeners;if(!(e||r&&r[t]))return!0;t=new createjs.Event(t,e,i)}else t.target&&t.clone&&(t=t.clone());try{t.target=this}catch(t){}if(t.bubbles&&this.parent){for(var n=this,s=[n];n.parent;)s.push(n=n.parent);var a,o=s.length;for(a=o-1;a>=0&&!t.propagationStopped;a--)s[a]._dispatchEvent(t,1+(0==a));for(a=1;a<o&&!t.propagationStopped;a++)s[a]._dispatchEvent(t,3)}else this._dispatchEvent(t,2);return!t.defaultPrevented},e.hasEventListener=function(t){var e=this._listeners,i=this._captureListeners;return!!(e&&e[t]||i&&i[t])},e.willTrigger=function(t){for(var e=this;e;){if(e.hasEventListener(t))return!0;e=e.parent}return!1},e.toString=function(){return"[EventDispatcher]"},e._dispatchEvent=function(t,e){var i,r,n=e<=2?this._captureListeners:this._listeners;if(t&&n&&(r=n[t.type])&&(i=r.length)){try{t.currentTarget=this}catch(t){}try{t.eventPhase=0|e}catch(t){}t.removed=!1,r=r.slice();for(var s=0;s<i&&!t.immediatePropagationStopped;s++){var a=r[s];a.handleEvent?a.handleEvent(t):a(t),t.removed&&(this.off(t.type,a,1==e),t.removed=!1)}}2===e&&this._dispatchEvent(t,2.1)},createjs.EventDispatcher=t}(),window.createjs=window.createjs||{},function(){"use strict";function t(t,e,i){this.type=t,this.target=null,this.currentTarget=null,this.eventPhase=0,this.bubbles=!!e,this.cancelable=!!i,this.timeStamp=(new Date).getTime(),this.defaultPrevented=!1,this.propagationStopped=!1,this.immediatePropagationStopped=!1,this.removed=!1}var e=t.prototype;e.preventDefault=function(){this.defaultPrevented=this.cancelable&&!0},e.stopPropagation=function(){this.propagationStopped=!0},e.stopImmediatePropagation=function(){this.immediatePropagationStopped=this.propagationStopped=!0},e.remove=function(){this.removed=!0},e.clone=function(){return new t(this.type,this.bubbles,this.cancelable)},e.set=function(t){for(var e in t)this[e]=t[e];return this},e.toString=function(){return"[Event (type="+this.type+")]"},createjs.Event=t}(),window.createjs=window.createjs||{},function(){"use strict";function t(t,e,i){this.Event_constructor("error"),this.title=t,this.message=e,this.data=i}createjs.extend(t,createjs.Event).clone=function(){return new createjs.ErrorEvent(this.title,this.message,this.data)},createjs.ErrorEvent=createjs.promote(t,"Event")}(),window.createjs=window.createjs||{},function(t){"use strict";function e(t,e){this.Event_constructor("progress"),this.loaded=t,this.total=null==e?1:e,this.progress=0==e?0:this.loaded/this.total}createjs.extend(e,createjs.Event).clone=function(){return new createjs.ProgressEvent(this.loaded,this.total)},createjs.ProgressEvent=createjs.promote(e,"Event")}(window),window.createjs=window.createjs||{},function(){"use strict";function t(){this.src=null,this.type=null,this.id=null,this.maintainOrder=!1,this.callback=null,this.data=null,this.method=createjs.Methods.GET,this.values=null,this.headers=null,this.withCredentials=!1,this.mimeType=null,this.crossOrigin=null,this.loadTimeout=i.LOAD_TIMEOUT_DEFAULT}var e=t.prototype={},i=t;i.LOAD_TIMEOUT_DEFAULT=8e3,i.create=function(e){if("string"==typeof e){var r=new t;return r.src=e,r}if(e instanceof i)return e;if(e instanceof Object&&e.src)return null==e.loadTimeout&&(e.loadTimeout=i.LOAD_TIMEOUT_DEFAULT),e;throw new Error("Type not recognized.")},e.set=function(t){for(var e in t)this[e]=t[e];return this},createjs.LoadItem=i}(),window.createjs=window.createjs||{},function(){var t={POST:"POST",GET:"GET"};createjs.Methods=t}(),window.createjs=window.createjs||{},function(){var t={BINARY:"binary",CSS:"css",FONT:"font",FONTCSS:"fontcss",IMAGE:"image",JAVASCRIPT:"javascript",JSON:"json",JSONP:"jsonp",MANIFEST:"manifest",SOUND:"sound",VIDEO:"video",SPRITESHEET:"spritesheet",SVG:"svg",TEXT:"text",XML:"xml"};createjs.Types=t}(),function(){var t={a:function(){return t.el("a")},svg:function(){return t.el("svg")},object:function(){return t.el("object")},image:function(){return t.el("image")},img:function(){return t.el("img")},style:function(){return t.el("style")},link:function(){return t.el("link")},script:function(){return t.el("script")},audio:function(){return t.el("audio")},video:function(){return t.el("video")},text:function(t){return document.createTextNode(t)},el:function(t){return document.createElement(t)}};createjs.Elements=t}(),function(){var t={container:null,appendToHead:function(e){t.getHead().appendChild(e)},appendToBody:function(e){if(null==t.container){t.container=document.createElement("div"),t.container.id="preloadjs-container";var i=t.container.style;i.visibility="hidden",i.position="absolute",i.width=t.container.style.height="10px",i.overflow="hidden",i.transform=i.msTransform=i.webkitTransform=i.oTransform="translate(-10px, -10px)",t.getBody().appendChild(t.container)}t.container.appendChild(e)},getHead:function(){return document.head||document.getElementsByTagName("head")[0]},getBody:function(){return document.body||document.getElementsByTagName("body")[0]},removeChild:function(t){t.parent&&t.parent.removeChild(t)},isImageTag:function(t){return t instanceof HTMLImageElement},isAudioTag:function(t){return!!window.HTMLAudioElement&&t instanceof HTMLAudioElement},isVideoTag:function(t){return!!window.HTMLVideoElement&&t instanceof HTMLVideoElement}};createjs.DomUtils=t}(),function(){var t={isBinary:function(t){switch(t){case createjs.Types.IMAGE:case createjs.Types.BINARY:return!0;default:return!1}},isText:function(t){switch(t){case createjs.Types.TEXT:case createjs.Types.JSON:case createjs.Types.MANIFEST:case createjs.Types.XML:case createjs.Types.CSS:case createjs.Types.SVG:case createjs.Types.JAVASCRIPT:case createjs.Types.SPRITESHEET:return!0;default:return!1}},getTypeByExtension:function(t){if(null==t)return createjs.Types.TEXT;switch(t.toLowerCase()){case"jpeg":case"jpg":case"gif":case"png":case"webp":case"bmp":return createjs.Types.IMAGE;case"ogg":case"mp3":case"webm":return createjs.Types.SOUND;case"mp4":case"webm":case"ts":return createjs.Types.VIDEO;case"json":return createjs.Types.JSON;case"xml":return createjs.Types.XML;case"css":return createjs.Types.CSS;case"js":return createjs.Types.JAVASCRIPT;case"svg":return createjs.Types.SVG;default:return createjs.Types.TEXT}}};createjs.RequestUtils=t}(),function(){var t={ABSOLUTE_PATT:/^(?:\w+:)?\/{2}/i,RELATIVE_PATT:/^[./]*?\//i,EXTENSION_PATT:/\/?[^/]+\.(\w{1,5})$/i,parseURI:function(e){var i={absolute:!1,relative:!1,protocol:null,hostname:null,port:null,pathname:null,search:null,hash:null,host:null};if(null==e)return i;var r=createjs.Elements.a();for(var n in r.href=e,i)n in r&&(i[n]=r[n]);var s,a=e.indexOf("?");return a>-1&&(e=e.substr(0,a)),t.ABSOLUTE_PATT.test(e)?i.absolute=!0:t.RELATIVE_PATT.test(e)&&(i.relative=!0),(s=e.match(t.EXTENSION_PATT))&&(i.extension=s[1].toLowerCase()),i},formatQueryString:function(t,e){if(null==t)throw new Error("You must specify data.");var i=[];for(var r in t)i.push(r+"="+escape(t[r]));return e&&(i=i.concat(e)),i.join("&")},buildURI:function(t,e){if(null==e)return t;var i=[],r=t.indexOf("?");if(-1!=r){var n=t.slice(r+1);i=i.concat(n.split("&"))}return-1!=r?t.slice(0,r)+"?"+this.formatQueryString(e,i):t+"?"+this.formatQueryString(e,i)},isCrossDomain:function(t){var e=createjs.Elements.a();e.href=t.src;var i=createjs.Elements.a();return i.href=location.href,""!=e.hostname&&(e.port!=i.port||e.protocol!=i.protocol||e.hostname!=i.hostname)},isLocal:function(t){var e=createjs.Elements.a();return e.href=t.src,""==e.hostname&&"file:"==e.protocol}};createjs.URLUtils=t}(),window.createjs=window.createjs||{},function(){"use strict";function t(t,e,i){this.EventDispatcher_constructor(),this.loaded=!1,this.canceled=!1,this.progress=0,this.type=i,this.resultFormatter=null,this._item=t?createjs.LoadItem.create(t):null,this._preferXHR=e,this._result=null,this._rawResult=null,this._loadedItems=null,this._tagSrcAttribute=null,this._tag=null}var e=createjs.extend(t,createjs.EventDispatcher),i=t;try{Object.defineProperties(i,{POST:{get:createjs.deprecate((function(){return createjs.Methods.POST}),"AbstractLoader.POST")},GET:{get:createjs.deprecate((function(){return createjs.Methods.GET}),"AbstractLoader.GET")},BINARY:{get:createjs.deprecate((function(){return createjs.Types.BINARY}),"AbstractLoader.BINARY")},CSS:{get:createjs.deprecate((function(){return createjs.Types.CSS}),"AbstractLoader.CSS")},FONT:{get:createjs.deprecate((function(){return createjs.Types.FONT}),"AbstractLoader.FONT")},FONTCSS:{get:createjs.deprecate((function(){return createjs.Types.FONTCSS}),"AbstractLoader.FONTCSS")},IMAGE:{get:createjs.deprecate((function(){return createjs.Types.IMAGE}),"AbstractLoader.IMAGE")},JAVASCRIPT:{get:createjs.deprecate((function(){return createjs.Types.JAVASCRIPT}),"AbstractLoader.JAVASCRIPT")},JSON:{get:createjs.deprecate((function(){return createjs.Types.JSON}),"AbstractLoader.JSON")},JSONP:{get:createjs.deprecate((function(){return createjs.Types.JSONP}),"AbstractLoader.JSONP")},MANIFEST:{get:createjs.deprecate((function(){return createjs.Types.MANIFEST}),"AbstractLoader.MANIFEST")},SOUND:{get:createjs.deprecate((function(){return createjs.Types.SOUND}),"AbstractLoader.SOUND")},VIDEO:{get:createjs.deprecate((function(){return createjs.Types.VIDEO}),"AbstractLoader.VIDEO")},SPRITESHEET:{get:createjs.deprecate((function(){return createjs.Types.SPRITESHEET}),"AbstractLoader.SPRITESHEET")},SVG:{get:createjs.deprecate((function(){return createjs.Types.SVG}),"AbstractLoader.SVG")},TEXT:{get:createjs.deprecate((function(){return createjs.Types.TEXT}),"AbstractLoader.TEXT")},XML:{get:createjs.deprecate((function(){return createjs.Types.XML}),"AbstractLoader.XML")}})}catch(t){}e.getItem=function(){return this._item},e.getResult=function(t){return t?this._rawResult:this._result},e.getTag=function(){return this._tag},e.setTag=function(t){this._tag=t},e.load=function(){this._createRequest(),this._request.on("complete",this,this),this._request.on("progress",this,this),this._request.on("loadStart",this,this),this._request.on("abort",this,this),this._request.on("timeout",this,this),this._request.on("error",this,this);var t=new createjs.Event("initialize");t.loader=this._request,this.dispatchEvent(t),this._request.load()},e.cancel=function(){this.canceled=!0,this.destroy()},e.destroy=function(){this._request&&(this._request.removeAllEventListeners(),this._request.destroy()),this._request=null,this._item=null,this._rawResult=null,this._result=null,this._loadItems=null,this.removeAllEventListeners()},e.getLoadedItems=function(){return this._loadedItems},e._createRequest=function(){this._preferXHR?this._request=new createjs.XHRRequest(this._item):this._request=new createjs.TagRequest(this._item,this._tag||this._createTag(),this._tagSrcAttribute)},e._createTag=function(t){return null},e._sendLoadStart=function(){this._isCanceled()||this.dispatchEvent("loadstart")},e._sendProgress=function(t){if(!this._isCanceled()){var e=null;"number"==typeof t?(this.progress=t,e=new createjs.ProgressEvent(this.progress)):(e=t,this.progress=t.loaded/t.total,e.progress=this.progress,(isNaN(this.progress)||this.progress==1/0)&&(this.progress=0)),this.hasEventListener("progress")&&this.dispatchEvent(e)}},e._sendComplete=function(){if(!this._isCanceled()){this.loaded=!0;var t=new createjs.Event("complete");t.rawResult=this._rawResult,null!=this._result&&(t.result=this._result),this.dispatchEvent(t)}},e._sendError=function(t){!this._isCanceled()&&this.hasEventListener("error")&&(null==t&&(t=new createjs.ErrorEvent("PRELOAD_ERROR_EMPTY")),this.dispatchEvent(t))},e._isCanceled=function(){return!(null!=window.createjs&&!this.canceled)},e.resultFormatter=null,e.handleEvent=function(t){switch(t.type){case"complete":this._rawResult=t.target._response;var e=this.resultFormatter&&this.resultFormatter(this);e instanceof Function?e.call(this,createjs.proxy(this._resultFormatSuccess,this),createjs.proxy(this._resultFormatFailed,this)):(this._result=e||this._rawResult,this._sendComplete());break;case"progress":this._sendProgress(t);break;case"error":this._sendError(t);break;case"loadstart":this._sendLoadStart();break;case"abort":case"timeout":this._isCanceled()||this.dispatchEvent(new createjs.ErrorEvent("PRELOAD_"+t.type.toUpperCase()+"_ERROR"))}},e._resultFormatSuccess=function(t){this._result=t,this._sendComplete()},e._resultFormatFailed=function(t){this._sendError(t)},e.toString=function(){return"[PreloadJS AbstractLoader]"},createjs.AbstractLoader=createjs.promote(t,"EventDispatcher")}(),window.createjs=window.createjs||{},function(){"use strict";function t(t,e,i){this.AbstractLoader_constructor(t,e,i),this.resultFormatter=this._formatResult,this._tagSrcAttribute="src",this.on("initialize",this._updateXHR,this)}var e=createjs.extend(t,createjs.AbstractLoader);e.load=function(){this._tag||(this._tag=this._createTag(this._item.src)),this._tag.preload="auto",this._tag.load(),this.AbstractLoader_load()},e._createTag=function(){},e._createRequest=function(){this._preferXHR?this._request=new createjs.XHRRequest(this._item):this._request=new createjs.MediaTagRequest(this._item,this._tag||this._createTag(),this._tagSrcAttribute)},e._updateXHR=function(t){t.loader.setResponseType&&t.loader.setResponseType("blob")},e._formatResult=function(t){if(this._tag.removeEventListener&&this._tag.removeEventListener("canplaythrough",this._loadedHandler),this._tag.onstalled=null,this._preferXHR){var e=window.URL||window.webkitURL,i=t.getResult(!0);t.getTag().src=e.createObjectURL(i)}return t.getTag()},createjs.AbstractMediaLoader=createjs.promote(t,"AbstractLoader")}(),window.createjs=window.createjs||{},function(){"use strict";var t=function(t){this._item=t},e=createjs.extend(t,createjs.EventDispatcher);e.load=function(){},e.destroy=function(){},e.cancel=function(){},createjs.AbstractRequest=createjs.promote(t,"EventDispatcher")}(),window.createjs=window.createjs||{},function(){"use strict";function t(t,e,i){this.AbstractRequest_constructor(t),this._tag=e,this._tagSrcAttribute=i,this._loadedHandler=createjs.proxy(this._handleTagComplete,this),this._addedToDOM=!1}var e=createjs.extend(t,createjs.AbstractRequest);e.load=function(){this._tag.onload=createjs.proxy(this._handleTagComplete,this),this._tag.onreadystatechange=createjs.proxy(this._handleReadyStateChange,this),this._tag.onerror=createjs.proxy(this._handleError,this);var t=new createjs.Event("initialize");t.loader=this._tag,this.dispatchEvent(t),this._loadTimeout=setTimeout(createjs.proxy(this._handleTimeout,this),this._item.loadTimeout),this._tag[this._tagSrcAttribute]=this._item.src,null==this._tag.parentNode&&(createjs.DomUtils.appendToBody(this._tag),this._addedToDOM=!0)},e.destroy=function(){this._clean(),this._tag=null,this.AbstractRequest_destroy()},e._handleReadyStateChange=function(){clearTimeout(this._loadTimeout);var t=this._tag;"loaded"!=t.readyState&&"complete"!=t.readyState||this._handleTagComplete()},e._handleError=function(){this._clean(),this.dispatchEvent("error")},e._handleTagComplete=function(){this._rawResult=this._tag,this._result=this.resultFormatter&&this.resultFormatter(this)||this._rawResult,this._clean(),this.dispatchEvent("complete")},e._handleTimeout=function(){this._clean(),this.dispatchEvent(new createjs.Event("timeout"))},e._clean=function(){this._tag.onload=null,this._tag.onreadystatechange=null,this._tag.onerror=null,this._addedToDOM&&null!=this._tag.parentNode&&this._tag.parentNode.removeChild(this._tag),clearTimeout(this._loadTimeout)},e._handleStalled=function(){},createjs.TagRequest=createjs.promote(t,"AbstractRequest")}(),window.createjs=window.createjs||{},function(){"use strict";function t(t,e,i){this.AbstractRequest_constructor(t),this._tag=e,this._tagSrcAttribute=i,this._loadedHandler=createjs.proxy(this._handleTagComplete,this)}var e=createjs.extend(t,createjs.TagRequest);e.load=function(){var t=createjs.proxy(this._handleStalled,this);this._stalledCallback=t;var e=createjs.proxy(this._handleProgress,this);this._handleProgress=e,this._tag.addEventListener("stalled",t),this._tag.addEventListener("progress",e),this._tag.addEventListener&&this._tag.addEventListener("canplaythrough",this._loadedHandler,!1),this.TagRequest_load()},e._handleReadyStateChange=function(){clearTimeout(this._loadTimeout);var t=this._tag;"loaded"!=t.readyState&&"complete"!=t.readyState||this._handleTagComplete()},e._handleStalled=function(){},e._handleProgress=function(t){if(t&&!(t.loaded>0&&0==t.total)){var e=new createjs.ProgressEvent(t.loaded,t.total);this.dispatchEvent(e)}},e._clean=function(){this._tag.removeEventListener&&this._tag.removeEventListener("canplaythrough",this._loadedHandler),this._tag.removeEventListener("stalled",this._stalledCallback),this._tag.removeEventListener("progress",this._progressCallback),this.TagRequest__clean()},createjs.MediaTagRequest=createjs.promote(t,"TagRequest")}(),window.createjs=window.createjs||{},function(){"use strict";function t(t){this.AbstractRequest_constructor(t),this._request=null,this._loadTimeout=null,this._xhrLevel=1,this._response=null,this._rawResponse=null,this._canceled=!1,this._handleLoadStartProxy=createjs.proxy(this._handleLoadStart,this),this._handleProgressProxy=createjs.proxy(this._handleProgress,this),this._handleAbortProxy=createjs.proxy(this._handleAbort,this),this._handleErrorProxy=createjs.proxy(this._handleError,this),this._handleTimeoutProxy=createjs.proxy(this._handleTimeout,this),this._handleLoadProxy=createjs.proxy(this._handleLoad,this),this._handleReadyStateChangeProxy=createjs.proxy(this._handleReadyStateChange,this),this._createXHR(t)}var e=createjs.extend(t,createjs.AbstractRequest);t.ACTIVEX_VERSIONS=["Msxml2.XMLHTTP.6.0","Msxml2.XMLHTTP.5.0","Msxml2.XMLHTTP.4.0","MSXML2.XMLHTTP.3.0","MSXML2.XMLHTTP","Microsoft.XMLHTTP"],e.getResult=function(t){return t&&this._rawResponse?this._rawResponse:this._response},e.cancel=function(){this.canceled=!0,this._clean(),this._request.abort()},e.load=function(){if(null!=this._request){null!=this._request.addEventListener?(this._request.addEventListener("loadstart",this._handleLoadStartProxy,!1),this._request.addEventListener("progress",this._handleProgressProxy,!1),this._request.addEventListener("abort",this._handleAbortProxy,!1),this._request.addEventListener("error",this._handleErrorProxy,!1),this._request.addEventListener("timeout",this._handleTimeoutProxy,!1),this._request.addEventListener("load",this._handleLoadProxy,!1),this._request.addEventListener("readystatechange",this._handleReadyStateChangeProxy,!1)):(this._request.onloadstart=this._handleLoadStartProxy,this._request.onprogress=this._handleProgressProxy,this._request.onabort=this._handleAbortProxy,this._request.onerror=this._handleErrorProxy,this._request.ontimeout=this._handleTimeoutProxy,this._request.onload=this._handleLoadProxy,this._request.onreadystatechange=this._handleReadyStateChangeProxy),1==this._xhrLevel&&(this._loadTimeout=setTimeout(createjs.proxy(this._handleTimeout,this),this._item.loadTimeout));try{this._item.values?this._request.send(createjs.URLUtils.formatQueryString(this._item.values)):this._request.send()}catch(t){this.dispatchEvent(new createjs.ErrorEvent("XHR_SEND",null,t))}}else this._handleError()},e.setResponseType=function(t){"blob"===t&&(t=window.URL?"blob":"arraybuffer",this._responseType=t),this._request.responseType=t},e.getAllResponseHeaders=function(){return this._request.getAllResponseHeaders instanceof Function?this._request.getAllResponseHeaders():null},e.getResponseHeader=function(t){return this._request.getResponseHeader instanceof Function?this._request.getResponseHeader(t):null},e._handleProgress=function(t){if(t&&!(t.loaded>0&&0==t.total)){var e=new createjs.ProgressEvent(t.loaded,t.total);this.dispatchEvent(e)}},e._handleLoadStart=function(t){clearTimeout(this._loadTimeout),this.dispatchEvent("loadstart")},e._handleAbort=function(t){this._clean(),this.dispatchEvent(new createjs.ErrorEvent("XHR_ABORTED",null,t))},e._handleError=function(t){this._clean(),this.dispatchEvent(new createjs.ErrorEvent(t.message))},e._handleReadyStateChange=function(t){4==this._request.readyState&&this._handleLoad()},e._handleLoad=function(t){if(!this.loaded){this.loaded=!0;var e=this._checkError();if(e)this._handleError(e);else{if(this._response=this._getResponse(),"arraybuffer"===this._responseType)try{this._response=new Blob([this._response])}catch(t){if(window.BlobBuilder=window.BlobBuilder||window.WebKitBlobBuilder||window.MozBlobBuilder||window.MSBlobBuilder,"TypeError"===t.name&&window.BlobBuilder){var i=new BlobBuilder;i.append(this._response),this._response=i.getBlob()}}this._clean(),this.dispatchEvent(new createjs.Event("complete"))}}},e._handleTimeout=function(t){this._clean(),this.dispatchEvent(new createjs.ErrorEvent("PRELOAD_TIMEOUT",null,t))},e._checkError=function(){var t=parseInt(this._request.status);return t>=400&&t<=599?new Error(t):0==t&&/^https?:/.test(location.protocol)?new Error(0):null},e._getResponse=function(){if(null!=this._response)return this._response;if(null!=this._request.response)return this._request.response;try{if(null!=this._request.responseText)return this._request.responseText}catch(t){}try{if(null!=this._request.responseXML)return this._request.responseXML}catch(t){}return null},e._createXHR=function(t){var e=createjs.URLUtils.isCrossDomain(t),i={},r=null;if(window.XMLHttpRequest)r=new XMLHttpRequest,e&&void 0===r.withCredentials&&window.XDomainRequest&&(r=new XDomainRequest);else{for(var n=0,a=s.ACTIVEX_VERSIONS.length;n<a;n++){var o=s.ACTIVEX_VERSIONS[n];try{r=new ActiveXObject(o);break}catch(t){}}if(null==r)return!1}null==t.mimeType&&createjs.RequestUtils.isText(t.type)&&(t.mimeType="text/plain; charset=utf-8"),t.mimeType&&r.overrideMimeType&&r.overrideMimeType(t.mimeType),this._xhrLevel="string"==typeof r.responseType?2:1;var c=null;if(c=t.method==createjs.Methods.GET?createjs.URLUtils.buildURI(t.src,t.values):t.src,r.open(t.method||createjs.Methods.GET,c,!0),e&&r instanceof XMLHttpRequest&&1==this._xhrLevel&&(i.Origin=location.origin),t.values&&t.method==createjs.Methods.POST&&(i["Content-Type"]="application/x-www-form-urlencoded"),e||i["X-Requested-With"]||(i["X-Requested-With"]="XMLHttpRequest"),t.headers)for(var u in t.headers)i[u]=t.headers[u];for(u in i)r.setRequestHeader(u,i[u]);return r instanceof XMLHttpRequest&&void 0!==t.withCredentials&&(r.withCredentials=t.withCredentials),this._request=r,!0},e._clean=function(){clearTimeout(this._loadTimeout),null!=this._request.removeEventListener?(this._request.removeEventListener("loadstart",this._handleLoadStartProxy),this._request.removeEventListener("progress",this._handleProgressProxy),this._request.removeEventListener("abort",this._handleAbortProxy),this._request.removeEventListener("error",this._handleErrorProxy),this._request.removeEventListener("timeout",this._handleTimeoutProxy),this._request.removeEventListener("load",this._handleLoadProxy),this._request.removeEventListener("readystatechange",this._handleReadyStateChangeProxy)):(this._request.onloadstart=null,this._request.onprogress=null,this._request.onabort=null,this._request.onerror=null,this._request.ontimeout=null,this._request.onload=null,this._request.onreadystatechange=null)},e.toString=function(){return"[PreloadJS XHRRequest]"},createjs.XHRRequest=createjs.promote(t,"AbstractRequest")}(),window.createjs=window.createjs||{},function(){"use strict";function t(t,e){this.AbstractMediaLoader_constructor(t,e,createjs.Types.SOUND),createjs.DomUtils.isAudioTag(t)||createjs.DomUtils.isAudioTag(t.src)?this._tag=t:createjs.DomUtils.isAudioTag(t.tag)&&(this._tag=createjs.DomUtils.isAudioTag(t)?t:t.src),null!=this._tag&&(this._preferXHR=!1)}var e=createjs.extend(t,createjs.AbstractMediaLoader);t.canLoadItem=function(t){return t.type==createjs.Types.SOUND},e._createTag=function(t){var e=createjs.Elements.audio();return e.autoplay=!1,e.preload="none",e.src=t,e},createjs.SoundLoader=createjs.promote(t,"AbstractMediaLoader")}(),window.createjs=window.createjs||{},function(){"use strict";var t=function(){this.interrupt=null,this.delay=null,this.offset=null,this.loop=null,this.volume=null,this.pan=null,this.startTime=null,this.duration=null},e=t.prototype={},i=t;i.create=function(t){if("string"==typeof t)return console&&(console.warn||console.log)("Deprecated behaviour. Sound.play takes a configuration object instead of individual arguments. See docs for info."),(new createjs.PlayPropsConfig).set({interrupt:t});if(null==t||t instanceof i||t instanceof Object)return(new createjs.PlayPropsConfig).set(t);if(null==t)throw new Error("PlayProps configuration not recognized.")},e.set=function(t){if(null!=t)for(var e in t)this[e]=t[e];return this},e.toString=function(){return"[PlayPropsConfig]"},createjs.PlayPropsConfig=i}(),window.createjs=window.createjs||{},function(){"use strict";function t(){throw"Sound cannot be instantiated"}var e=t;function i(t,e){this.init(t,e)}e.INTERRUPT_ANY="any",e.INTERRUPT_EARLY="early",e.INTERRUPT_LATE="late",e.INTERRUPT_NONE="none",e.PLAY_INITED="playInited",e.PLAY_SUCCEEDED="playSucceeded",e.PLAY_INTERRUPTED="playInterrupted",e.PLAY_FINISHED="playFinished",e.PLAY_FAILED="playFailed",e.SUPPORTED_EXTENSIONS=["mp3","ogg","opus","mpeg","wav","m4a","mp4","aiff","wma","mid"],e.EXTENSION_MAP={m4a:"mp4"},e.FILE_PATTERN=/^(?:(\w+:)\/{2}(\w+(?:\.\w+)*\/?))?([/.]*?(?:[^?]+)?\/)?((?:[^/?]+)\.(\w+))(?:\?(\S+)?)?$/,e.defaultInterruptBehavior=e.INTERRUPT_NONE,e.alternateExtensions=[],e.activePlugin=null,e._masterVolume=1,e._getMasterVolume=function(){return this._masterVolume},e.getVolume=createjs.deprecate(e._getMasterVolume,"Sound.getVolume"),e._setMasterVolume=function(t){if(null!=Number(t)&&(t=Math.max(0,Math.min(1,t)),e._masterVolume=t,!this.activePlugin||!this.activePlugin.setVolume||!this.activePlugin.setVolume(t)))for(var i=this._instances,r=0,n=i.length;r<n;r++)i[r].setMasterVolume(t)},e.setVolume=createjs.deprecate(e._setMasterVolume,"Sound.setVolume"),e._masterMute=!1,e._getMute=function(){return this._masterMute},e.getMute=createjs.deprecate(e._getMute,"Sound.getMute"),e._setMute=function(t){if(null!=t&&(this._masterMute=t,!this.activePlugin||!this.activePlugin.setMute||!this.activePlugin.setMute(t)))for(var e=this._instances,i=0,r=e.length;i<r;i++)e[i].setMasterMute(t)},e.setMute=createjs.deprecate(e._setMute,"Sound.setMute"),e._getCapabilities=function(){return null==e.activePlugin?null:e.activePlugin._capabilities},e.getCapabilities=createjs.deprecate(e._getCapabilities,"Sound.getCapabilities"),Object.defineProperties(e,{volume:{get:e._getMasterVolume,set:e._setMasterVolume},muted:{get:e._getMute,set:e._setMute},capabilities:{get:e._getCapabilities}}),e._pluginsRegistered=!1,e._lastID=0,e._instances=[],e._idHash={},e._preloadHash={},e._defaultPlayPropsHash={},e.addEventListener=null,e.removeEventListener=null,e.removeAllEventListeners=null,e.dispatchEvent=null,e.hasEventListener=null,e._listeners=null,createjs.EventDispatcher.initialize(e),e.getPreloadHandlers=function(){return{callback:createjs.proxy(e.initLoad,e),types:["sound"],extensions:e.SUPPORTED_EXTENSIONS}},e._handleLoadComplete=function(t){var i=t.target.getItem().src;if(e._preloadHash[i])for(var r=0,n=e._preloadHash[i].length;r<n;r++){var s=e._preloadHash[i][r];if(e._preloadHash[i][r]=!0,e.hasEventListener("fileload"))(t=new createjs.Event("fileload")).src=s.src,t.id=s.id,t.data=s.data,t.sprite=s.sprite,e.dispatchEvent(t)}},e._handleLoadError=function(t){var i=t.target.getItem().src;if(e._preloadHash[i])for(var r=0,n=e._preloadHash[i].length;r<n;r++){var s=e._preloadHash[i][r];if(e._preloadHash[i][r]=!1,e.hasEventListener("fileerror"))(t=new createjs.Event("fileerror")).src=s.src,t.id=s.id,t.data=s.data,t.sprite=s.sprite,e.dispatchEvent(t)}},e._registerPlugin=function(t){return!!t.isSupported()&&(e.activePlugin=new t,!0)},e.registerPlugins=function(t){e._pluginsRegistered=!0;for(var i=0,r=t.length;i<r;i++)if(e._registerPlugin(t[i]))return!0;return!1},e.initializeDefaultPlugins=function(){return null!=e.activePlugin||!e._pluginsRegistered&&!!e.registerPlugins([createjs.WebAudioPlugin,createjs.HTMLAudioPlugin])},e.isReady=function(){return null!=e.activePlugin},e.initLoad=function(t){return"video"==t.type||e._registerSound(t)},e._registerSound=function(t){if(!e.initializeDefaultPlugins())return!1;var r;if(t.src instanceof Object?(r=e._parseSrc(t.src)).src=t.path+r.src:r=e._parsePath(t.src),null==r)return!1;t.src=r.src,t.type="sound";var n=t.data,s=null;if(null!=n&&(isNaN(n.channels)?isNaN(n)||(s=parseInt(n)):s=parseInt(n.channels),n.audioSprite))for(var a,o=n.audioSprite.length;o--;)a=n.audioSprite[o],e._idHash[a.id]={src:t.src,startTime:parseInt(a.startTime),duration:parseInt(a.duration)},a.defaultPlayProps&&(e._defaultPlayPropsHash[a.id]=createjs.PlayPropsConfig.create(a.defaultPlayProps));null!=t.id&&(e._idHash[t.id]={src:t.src});var c=e.activePlugin.register(t);return i.create(t.src,s),null!=n&&isNaN(n)?t.data.channels=s||i.maxPerChannel():t.data=s||i.maxPerChannel(),c.type&&(t.type=c.type),t.defaultPlayProps&&(e._defaultPlayPropsHash[t.src]=createjs.PlayPropsConfig.create(t.defaultPlayProps)),c},e.registerSound=function(t,i,r,n,s){var a={src:t,id:i,data:r,defaultPlayProps:s};t instanceof Object&&t.src&&(n=i,a=t),(a=createjs.LoadItem.create(a)).path=n,null==n||a.src instanceof Object||(a.src=n+a.src);var o=e._registerSound(a);if(!o)return!1;if(e._preloadHash[a.src]||(e._preloadHash[a.src]=[]),e._preloadHash[a.src].push(a),1==e._preloadHash[a.src].length)o.on("complete",this._handleLoadComplete,this),o.on("error",this._handleLoadError,this),e.activePlugin.preload(o);else if(1==e._preloadHash[a.src][0])return!0;return a},e.registerSounds=function(t,e){var i=[];t.path&&(e?e+=t.path:e=t.path,t=t.manifest);for(var r=0,n=t.length;r<n;r++)i[r]=createjs.Sound.registerSound(t[r].src,t[r].id,t[r].data,e,t[r].defaultPlayProps);return i},e.removeSound=function(t,r){if(null==e.activePlugin)return!1;var n;if(t instanceof Object&&t.src&&(t=t.src),t instanceof Object?n=e._parseSrc(t):(t=e._getSrcById(t).src,n=e._parsePath(t)),null==n)return!1;for(var s in t=n.src,null!=r&&(t=r+t),e._idHash)e._idHash[s].src==t&&delete e._idHash[s];return i.removeSrc(t),delete e._preloadHash[t],e.activePlugin.removeSound(t),!0},e.removeSounds=function(t,e){var i=[];t.path&&(e?e+=t.path:e=t.path,t=t.manifest);for(var r=0,n=t.length;r<n;r++)i[r]=createjs.Sound.removeSound(t[r].src,e);return i},e.removeAllSounds=function(){e._idHash={},e._preloadHash={},i.removeAll(),e.activePlugin&&e.activePlugin.removeAllSounds()},e.loadComplete=function(t){if(!e.isReady())return!1;var i=e._parsePath(t);return t=i?e._getSrcById(i.src).src:e._getSrcById(t).src,null!=e._preloadHash[t]&&1==e._preloadHash[t][0]},e._parsePath=function(t){"string"!=typeof t&&(t=t.toString());var i=t.match(e.FILE_PATTERN);if(null==i)return!1;for(var r=i[4],n=i[5],s=e.capabilities,a=0;!s[n];)if(n=e.alternateExtensions[a++],a>e.alternateExtensions.length)return null;return{name:r,src:t=t.replace("."+i[5],"."+n),extension:n}},e._parseSrc=function(t){var i={name:void 0,src:void 0,extension:void 0},r=e.capabilities;for(var n in t)if(t.hasOwnProperty(n)&&r[n]){i.src=t[n],i.extension=n;break}if(!i.src)return!1;var s=i.src.lastIndexOf("/");return i.name=-1!=s?i.src.slice(s+1):i.src,i},e.play=function(t,i){var r=createjs.PlayPropsConfig.create(i),n=e.createInstance(t,r.startTime,r.duration);return e._playInstance(n,r)||n._playFailed(),n},e.createInstance=function(t,r,n){if(!e.initializeDefaultPlugins())return new createjs.DefaultSoundInstance(t,r,n);var s=e._defaultPlayPropsHash[t];t=e._getSrcById(t);var a=e._parsePath(t.src),o=null;return null!=a&&null!=a.src?(i.create(a.src),null==r&&(r=t.startTime),o=e.activePlugin.create(a.src,r,n||t.duration),(s=s||e._defaultPlayPropsHash[a.src])&&o.applyPlayProps(s)):o=new createjs.DefaultSoundInstance(t,r,n),o.uniqueId=e._lastID++,o},e.stop=function(){for(var t=this._instances,e=t.length;e--;)t[e].stop()},e.setDefaultPlayProps=function(t,i){t=e._getSrcById(t),e._defaultPlayPropsHash[e._parsePath(t.src).src]=createjs.PlayPropsConfig.create(i)},e.getDefaultPlayProps=function(t){return t=e._getSrcById(t),e._defaultPlayPropsHash[e._parsePath(t.src).src]},e._playInstance=function(t,i){var r=e._defaultPlayPropsHash[t.src]||{};if(null==i.interrupt&&(i.interrupt=r.interrupt||e.defaultInterruptBehavior),null==i.delay&&(i.delay=r.delay||0),null==i.offset&&(i.offset=t.position),null==i.loop&&(i.loop=t.loop),null==i.volume&&(i.volume=t.volume),null==i.pan&&(i.pan=t.pan),0==i.delay){if(!e._beginPlaying(t,i))return!1}else{var n=setTimeout((function(){e._beginPlaying(t,i)}),i.delay);t.delayTimeoutId=n}return this._instances.push(t),!0},e._beginPlaying=function(t,e){if(!i.add(t,e.interrupt))return!1;if(!t._beginPlaying(e)){var r=createjs.indexOf(this._instances,t);return r>-1&&this._instances.splice(r,1),!1}return!0},e._getSrcById=function(t){return e._idHash[t]||{src:t}},e._playFinished=function(t){i.remove(t);var e=createjs.indexOf(this._instances,t);e>-1&&this._instances.splice(e,1)},createjs.Sound=t,i.channels={},i.create=function(t,e){return null==i.get(t)&&(i.channels[t]=new i(t,e),!0)},i.removeSrc=function(t){var e=i.get(t);return null!=e&&(e._removeAll(),delete i.channels[t],!0)},i.removeAll=function(){for(var t in i.channels)i.channels[t]._removeAll();i.channels={}},i.add=function(t,e){var r=i.get(t.src);return null!=r&&r._add(t,e)},i.remove=function(t){var e=i.get(t.src);return null!=e&&(e._remove(t),!0)},i.maxPerChannel=function(){return r.maxDefault},i.get=function(t){return i.channels[t]};var r=i.prototype;r.constructor=i,r.src=null,r.max=null,r.maxDefault=100,r.length=0,r.init=function(t,e){this.src=t,this.max=e||this.maxDefault,-1==this.max&&(this.max=this.maxDefault),this._instances=[]},r._get=function(t){return this._instances[t]},r._add=function(t,e){return!!this._getSlot(e,t)&&(this._instances.push(t),this.length++,!0)},r._remove=function(t){var e=createjs.indexOf(this._instances,t);return-1!=e&&(this._instances.splice(e,1),this.length--,!0)},r._removeAll=function(){for(var t=this.length-1;t>=0;t--)this._instances[t].stop()},r._getSlot=function(e,i){var r,n;if(e!=t.INTERRUPT_NONE&&null==(n=this._get(0)))return!0;for(var s=0,a=this.max;s<a;s++){if(null==(r=this._get(s)))return!0;if(r.playState==t.PLAY_FINISHED||r.playState==t.PLAY_INTERRUPTED||r.playState==t.PLAY_FAILED){n=r;break}e!=t.INTERRUPT_NONE&&((e==t.INTERRUPT_EARLY&&r.position<n.position||e==t.INTERRUPT_LATE&&r.position>n.position)&&(n=r))}return null!=n&&(n._interrupt(),this._remove(n),!0)},r.toString=function(){return"[Sound SoundChannel]"}}(),window.createjs=window.createjs||{},function(){"use strict";var t=function(t,e,i,r){this.EventDispatcher_constructor(),this.src=t,this.uniqueId=-1,this.playState=null,this.delayTimeoutId=null,this._volume=1,Object.defineProperty(this,"volume",{get:this._getVolume,set:this._setVolume}),this._pan=0,Object.defineProperty(this,"pan",{get:this._getPan,set:this._setPan}),this._startTime=Math.max(0,e||0),Object.defineProperty(this,"startTime",{get:this._getStartTime,set:this._setStartTime}),this._duration=Math.max(0,i||0),Object.defineProperty(this,"duration",{get:this._getDuration,set:this._setDuration}),this._playbackResource=null,Object.defineProperty(this,"playbackResource",{get:this._getPlaybackResource,set:this._setPlaybackResource}),!1!==r&&!0!==r&&this._setPlaybackResource(r),this._position=0,Object.defineProperty(this,"position",{get:this._getPosition,set:this._setPosition}),this._loop=0,Object.defineProperty(this,"loop",{get:this._getLoop,set:this._setLoop}),this._muted=!1,Object.defineProperty(this,"muted",{get:this._getMuted,set:this._setMuted}),this._paused=!1,Object.defineProperty(this,"paused",{get:this._getPaused,set:this._setPaused})},e=createjs.extend(t,createjs.EventDispatcher);e.play=function(t){var e=createjs.PlayPropsConfig.create(t);return this.playState==createjs.Sound.PLAY_SUCCEEDED?(this.applyPlayProps(e),void(this._paused&&this._setPaused(!1))):(this._cleanUp(),createjs.Sound._playInstance(this,e),this)},e.stop=function(){return this._position=0,this._paused=!1,this._handleStop(),this._cleanUp(),this.playState=createjs.Sound.PLAY_FINISHED,this},e.destroy=function(){this._cleanUp(),this.src=null,this.playbackResource=null,this.removeAllEventListeners()},e.applyPlayProps=function(t){return null!=t.offset&&this._setPosition(t.offset),null!=t.loop&&this._setLoop(t.loop),null!=t.volume&&this._setVolume(t.volume),null!=t.pan&&this._setPan(t.pan),null!=t.startTime&&(this._setStartTime(t.startTime),this._setDuration(t.duration)),this},e.toString=function(){return"[AbstractSoundInstance]"},e._getPaused=function(){return this._paused},e._setPaused=function(t){if(!(!0!==t&&!1!==t||this._paused==t||1==t&&this.playState!=createjs.Sound.PLAY_SUCCEEDED))return this._paused=t,t?this._pause():this._resume(),clearTimeout(this.delayTimeoutId),this},e._setVolume=function(t){return t==this._volume||(this._volume=Math.max(0,Math.min(1,t)),this._muted||this._updateVolume()),this},e._getVolume=function(){return this._volume},e._setMuted=function(t){if(!0===t||!1===t)return this._muted=t,this._updateVolume(),this},e._getMuted=function(){return this._muted},e._setPan=function(t){return t==this._pan||(this._pan=Math.max(-1,Math.min(1,t)),this._updatePan()),this},e._getPan=function(){return this._pan},e._getPosition=function(){return this._paused||this.playState!=createjs.Sound.PLAY_SUCCEEDED||(this._position=this._calculateCurrentPosition()),this._position},e._setPosition=function(t){return this._position=Math.max(0,t),this.playState==createjs.Sound.PLAY_SUCCEEDED&&this._updatePosition(),this},e._getStartTime=function(){return this._startTime},e._setStartTime=function(t){return t==this._startTime||(this._startTime=Math.max(0,t||0),this._updateStartTime()),this},e._getDuration=function(){return this._duration},e._setDuration=function(t){return t==this._duration||(this._duration=Math.max(0,t||0),this._updateDuration()),this},e._setPlaybackResource=function(t){return this._playbackResource=t,0==this._duration&&this._playbackResource&&this._setDurationFromSource(),this},e._getPlaybackResource=function(){return this._playbackResource},e._getLoop=function(){return this._loop},e._setLoop=function(t){null!=this._playbackResource&&(0!=this._loop&&0==t?this._removeLooping(t):0==this._loop&&0!=t&&this._addLooping(t)),this._loop=t},e._sendEvent=function(t){var e=new createjs.Event(t);this.dispatchEvent(e)},e._cleanUp=function(){clearTimeout(this.delayTimeoutId),this._handleCleanUp(),this._paused=!1,createjs.Sound._playFinished(this)},e._interrupt=function(){this._cleanUp(),this.playState=createjs.Sound.PLAY_INTERRUPTED,this._sendEvent("interrupted")},e._beginPlaying=function(t){return this._setPosition(t.offset),this._setLoop(t.loop),this._setVolume(t.volume),this._setPan(t.pan),null!=t.startTime&&(this._setStartTime(t.startTime),this._setDuration(t.duration)),null!=this._playbackResource&&this._position<this._duration?(this._paused=!1,this._handleSoundReady(),this.playState=createjs.Sound.PLAY_SUCCEEDED,this._sendEvent("succeeded"),!0):(this._playFailed(),!1)},e._playFailed=function(){this._cleanUp(),this.playState=createjs.Sound.PLAY_FAILED,this._sendEvent("failed")},e._handleSoundComplete=function(t){if(this._position=0,0!=this._loop)return this._loop--,this._handleLoop(),void this._sendEvent("loop");this._cleanUp(),this.playState=createjs.Sound.PLAY_FINISHED,this._sendEvent("complete")},e._handleSoundReady=function(){},e._updateVolume=function(){},e._updatePan=function(){},e._updateStartTime=function(){},e._updateDuration=function(){},e._setDurationFromSource=function(){},e._calculateCurrentPosition=function(){},e._updatePosition=function(){},e._removeLooping=function(t){},e._addLooping=function(t){},e._pause=function(){},e._resume=function(){},e._handleStop=function(){},e._handleCleanUp=function(){},e._handleLoop=function(){},createjs.AbstractSoundInstance=createjs.promote(t,"EventDispatcher"),createjs.DefaultSoundInstance=createjs.AbstractSoundInstance}(),window.createjs=window.createjs||{},function(){"use strict";var t=function(){this._capabilities=null,this._loaders={},this._audioSources={},this._soundInstances={},this._volume=1,this._loaderClass,this._soundInstanceClass},e=t.prototype;t._capabilities=null,t.isSupported=function(){return!0},e.register=function(t){var e=this._loaders[t.src];return e&&!e.canceled?this._loaders[t.src]:(this._audioSources[t.src]=!0,this._soundInstances[t.src]=[],(e=new this._loaderClass(t)).on("complete",this._handlePreloadComplete,this),this._loaders[t.src]=e,e)},e.preload=function(t){t.on("error",this._handlePreloadError,this),t.load()},e.isPreloadStarted=function(t){return null!=this._audioSources[t]},e.isPreloadComplete=function(t){return!(null==this._audioSources[t]||1==this._audioSources[t])},e.removeSound=function(t){if(this._soundInstances[t]){for(var e=this._soundInstances[t].length;e--;){this._soundInstances[t][e].destroy()}delete this._soundInstances[t],delete this._audioSources[t],this._loaders[t]&&this._loaders[t].destroy(),delete this._loaders[t]}},e.removeAllSounds=function(){for(var t in this._audioSources)this.removeSound(t)},e.create=function(t,e,i){this.isPreloadStarted(t)||this.preload(this.register(t));var r=new this._soundInstanceClass(t,e,i,this._audioSources[t]);return this._soundInstances[t]&&this._soundInstances[t].push(r),r.setMasterVolume&&r.setMasterVolume(createjs.Sound.volume),r.setMasterMute&&r.setMasterMute(createjs.Sound.muted),r},e.setVolume=function(t){return this._volume=t,this._updateVolume(),!0},e.getVolume=function(){return this._volume},e.setMute=function(t){return this._updateVolume(),!0},e.toString=function(){return"[AbstractPlugin]"},e._handlePreloadComplete=function(t){var e=t.target.getItem().src;if(this._audioSources[e]=t.result,this._soundInstances[e])for(var i=0,r=this._soundInstances[e].length;i<r;i++){this._soundInstances[e][i].playbackResource=this._audioSources[e],this._soundInstances[e]=null}},e._handlePreloadError=function(t){},e._updateVolume=function(){},createjs.AbstractPlugin=t}(),window.createjs=window.createjs||{},function(){"use strict";function t(t){this.AbstractLoader_constructor(t,!0,createjs.Types.SOUND)}var e=createjs.extend(t,createjs.AbstractLoader);t.context=null,e.toString=function(){return"[WebAudioLoader]"},e._createRequest=function(){this._request=new createjs.XHRRequest(this._item,!1),this._request.setResponseType("arraybuffer")},e._sendComplete=function(e){t.context.decodeAudioData(this._rawResult,createjs.proxy(this._handleAudioDecoded,this),createjs.proxy(this._sendError,this))},e._handleAudioDecoded=function(t){this._result=t,this.AbstractLoader__sendComplete()},createjs.WebAudioLoader=createjs.promote(t,"AbstractLoader")}(),window.createjs=window.createjs||{},function(){"use strict";function t(t,e,r,n){this.AbstractSoundInstance_constructor(t,e,r,n),this.gainNode=i.context.createGain(),this.panNode=i.context.createPanner(),this.panNode.panningModel=i._panningModel,this.panNode.connect(this.gainNode),this._updatePan(),this.sourceNode=null,this._soundCompleteTimeout=null,this._sourceNodeNext=null,this._playbackStartTime=0,this._endedHandler=createjs.proxy(this._handleSoundComplete,this)}var e=createjs.extend(t,createjs.AbstractSoundInstance),i=t;i.context=null,i._scratchBuffer=null,i.destinationNode=null,i._panningModel="equalpower",e.destroy=function(){this.AbstractSoundInstance_destroy(),this.panNode.disconnect(0),this.panNode=null,this.gainNode.disconnect(0),this.gainNode=null},e.toString=function(){return"[WebAudioSoundInstance]"},e._updatePan=function(){this.panNode.setPosition(this._pan,0,-.5)},e._removeLooping=function(t){this._sourceNodeNext=this._cleanUpAudioNode(this._sourceNodeNext)},e._addLooping=function(t){this.playState==createjs.Sound.PLAY_SUCCEEDED&&(this._sourceNodeNext=this._createAndPlayAudioNode(this._playbackStartTime,0))},e._setDurationFromSource=function(){this._duration=1e3*this.playbackResource.duration},e._handleCleanUp=function(){this.sourceNode&&this.playState==createjs.Sound.PLAY_SUCCEEDED&&(this.sourceNode=this._cleanUpAudioNode(this.sourceNode),this._sourceNodeNext=this._cleanUpAudioNode(this._sourceNodeNext)),0!=this.gainNode.numberOfOutputs&&this.gainNode.disconnect(0),clearTimeout(this._soundCompleteTimeout),this._playbackStartTime=0},e._cleanUpAudioNode=function(t){if(t){if(t.stop(0),t.disconnect(0),createjs.BrowserDetect.isIOS)try{t.buffer=i._scratchBuffer}catch(t){}t=null}return t},e._handleSoundReady=function(t){this.gainNode.connect(i.destinationNode);var e=.001*this._duration,r=Math.min(.001*Math.max(0,this._position),e);this.sourceNode=this._createAndPlayAudioNode(i.context.currentTime-e,r),this._playbackStartTime=this.sourceNode.startTime-r,this._soundCompleteTimeout=setTimeout(this._endedHandler,1e3*(e-r)),0!=this._loop&&(this._sourceNodeNext=this._createAndPlayAudioNode(this._playbackStartTime,0))},e._createAndPlayAudioNode=function(t,e){var r=i.context.createBufferSource();r.buffer=this.playbackResource,r.connect(this.panNode);var n=.001*this._duration;return r.startTime=t+n,r.start(r.startTime,e+.001*this._startTime,n-e),r},e._pause=function(){this._position=1e3*(i.context.currentTime-this._playbackStartTime),this.sourceNode=this._cleanUpAudioNode(this.sourceNode),this._sourceNodeNext=this._cleanUpAudioNode(this._sourceNodeNext),0!=this.gainNode.numberOfOutputs&&this.gainNode.disconnect(0),clearTimeout(this._soundCompleteTimeout)},e._resume=function(){this._handleSoundReady()},e._updateVolume=function(){var t=this._muted?0:this._volume;t!=this.gainNode.gain.value&&(this.gainNode.gain.value=t)},e._calculateCurrentPosition=function(){return 1e3*(i.context.currentTime-this._playbackStartTime)},e._updatePosition=function(){this.sourceNode=this._cleanUpAudioNode(this.sourceNode),this._sourceNodeNext=this._cleanUpAudioNode(this._sourceNodeNext),clearTimeout(this._soundCompleteTimeout),this._paused||this._handleSoundReady()},e._handleLoop=function(){this._cleanUpAudioNode(this.sourceNode),this.sourceNode=this._sourceNodeNext,this._playbackStartTime=this.sourceNode.startTime,this._sourceNodeNext=this._createAndPlayAudioNode(this._playbackStartTime,0),this._soundCompleteTimeout=setTimeout(this._endedHandler,this._duration)},e._updateDuration=function(){this.playState==createjs.Sound.PLAY_SUCCEEDED&&(this._pause(),this._resume())},createjs.WebAudioSoundInstance=createjs.promote(t,"AbstractSoundInstance")}(),window.createjs=window.createjs||{},function(){"use strict";function t(){this.AbstractPlugin_constructor(),this._panningModel=i._panningModel,this.context=i.context,this.dynamicsCompressorNode=this.context.createDynamicsCompressor(),this.dynamicsCompressorNode.connect(this.context.destination),this.gainNode=this.context.createGain(),this.gainNode.connect(this.dynamicsCompressorNode),createjs.WebAudioSoundInstance.destinationNode=this.gainNode,this._capabilities=i._capabilities,this._loaderClass=createjs.WebAudioLoader,this._soundInstanceClass=createjs.WebAudioSoundInstance,this._addPropsToClasses()}var e=createjs.extend(t,createjs.AbstractPlugin),i=t;i._capabilities=null,i._panningModel="equalpower",i.context=null,i._scratchBuffer=null,i._unlocked=!1,i.DEFAULT_SAMPLE_RATE=44100,i.isSupported=function(){var t=createjs.BrowserDetect.isIOS||createjs.BrowserDetect.isAndroid||createjs.BrowserDetect.isBlackberry;return!("file:"==location.protocol&&!t&&!this._isFileXHRSupported())&&(i._generateCapabilities(),null!=i.context)},i.playEmptySound=function(){if(null!=i.context){var t=i.context.createBufferSource();t.buffer=i._scratchBuffer,t.connect(i.context.destination),t.start(0,0,0)}},i._isFileXHRSupported=function(){var t=!0,e=new XMLHttpRequest;try{e.open("GET","WebAudioPluginTest.fail",!1)}catch(e){return t=!1}e.onerror=function(){t=!1},e.onload=function(){t=404==this.status||200==this.status||0==this.status&&""!=this.response};try{e.send()}catch(e){t=!1}return t},i._generateCapabilities=function(){if(null==i._capabilities){var t=document.createElement("audio");if(null==t.canPlayType)return null;if(null==i.context&&(i.context=i._createAudioContext(),null==i.context))return null;null==i._scratchBuffer&&(i._scratchBuffer=i.context.createBuffer(1,1,22050)),i._compatibilitySetUp(),"ontouchstart"in window&&"running"!=i.context.state&&(i._unlock(),document.addEventListener("mousedown",i._unlock,!0),document.addEventListener("touchstart",i._unlock,!0),document.addEventListener("touchend",i._unlock,!0)),i._capabilities={panning:!0,volume:!0,tracks:-1};for(var e=createjs.Sound.SUPPORTED_EXTENSIONS,r=createjs.Sound.EXTENSION_MAP,n=0,s=e.length;n<s;n++){var a=e[n],o=r[a]||a;i._capabilities[a]="no"!=t.canPlayType("audio/"+a)&&""!=t.canPlayType("audio/"+a)||"no"!=t.canPlayType("audio/"+o)&&""!=t.canPlayType("audio/"+o)}i.context.destination.numberOfChannels<2&&(i._capabilities.panning=!1)}},i._createAudioContext=function(){var t=window.AudioContext||window.webkitAudioContext;if(null==t)return null;var e=new t;if(/(iPhone|iPad)/i.test(navigator.userAgent)&&e.sampleRate!==i.DEFAULT_SAMPLE_RATE){var r=e.createBuffer(1,1,i.DEFAULT_SAMPLE_RATE),n=e.createBufferSource();n.buffer=r,n.connect(e.destination),n.start(0),n.disconnect(),e.close(),e=new t}return e},i._compatibilitySetUp=function(){if(i._panningModel="equalpower",!i.context.createGain){i.context.createGain=i.context.createGainNode;var t=i.context.createBufferSource();t.__proto__.start=t.__proto__.noteGrainOn,t.__proto__.stop=t.__proto__.noteOff,i._panningModel=0}},i._unlock=function(){i._unlocked||(i.playEmptySound(),"running"==i.context.state&&(document.removeEventListener("mousedown",i._unlock,!0),document.removeEventListener("touchend",i._unlock,!0),document.removeEventListener("touchstart",i._unlock,!0),i._unlocked=!0))},e.toString=function(){return"[WebAudioPlugin]"},e._addPropsToClasses=function(){var t=this._soundInstanceClass;t.context=this.context,t._scratchBuffer=i._scratchBuffer,t.destinationNode=this.gainNode,t._panningModel=this._panningModel,this._loaderClass.context=this.context},e._updateVolume=function(){var t=createjs.Sound._masterMute?0:this._volume;t!=this.gainNode.gain.value&&(this.gainNode.gain.value=t)},createjs.WebAudioPlugin=createjs.promote(t,"AbstractPlugin")}(),window.createjs=window.createjs||{},function(){"use strict";function t(){throw"HTMLAudioTagPool cannot be instantiated"}var e=t;function i(t){this._tags=[]}e._tags={},e._tagPool=new i,e._tagUsed={},e.get=function(t){var i=e._tags[t];return null==i?(i=e._tags[t]=e._tagPool.get()).src=t:e._tagUsed[t]?(i=e._tagPool.get()).src=t:e._tagUsed[t]=!0,i},e.set=function(t,i){i==e._tags[t]?e._tagUsed[t]=!1:e._tagPool.set(i)},e.remove=function(t){var i=e._tags[t];return null!=i&&(e._tagPool.set(i),delete e._tags[t],delete e._tagUsed[t],!0)},e.getDuration=function(t){var i=e._tags[t];return null!=i&&i.duration?1e3*i.duration:0},createjs.HTMLAudioTagPool=t;var r=i.prototype;r.constructor=i,r.get=function(){var t;return null==(t=0==this._tags.length?this._createTag():this._tags.pop()).parentNode&&document.body.appendChild(t),t},r.set=function(t){-1==createjs.indexOf(this._tags,t)&&(this._tags.src=null,this._tags.push(t))},r.toString=function(){return"[TagPool]"},r._createTag=function(){var t=document.createElement("audio");return t.autoplay=!1,t.preload="none",t}}(),window.createjs=window.createjs||{},function(){"use strict";function t(t,e,i,r){this.AbstractSoundInstance_constructor(t,e,i,r),this._audioSpriteStopTime=null,this._delayTimeoutId=null,this._endedHandler=createjs.proxy(this._handleSoundComplete,this),this._readyHandler=createjs.proxy(this._handleTagReady,this),this._stalledHandler=createjs.proxy(this._playFailed,this),this._audioSpriteEndHandler=createjs.proxy(this._handleAudioSpriteLoop,this),this._loopHandler=createjs.proxy(this._handleSoundComplete,this),i?this._audioSpriteStopTime=.001*(e+i):this._duration=createjs.HTMLAudioTagPool.getDuration(this.src)}var e=createjs.extend(t,createjs.AbstractSoundInstance);e.setMasterVolume=function(t){this._updateVolume()},e.setMasterMute=function(t){this._updateVolume()},e.toString=function(){return"[HTMLAudioSoundInstance]"},e._removeLooping=function(){null!=this._playbackResource&&(this._playbackResource.loop=!1,this._playbackResource.removeEventListener(createjs.HTMLAudioPlugin._AUDIO_SEEKED,this._loopHandler,!1))},e._addLooping=function(){null==this._playbackResource||this._audioSpriteStopTime||(this._playbackResource.addEventListener(createjs.HTMLAudioPlugin._AUDIO_SEEKED,this._loopHandler,!1),this._playbackResource.loop=!0)},e._handleCleanUp=function(){var t=this._playbackResource;if(null!=t){t.pause(),t.loop=!1,t.removeEventListener(createjs.HTMLAudioPlugin._AUDIO_ENDED,this._endedHandler,!1),t.removeEventListener(createjs.HTMLAudioPlugin._AUDIO_READY,this._readyHandler,!1),t.removeEventListener(createjs.HTMLAudioPlugin._AUDIO_STALLED,this._stalledHandler,!1),t.removeEventListener(createjs.HTMLAudioPlugin._AUDIO_SEEKED,this._loopHandler,!1),t.removeEventListener(createjs.HTMLAudioPlugin._TIME_UPDATE,this._audioSpriteEndHandler,!1);try{t.currentTime=this._startTime}catch(t){}createjs.HTMLAudioTagPool.set(this.src,t),this._playbackResource=null}},e._beginPlaying=function(t){return this._playbackResource=createjs.HTMLAudioTagPool.get(this.src),this.AbstractSoundInstance__beginPlaying(t)},e._handleSoundReady=function(t){if(4!==this._playbackResource.readyState){var e=this._playbackResource;return e.addEventListener(createjs.HTMLAudioPlugin._AUDIO_READY,this._readyHandler,!1),e.addEventListener(createjs.HTMLAudioPlugin._AUDIO_STALLED,this._stalledHandler,!1),e.preload="auto",void e.load()}this._updateVolume(),this._playbackResource.currentTime=.001*(this._startTime+this._position),this._audioSpriteStopTime?this._playbackResource.addEventListener(createjs.HTMLAudioPlugin._TIME_UPDATE,this._audioSpriteEndHandler,!1):(this._playbackResource.addEventListener(createjs.HTMLAudioPlugin._AUDIO_ENDED,this._endedHandler,!1),0!=this._loop&&(this._playbackResource.addEventListener(createjs.HTMLAudioPlugin._AUDIO_SEEKED,this._loopHandler,!1),this._playbackResource.loop=!0)),this._playbackResource.play()},e._handleTagReady=function(t){this._playbackResource.removeEventListener(createjs.HTMLAudioPlugin._AUDIO_READY,this._readyHandler,!1),this._playbackResource.removeEventListener(createjs.HTMLAudioPlugin._AUDIO_STALLED,this._stalledHandler,!1),this._handleSoundReady()},e._pause=function(){this._playbackResource.pause()},e._resume=function(){this._playbackResource.play()},e._updateVolume=function(){if(null!=this._playbackResource){var t=this._muted||createjs.Sound._masterMute?0:this._volume*createjs.Sound._masterVolume;t!=this._playbackResource.volume&&(this._playbackResource.volume=t)}},e._calculateCurrentPosition=function(){return 1e3*this._playbackResource.currentTime-this._startTime},e._updatePosition=function(){this._playbackResource.removeEventListener(createjs.HTMLAudioPlugin._AUDIO_SEEKED,this._loopHandler,!1),this._playbackResource.addEventListener(createjs.HTMLAudioPlugin._AUDIO_SEEKED,this._handleSetPositionSeek,!1);try{this._playbackResource.currentTime=.001*(this._position+this._startTime)}catch(t){this._handleSetPositionSeek(null)}},e._handleSetPositionSeek=function(t){null!=this._playbackResource&&(this._playbackResource.removeEventListener(createjs.HTMLAudioPlugin._AUDIO_SEEKED,this._handleSetPositionSeek,!1),this._playbackResource.addEventListener(createjs.HTMLAudioPlugin._AUDIO_SEEKED,this._loopHandler,!1))},e._handleAudioSpriteLoop=function(t){this._playbackResource.currentTime<=this._audioSpriteStopTime||(this._playbackResource.pause(),0==this._loop?this._handleSoundComplete(null):(this._position=0,this._loop--,this._playbackResource.currentTime=.001*this._startTime,this._paused||this._playbackResource.play(),this._sendEvent("loop")))},e._handleLoop=function(t){0==this._loop&&(this._playbackResource.loop=!1,this._playbackResource.removeEventListener(createjs.HTMLAudioPlugin._AUDIO_SEEKED,this._loopHandler,!1))},e._updateStartTime=function(){this._audioSpriteStopTime=.001*(this._startTime+this._duration),this.playState==createjs.Sound.PLAY_SUCCEEDED&&(this._playbackResource.removeEventListener(createjs.HTMLAudioPlugin._AUDIO_ENDED,this._endedHandler,!1),this._playbackResource.addEventListener(createjs.HTMLAudioPlugin._TIME_UPDATE,this._audioSpriteEndHandler,!1))},e._updateDuration=function(){this._audioSpriteStopTime=.001*(this._startTime+this._duration),this.playState==createjs.Sound.PLAY_SUCCEEDED&&(this._playbackResource.removeEventListener(createjs.HTMLAudioPlugin._AUDIO_ENDED,this._endedHandler,!1),this._playbackResource.addEventListener(createjs.HTMLAudioPlugin._TIME_UPDATE,this._audioSpriteEndHandler,!1))},e._setDurationFromSource=function(){this._duration=createjs.HTMLAudioTagPool.getDuration(this.src),this._playbackResource=null},createjs.HTMLAudioSoundInstance=createjs.promote(t,"AbstractSoundInstance")}(),window.createjs=window.createjs||{},function(){"use strict";function t(){this.AbstractPlugin_constructor(),this._capabilities=i._capabilities,this._loaderClass=createjs.SoundLoader,this._soundInstanceClass=createjs.HTMLAudioSoundInstance}var e=createjs.extend(t,createjs.AbstractPlugin),i=t;i.MAX_INSTANCES=30,i._AUDIO_READY="canplaythrough",i._AUDIO_ENDED="ended",i._AUDIO_SEEKED="seeked",i._AUDIO_STALLED="stalled",i._TIME_UPDATE="timeupdate",i._capabilities=null,i.isSupported=function(){return i._generateCapabilities(),null!=i._capabilities},i._generateCapabilities=function(){if(null==i._capabilities){var t=document.createElement("audio");if(null==t.canPlayType)return null;i._capabilities={panning:!1,volume:!0,tracks:-1};for(var e=createjs.Sound.SUPPORTED_EXTENSIONS,r=createjs.Sound.EXTENSION_MAP,n=0,s=e.length;n<s;n++){var a=e[n],o=r[a]||a;i._capabilities[a]="no"!=t.canPlayType("audio/"+a)&&""!=t.canPlayType("audio/"+a)||"no"!=t.canPlayType("audio/"+o)&&""!=t.canPlayType("audio/"+o)}}},e.register=function(t){var e=createjs.HTMLAudioTagPool.get(t.src),i=this.AbstractPlugin_register(t);return i.setTag(e),i},e.removeSound=function(t){this.AbstractPlugin_removeSound(t),createjs.HTMLAudioTagPool.remove(t)},e.create=function(t,e,i){var r=this.AbstractPlugin_create(t,e,i);return r.playbackResource=null,r},e.toString=function(){return"[HTMLAudioPlugin]"},e.setVolume=e.getVolume=e.setMute=null,createjs.HTMLAudioPlugin=createjs.promote(t,"AbstractPlugin")}()},653:()=>{window.createjs=window.createjs||{},createjs.extend=function(t,e){"use strict";function i(){this.constructor=t}return i.prototype=e.prototype,t.prototype=new i},window.createjs=window.createjs||{},createjs.promote=function(t,e){"use strict";var i=t.prototype,r=Object.getPrototypeOf&&Object.getPrototypeOf(i)||i.__proto__;if(r)for(var n in i[(e+="_")+"constructor"]=r.constructor,r)i.hasOwnProperty(n)&&"function"==typeof r[n]&&(i[e+n]=r[n]);return t},window.createjs=window.createjs||{},createjs.deprecate=function(t,e){"use strict";return function(){var i="Deprecated property or method '"+e+"'. See docs for info.";return console&&(console.warn?console.warn(i):console.log(i)),t&&t.apply(this,arguments)}},window.createjs=window.createjs||{},function(){"use strict";function t(t,e,i){this.type=t,this.target=null,this.currentTarget=null,this.eventPhase=0,this.bubbles=!!e,this.cancelable=!!i,this.timeStamp=(new Date).getTime(),this.defaultPrevented=!1,this.propagationStopped=!1,this.immediatePropagationStopped=!1,this.removed=!1}var e=t.prototype;e.preventDefault=function(){this.defaultPrevented=this.cancelable&&!0},e.stopPropagation=function(){this.propagationStopped=!0},e.stopImmediatePropagation=function(){this.immediatePropagationStopped=this.propagationStopped=!0},e.remove=function(){this.removed=!0},e.clone=function(){return new t(this.type,this.bubbles,this.cancelable)},e.set=function(t){for(var e in t)this[e]=t[e];return this},e.toString=function(){return"[Event (type="+this.type+")]"},createjs.Event=t}(),window.createjs=window.createjs||{},function(){"use strict";function t(){this._listeners=null,this._captureListeners=null}var e=t.prototype;t.initialize=function(t){t.addEventListener=e.addEventListener,t.on=e.on,t.removeEventListener=t.off=e.removeEventListener,t.removeAllEventListeners=e.removeAllEventListeners,t.hasEventListener=e.hasEventListener,t.dispatchEvent=e.dispatchEvent,t._dispatchEvent=e._dispatchEvent,t.willTrigger=e.willTrigger},e.addEventListener=function(t,e,i){var r,n=(r=i?this._captureListeners=this._captureListeners||{}:this._listeners=this._listeners||{})[t];return n&&this.removeEventListener(t,e,i),(n=r[t])?n.push(e):r[t]=[e],e},e.on=function(t,e,i,r,n,s){return e.handleEvent&&(i=i||e,e=e.handleEvent),i=i||this,this.addEventListener(t,(function(t){e.call(i,t,n),r&&t.remove()}),s)},e.removeEventListener=function(t,e,i){var r=i?this._captureListeners:this._listeners;if(r){var n=r[t];if(n)for(var s=0,a=n.length;s<a;s++)if(n[s]==e){1==a?delete r[t]:n.splice(s,1);break}}},e.off=e.removeEventListener,e.removeAllEventListeners=function(t){t?(this._listeners&&delete this._listeners[t],this._captureListeners&&delete this._captureListeners[t]):this._listeners=this._captureListeners=null},e.dispatchEvent=function(t,e,i){if("string"==typeof t){var r=this._listeners;if(!(e||r&&r[t]))return!0;t=new createjs.Event(t,e,i)}else t.target&&t.clone&&(t=t.clone());try{t.target=this}catch(t){}if(t.bubbles&&this.parent){for(var n=this,s=[n];n.parent;)s.push(n=n.parent);var a,o=s.length;for(a=o-1;a>=0&&!t.propagationStopped;a--)s[a]._dispatchEvent(t,1+(0==a));for(a=1;a<o&&!t.propagationStopped;a++)s[a]._dispatchEvent(t,3)}else this._dispatchEvent(t,2);return!t.defaultPrevented},e.hasEventListener=function(t){var e=this._listeners,i=this._captureListeners;return!!(e&&e[t]||i&&i[t])},e.willTrigger=function(t){for(var e=this;e;){if(e.hasEventListener(t))return!0;e=e.parent}return!1},e.toString=function(){return"[EventDispatcher]"},e._dispatchEvent=function(t,e){var i,r,n=e<=2?this._captureListeners:this._listeners;if(t&&n&&(r=n[t.type])&&(i=r.length)){try{t.currentTarget=this}catch(t){}try{t.eventPhase=0|e}catch(t){}t.removed=!1,r=r.slice();for(var s=0;s<i&&!t.immediatePropagationStopped;s++){var a=r[s];a.handleEvent?a.handleEvent(t):a(t),t.removed&&(this.off(t.type,a,1==e),t.removed=!1)}}2===e&&this._dispatchEvent(t,2.1)},createjs.EventDispatcher=t}(),window.createjs=window.createjs||{},function(){"use strict";function t(){throw"Ticker cannot be instantiated."}t.RAF_SYNCHED="synched",t.RAF="raf",t.TIMEOUT="timeout",t.timingMode=null,t.maxDelta=0,t.paused=!1,t.removeEventListener=null,t.removeAllEventListeners=null,t.dispatchEvent=null,t.hasEventListener=null,t._listeners=null,createjs.EventDispatcher.initialize(t),t._addEventListener=t.addEventListener,t.addEventListener=function(){return!t._inited&&t.init(),t._addEventListener.apply(t,arguments)},t._inited=!1,t._startTime=0,t._pausedTime=0,t._ticks=0,t._pausedTicks=0,t._interval=50,t._lastTime=0,t._times=null,t._tickTimes=null,t._timerId=null,t._raf=!0,t._setInterval=function(e){t._interval=e,t._inited&&t._setupTick()},t.setInterval=createjs.deprecate(t._setInterval,"Ticker.setInterval"),t._getInterval=function(){return t._interval},t.getInterval=createjs.deprecate(t._getInterval,"Ticker.getInterval"),t._setFPS=function(e){t._setInterval(1e3/e)},t.setFPS=createjs.deprecate(t._setFPS,"Ticker.setFPS"),t._getFPS=function(){return 1e3/t._interval},t.getFPS=createjs.deprecate(t._getFPS,"Ticker.getFPS");try{Object.defineProperties(t,{interval:{get:t._getInterval,set:t._setInterval},framerate:{get:t._getFPS,set:t._setFPS}})}catch(t){console.log(t)}t.init=function(){t._inited||(t._inited=!0,t._times=[],t._tickTimes=[],t._startTime=t._getTime(),t._times.push(t._lastTime=0),t.interval=t._interval)},t.reset=function(){if(t._raf){var e=window.cancelAnimationFrame||window.webkitCancelAnimationFrame||window.mozCancelAnimationFrame||window.oCancelAnimationFrame||window.msCancelAnimationFrame;e&&e(t._timerId)}else clearTimeout(t._timerId);t.removeAllEventListeners("tick"),t._timerId=t._times=t._tickTimes=null,t._startTime=t._lastTime=t._ticks=t._pausedTime=0,t._inited=!1},t.getMeasuredTickTime=function(e){var i=0,r=t._tickTimes;if(!r||r.length<1)return-1;e=Math.min(r.length,e||0|t._getFPS());for(var n=0;n<e;n++)i+=r[n];return i/e},t.getMeasuredFPS=function(e){var i=t._times;return!i||i.length<2?-1:(e=Math.min(i.length-1,e||0|t._getFPS()),1e3/((i[0]-i[e])/e))},t.getTime=function(e){return t._startTime?t._getTime()-(e?t._pausedTime:0):-1},t.getEventTime=function(e){return t._startTime?(t._lastTime||t._startTime)-(e?t._pausedTime:0):-1},t.getTicks=function(e){return t._ticks-(e?t._pausedTicks:0)},t._handleSynch=function(){t._timerId=null,t._setupTick(),t._getTime()-t._lastTime>=.97*(t._interval-1)&&t._tick()},t._handleRAF=function(){t._timerId=null,t._setupTick(),t._tick()},t._handleTimeout=function(){t._timerId=null,t._setupTick(),t._tick()},t._setupTick=function(){if(null==t._timerId){var e=t.timingMode;if(e==t.RAF_SYNCHED||e==t.RAF){var i=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame;if(i)return t._timerId=i(e==t.RAF?t._handleRAF:t._handleSynch),void(t._raf=!0)}t._raf=!1,t._timerId=setTimeout(t._handleTimeout,t._interval)}},t._tick=function(){var e=t.paused,i=t._getTime(),r=i-t._lastTime;if(t._lastTime=i,t._ticks++,e&&(t._pausedTicks++,t._pausedTime+=r),t.hasEventListener("tick")){var n=new createjs.Event("tick"),s=t.maxDelta;n.delta=s&&r>s?s:r,n.paused=e,n.time=i,n.runTime=i-t._pausedTime,t.dispatchEvent(n)}for(t._tickTimes.unshift(t._getTime()-i);t._tickTimes.length>100;)t._tickTimes.pop();for(t._times.unshift(i);t._times.length>100;)t._times.pop()};var e=window,i=e.performance.now||e.performance.mozNow||e.performance.msNow||e.performance.oNow||e.performance.webkitNow;t._getTime=function(){return(i&&i.call(e.performance)||(new Date).getTime())-t._startTime},createjs.Ticker=t}(),window.createjs=window.createjs||{},function(){"use strict";function t(t){this.EventDispatcher_constructor(),this.ignoreGlobalPause=!1,this.loop=0,this.useTicks=!1,this.reversed=!1,this.bounce=!1,this.timeScale=1,this.duration=0,this.position=0,this.rawPosition=-1,this._paused=!0,this._next=null,this._prev=null,this._parent=null,this._labels=null,this._labelList=null,t&&(this.useTicks=!!t.useTicks,this.ignoreGlobalPause=!!t.ignoreGlobalPause,this.loop=!0===t.loop?-1:t.loop||0,this.reversed=!!t.reversed,this.bounce=!!t.bounce,this.timeScale=t.timeScale||1,t.onChange&&this.addEventListener("change",t.onChange),t.onComplete&&this.addEventListener("complete",t.onComplete))}var e=createjs.extend(t,createjs.EventDispatcher);e._setPaused=function(t){return createjs.Tween._register(this,t),this},e.setPaused=createjs.deprecate(e._setPaused,"AbstractTween.setPaused"),e._getPaused=function(){return this._paused},e.getPaused=createjs.deprecate(e._getPaused,"AbstactTween.getPaused"),e._getCurrentLabel=function(t){var e=this.getLabels();null==t&&(t=this.position);for(var i=0,r=e.length;i<r&&!(t<e[i].position);i++);return 0===i?null:e[i-1].label},e.getCurrentLabel=createjs.deprecate(e._getCurrentLabel,"AbstractTween.getCurrentLabel");try{Object.defineProperties(e,{paused:{set:e._setPaused,get:e._getPaused},currentLabel:{get:e._getCurrentLabel}})}catch(t){}e.advance=function(t,e){this.setPosition(this.rawPosition+t*this.timeScale,e)},e.setPosition=function(t,e,i,r){var n=this.duration,s=this.loop,a=this.rawPosition,o=0,c=0,u=!1;if(t<0&&(t=0),0===n){if(u=!0,-1!==a)return u}else{if(c=t-(o=t/n|0)*n,(u=-1!==s&&t>=s*n+n)&&(t=(c=n)*(o=s)+n),t===a)return u;!this.reversed!=!(this.bounce&&o%2)&&(c=n-c)}this.position=c,this.rawPosition=t,this._updatePosition(i,u),u&&(this.paused=!0),r&&r(this),e||this._runActions(a,t,i,!i&&-1===a),this.dispatchEvent("change"),u&&this.dispatchEvent("complete")},e.calculatePosition=function(t){var e=this.duration,i=this.loop,r=0,n=0;return 0===e?0:(-1!==i&&t>=i*e+e?(n=e,r=i):n=t<0?0:t-(r=t/e|0)*e,!this.reversed!=!(this.bounce&&r%2)?e-n:n)},e.getLabels=function(){var t=this._labelList;if(!t){t=this._labelList=[];var e=this._labels;for(var i in e)t.push({label:i,position:e[i]});t.sort((function(t,e){return t.position-e.position}))}return t},e.setLabels=function(t){this._labels=t,this._labelList=null},e.addLabel=function(t,e){this._labels||(this._labels={}),this._labels[t]=e;var i=this._labelList;if(i){for(var r=0,n=i.length;r<n&&!(e<i[r].position);r++);i.splice(r,0,{label:t,position:e})}},e.gotoAndPlay=function(t){this.paused=!1,this._goto(t)},e.gotoAndStop=function(t){this.paused=!0,this._goto(t)},e.resolve=function(t){var e=Number(t);return isNaN(e)&&(e=this._labels&&this._labels[t]),e},e.toString=function(){return"[AbstractTween]"},e.clone=function(){throw"AbstractTween can not be cloned."},e._init=function(t){t&&t.paused||(this.paused=!1),t&&null!=t.position&&this.setPosition(t.position)},e._updatePosition=function(t,e){},e._goto=function(t){var e=this.resolve(t);null!=e&&this.setPosition(e,!1,!0)},e._runActions=function(t,e,i,r){if(this._actionHead||this.tweens){var n,s,a,o,c=this.duration,u=this.reversed,l=this.bounce,h=this.loop;if(0===c?(n=s=a=o=0,u=l=!1):(a=t-(n=t/c|0)*c,o=e-(s=e/c|0)*c),-1!==h&&(s>h&&(o=c,s=h),n>h&&(a=c,n=h)),i)return this._runActionsRange(o,o,i,r);if(n!==s||a!==o||i||r){-1===n&&(n=a=0);var d=t<=e,f=n;do{var p=f===n?a:d?0:c,_=f===s?o:d?c:0;if(!u!=!(l&&f%2)&&(p=c-p,_=c-_),l&&f!==n&&p===_);else if(this._runActionsRange(p,_,i,r||f!==n&&!l))return!0;r=!1}while(d&&++f<=s||!d&&--f>=s)}}},e._runActionsRange=function(t,e,i,r){},createjs.AbstractTween=createjs.promote(t,"EventDispatcher")}(),window.createjs=window.createjs||{},function(){"use strict";function t(e,r){this.AbstractTween_constructor(r),this.pluginData=null,this.target=e,this.passive=!1,this._stepHead=new i(null,0,0,{},null,!0),this._stepTail=this._stepHead,this._stepPosition=0,this._actionHead=null,this._actionTail=null,this._plugins=null,this._pluginIds=null,this._injected=null,r&&(this.pluginData=r.pluginData,r.override&&t.removeTweens(e)),this.pluginData||(this.pluginData={}),this._init(r)}var e=createjs.extend(t,createjs.AbstractTween);function i(t,e,i,r,n,s){this.next=null,this.prev=t,this.t=e,this.d=i,this.props=r,this.ease=n,this.passive=s,this.index=t?t.index+1:0}function r(t,e,i,r,n){this.next=null,this.prev=t,this.t=e,this.d=0,this.scope=i,this.funct=r,this.params=n}t.IGNORE={},t._tweens=[],t._plugins=null,t._tweenHead=null,t._tweenTail=null,t.get=function(e,i){return new t(e,i)},t.tick=function(e,i){for(var r=t._tweenHead;r;){var n=r._next;i&&!r.ignoreGlobalPause||r._paused||r.advance(r.useTicks?1:e),r=n}},t.handleEvent=function(t){"tick"===t.type&&this.tick(t.delta,t.paused)},t.removeTweens=function(e){if(e.tweenjs_count){for(var i=t._tweenHead;i;){var r=i._next;i.target===e&&t._register(i,!0),i=r}e.tweenjs_count=0}},t.removeAllTweens=function(){for(var e=t._tweenHead;e;){var i=e._next;e._paused=!0,e.target&&(e.target.tweenjs_count=0),e._next=e._prev=null,e=i}t._tweenHead=t._tweenTail=null},t.hasActiveTweens=function(e){return e?!!e.tweenjs_count:!!t._tweenHead},t._installPlugin=function(e){for(var i=e.priority=e.priority||0,r=t._plugins=t._plugins||[],n=0,s=r.length;n<s&&!(i<r[n].priority);n++);r.splice(n,0,e)},t._register=function(e,i){var r=e.target;if(!i&&e._paused){r&&(r.tweenjs_count=r.tweenjs_count?r.tweenjs_count+1:1);var n=t._tweenTail;n?(t._tweenTail=n._next=e,e._prev=n):t._tweenHead=t._tweenTail=e,!t._inited&&createjs.Ticker&&(createjs.Ticker.addEventListener("tick",t),t._inited=!0)}else if(i&&!e._paused){r&&r.tweenjs_count--;var s=e._next,a=e._prev;s?s._prev=a:t._tweenTail=a,a?a._next=s:t._tweenHead=s,e._next=e._prev=null}e._paused=i},e.wait=function(t,e){return t>0&&this._addStep(+t,this._stepTail.props,null,e),this},e.to=function(t,e,i){(null==e||e<0)&&(e=0);var r=this._addStep(+e,null,i);return this._appendProps(t,r),this},e.label=function(t){return this.addLabel(t,this.duration),this},e.call=function(t,e,i){return this._addAction(i||this.target,t,e||[this])},e.set=function(t,e){return this._addAction(e||this.target,this._set,[t])},e.play=function(t){return this._addAction(t||this,this._set,[{paused:!1}])},e.pause=function(t){return this._addAction(t||this,this._set,[{paused:!0}])},e.w=e.wait,e.t=e.to,e.c=e.call,e.s=e.set,e.toString=function(){return"[Tween]"},e.clone=function(){throw"Tween can not be cloned."},e._addPlugin=function(t){var e=this._pluginIds||(this._pluginIds={}),i=t.ID;if(i&&!e[i]){e[i]=!0;for(var r=this._plugins||(this._plugins=[]),n=t.priority||0,s=0,a=r.length;s<a;s++)if(n<r[s].priority)return void r.splice(s,0,t);r.push(t)}},e._updatePosition=function(t,e){var i=this._stepHead.next,r=this.position,n=this.duration;if(this.target&&i){for(var s=i.next;s&&s.t<=r;)s=(i=i.next).next;var a=e?0===n?1:r/n:(r-i.t)/i.d;this._updateTargetProps(i,a,e)}this._stepPosition=i?r-i.t:0},e._updateTargetProps=function(e,i,r){if(!(this.passive=!!e.passive)){var n,s,a,o,c=e.prev.props,u=e.props;(o=e.ease)&&(i=o(i,0,1,1));var l=this._plugins;t:for(var h in c){if(n=(s=c[h])!==(a=u[h])&&"number"==typeof s?s+(a-s)*i:i>=1?a:s,l)for(var d=0,f=l.length;d<f;d++){var p=l[d].change(this,e,h,n,i,r);if(p===t.IGNORE)continue t;void 0!==p&&(n=p)}this.target[h]=n}}},e._runActionsRange=function(t,e,i,r){var n=t>e,s=n?this._actionTail:this._actionHead,a=e,o=t;n&&(a=t,o=e);for(var c=this.position;s;){var u=s.t;if((u===e||u>o&&u<a||r&&u===t)&&(s.funct.apply(s.scope,s.params),c!==this.position))return!0;s=n?s.prev:s.next}},e._appendProps=function(e,i,r){var n,s,a,o,c,u=this._stepHead.props,l=this.target,h=t._plugins,d=i.prev,f=d.props,p=i.props||(i.props=this._cloneProps(f)),_={};for(n in e)if(e.hasOwnProperty(n)&&(_[n]=p[n]=e[n],void 0===u[n])){if(o=void 0,h)for(s=h.length-1;s>=0;s--)if(void 0!==(a=h[s].init(this,n,o))&&(o=a),o===t.IGNORE){delete p[n],delete _[n];break}o!==t.IGNORE&&(void 0===o&&(o=l[n]),f[n]=void 0===o?null:o)}for(n in _){a=e[n];for(var v,m=d;(v=m)&&(m=v.prev);)if(m.props!==v.props){if(void 0!==m.props[n])break;m.props[n]=f[n]}}if(!1!==r&&(h=this._plugins))for(s=h.length-1;s>=0;s--)h[s].step(this,i,_);(c=this._injected)&&(this._injected=null,this._appendProps(c,i,!1))},e._injectProp=function(t,e){(this._injected||(this._injected={}))[t]=e},e._addStep=function(t,e,r,n){var s=new i(this._stepTail,this.duration,t,e,r,n||!1);return this.duration+=t,this._stepTail=this._stepTail.next=s},e._addAction=function(t,e,i){var n=new r(this._actionTail,this.duration,t,e,i);return this._actionTail?this._actionTail.next=n:this._actionHead=n,this._actionTail=n,this},e._set=function(t){for(var e in t)this[e]=t[e]},e._cloneProps=function(t){var e={};for(var i in t)e[i]=t[i];return e},createjs.Tween=createjs.promote(t,"AbstractTween")}(),window.createjs=window.createjs||{},function(){"use strict";function t(t){var e,i;t instanceof Array||null==t&&arguments.length>1?(e=t,i=arguments[1],t=arguments[2]):t&&(e=t.tweens,i=t.labels),this.AbstractTween_constructor(t),this.tweens=[],e&&this.addTween.apply(this,e),this.setLabels(i),this._init(t)}var e=createjs.extend(t,createjs.AbstractTween);e.addTween=function(t){t._parent&&t._parent.removeTween(t);var e=arguments.length;if(e>1){for(var i=0;i<e;i++)this.addTween(arguments[i]);return arguments[e-1]}if(0===e)return null;this.tweens.push(t),t._parent=this,t.paused=!0;var r=t.duration;return t.loop>0&&(r*=t.loop+1),r>this.duration&&(this.duration=r),this.rawPosition>=0&&t.setPosition(this.rawPosition),t},e.removeTween=function(t){var e=arguments.length;if(e>1){for(var i=!0,r=0;r<e;r++)i=i&&this.removeTween(arguments[r]);return i}if(0===e)return!0;var n=this.tweens;for(r=n.length;r--;)if(n[r]===t)return n.splice(r,1),t._parent=null,t.duration>=this.duration&&this.updateDuration(),!0;return!1},e.updateDuration=function(){this.duration=0;for(var t=0,e=this.tweens.length;t<e;t++){var i=this.tweens[t],r=i.duration;i.loop>0&&(r*=i.loop+1),r>this.duration&&(this.duration=r)}},e.toString=function(){return"[Timeline]"},e.clone=function(){throw"Timeline can not be cloned."},e._updatePosition=function(t,e){for(var i=this.position,r=0,n=this.tweens.length;r<n;r++)this.tweens[r].setPosition(i,!0,t)},e._runActionsRange=function(t,e,i,r){for(var n=this.position,s=0,a=this.tweens.length;s<a;s++)if(this.tweens[s]._runActions(t,e,i,r),n!==this.position)return!0},createjs.Timeline=createjs.promote(t,"AbstractTween")}(),window.createjs=window.createjs||{},function(){"use strict";function t(){throw"Ease cannot be instantiated."}t.linear=function(t){return t},t.none=t.linear,t.get=function(t){return t<-1?t=-1:t>1&&(t=1),function(e){return 0==t?e:t<0?e*(e*-t+1+t):e*((2-e)*t+(1-t))}},t.getPowIn=function(t){return function(e){return Math.pow(e,t)}},t.getPowOut=function(t){return function(e){return 1-Math.pow(1-e,t)}},t.getPowInOut=function(t){return function(e){return(e*=2)<1?.5*Math.pow(e,t):1-.5*Math.abs(Math.pow(2-e,t))}},t.quadIn=t.getPowIn(2),t.quadOut=t.getPowOut(2),t.quadInOut=t.getPowInOut(2),t.cubicIn=t.getPowIn(3),t.cubicOut=t.getPowOut(3),t.cubicInOut=t.getPowInOut(3),t.quartIn=t.getPowIn(4),t.quartOut=t.getPowOut(4),t.quartInOut=t.getPowInOut(4),t.quintIn=t.getPowIn(5),t.quintOut=t.getPowOut(5),t.quintInOut=t.getPowInOut(5),t.sineIn=function(t){return 1-Math.cos(t*Math.PI/2)},t.sineOut=function(t){return Math.sin(t*Math.PI/2)},t.sineInOut=function(t){return-.5*(Math.cos(Math.PI*t)-1)},t.getBackIn=function(t){return function(e){return e*e*((t+1)*e-t)}},t.backIn=t.getBackIn(1.7),t.getBackOut=function(t){return function(e){return--e*e*((t+1)*e+t)+1}},t.backOut=t.getBackOut(1.7),t.getBackInOut=function(t){return t*=1.525,function(e){return(e*=2)<1?e*e*((t+1)*e-t)*.5:.5*((e-=2)*e*((t+1)*e+t)+2)}},t.backInOut=t.getBackInOut(1.7),t.circIn=function(t){return-(Math.sqrt(1-t*t)-1)},t.circOut=function(t){return Math.sqrt(1- --t*t)},t.circInOut=function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},t.bounceIn=function(e){return 1-t.bounceOut(1-e)},t.bounceOut=function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},t.bounceInOut=function(e){return e<.5?.5*t.bounceIn(2*e):.5*t.bounceOut(2*e-1)+.5},t.getElasticIn=function(t,e){var i=2*Math.PI;return function(r){if(0==r||1==r)return r;var n=e/i*Math.asin(1/t);return-t*Math.pow(2,10*(r-=1))*Math.sin((r-n)*i/e)}},t.elasticIn=t.getElasticIn(1,.3),t.getElasticOut=function(t,e){var i=2*Math.PI;return function(r){if(0==r||1==r)return r;var n=e/i*Math.asin(1/t);return t*Math.pow(2,-10*r)*Math.sin((r-n)*i/e)+1}},t.elasticOut=t.getElasticOut(1,.3),t.getElasticInOut=function(t,e){var i=2*Math.PI;return function(r){var n=e/i*Math.asin(1/t);return(r*=2)<1?t*Math.pow(2,10*(r-=1))*Math.sin((r-n)*i/e)*-.5:t*Math.pow(2,-10*(r-=1))*Math.sin((r-n)*i/e)*.5+1}},t.elasticInOut=t.getElasticInOut(1,.3*1.5),createjs.Ease=t}(),window.createjs=window.createjs||{},function(){"use strict";function t(){throw"MotionGuidePlugin cannot be instantiated."}var e=t;e.priority=0,e.ID="MotionGuide",e.install=function(){return createjs.Tween._installPlugin(t),createjs.Tween.IGNORE},e.init=function(t,i,r){"guide"==i&&t._addPlugin(e)},e.step=function(t,i,r){for(var n in r)if("guide"===n){var s=i.props.guide,a=e._solveGuideData(r.guide,s);s.valid=!a;var o=s.endData;if(t._injectProp("x",o.x),t._injectProp("y",o.y),a||!s.orient)break;var c=void 0===i.prev.props.rotation?t.target.rotation||0:i.prev.props.rotation;if(s.startOffsetRot=c-s.startData.rotation,"fixed"==s.orient)s.endAbsRot=o.rotation+s.startOffsetRot,s.deltaRotation=0;else{var u=void 0===r.rotation?t.target.rotation||0:r.rotation,l=u-s.endData.rotation-s.startOffsetRot,h=l%360;switch(s.endAbsRot=u,s.orient){case"auto":s.deltaRotation=l;break;case"cw":s.deltaRotation=(h+360)%360+360*Math.abs(l/360|0);break;case"ccw":s.deltaRotation=(h-360)%360+-360*Math.abs(l/360|0)}}t._injectProp("rotation",s.endAbsRot)}},e.change=function(t,i,r,n,s,a){var o=i.props.guide;if(o&&i.props!==i.prev.props&&o!==i.prev.props.guide)return"guide"===r&&!o.valid||"x"==r||"y"==r||"rotation"===r&&o.orient?createjs.Tween.IGNORE:void e._ratioToPositionData(s,o,t.target)},e.debug=function(t,i,r){t=t.guide||t;var n=e._findPathProblems(t);if(n&&console.error("MotionGuidePlugin Error found: \n"+n),!i)return n;var s=t.path,a=s.length;for(i.save(),i.lineCap="round",i.lineJoin="miter",i.beginPath(),i.moveTo(s[0],s[1]),l=2;l<a;l+=4)i.quadraticCurveTo(s[l],s[l+1],s[l+2],s[l+3]);i.strokeStyle="black",i.lineWidth=4.5,i.stroke(),i.strokeStyle="white",i.lineWidth=3,i.stroke(),i.closePath();var o=r.length;if(r&&o){var c={},u={};e._solveGuideData(t,c);for(var l=0;l<o;l++)c.orient="fixed",e._ratioToPositionData(r[l],c,u),i.beginPath(),i.moveTo(u.x,u.y),i.lineTo(u.x+9*Math.cos(.0174533*u.rotation),u.y+9*Math.sin(.0174533*u.rotation)),i.strokeStyle="black",i.lineWidth=4.5,i.stroke(),i.strokeStyle="red",i.lineWidth=3,i.stroke(),i.closePath()}return i.restore(),n},e._solveGuideData=function(t,i){var r;if(r=e.debug(t))return r;var n=i.path=t.path;i.orient=t.orient;i.subLines=[],i.totalLength=0,i.startOffsetRot=0,i.deltaRotation=0,i.startData={ratio:0},i.endData={ratio:1},i.animSpan=1;var s,a,o,c,u,l,h,d,f,p=n.length,_={};for(s=n[0],a=n[1],h=2;h<p;h+=4){o=n[h],c=n[h+1],u=n[h+2],l=n[h+3];var v={weightings:[],estLength:0,portion:0},m=s,g=a;for(d=1;d<=10;d++){e._getParamsForCurve(s,a,o,c,u,l,d/10,!1,_);var y=_.x-m,b=_.y-g;f=Math.sqrt(y*y+b*b),v.weightings.push(f),v.estLength+=f,m=_.x,g=_.y}for(i.totalLength+=v.estLength,d=0;d<10;d++)f=v.estLength,v.weightings[d]=v.weightings[d]/f;i.subLines.push(v),s=u,a=l}f=i.totalLength;var w=i.subLines.length;for(h=0;h<w;h++)i.subLines[h].portion=i.subLines[h].estLength/f;var T=isNaN(t.start)?0:t.start,E=isNaN(t.end)?1:t.end;e._ratioToPositionData(T,i,i.startData),e._ratioToPositionData(E,i,i.endData),i.startData.ratio=T,i.endData.ratio=E,i.animSpan=i.endData.ratio-i.startData.ratio},e._ratioToPositionData=function(t,i,r){var n,s,a,o,c,u=i.subLines,l=0,h=t*i.animSpan+i.startData.ratio;for(s=u.length,n=0;n<s;n++){if(l+(o=u[n].portion)>=h){c=n;break}l+=o}void 0===c&&(c=s-1,l-=o);var d=u[c].weightings,f=o;for(s=d.length,n=0;n<s&&!(l+(o=d[n]*f)>=h);n++)l+=o;c=4*c+2,a=n/10+(h-l)/o*.1;var p=i.path;return e._getParamsForCurve(p[c-2],p[c-1],p[c],p[c+1],p[c+2],p[c+3],a,i.orient,r),i.orient&&(t>=.99999&&t<=1.00001&&void 0!==i.endAbsRot?r.rotation=i.endAbsRot:r.rotation+=i.startOffsetRot+t*i.deltaRotation),r},e._getParamsForCurve=function(t,e,i,r,n,s,a,o,c){var u=1-a;c.x=u*u*t+2*u*a*i+a*a*n,c.y=u*u*e+2*u*a*r+a*a*s,o&&(c.rotation=57.2957795*Math.atan2((r-e)*u+(s-r)*a,(i-t)*u+(n-i)*a))},e._findPathProblems=function(t){var e=t.path,i=e&&e.length||0;if(i<6||(i-2)%4){var r="\tCannot parse 'path' array due to invalid number of entries in path. ";return r+="There should be an odd number of points, at least 3 points, and 2 entries per point (x & y). ",r+="See 'CanvasRenderingContext2D.quadraticCurveTo' for details as 'path' models a quadratic bezier.\n\n",r+="Only [ "+i+" ] values found. Expected: "+Math.max(4*Math.ceil((i-2)/4)+2,6)}for(var n=0;n<i;n++)if(isNaN(e[n]))return"All data in path array must be numeric";var s=t.start;if(isNaN(s)&&void 0!==s)return"'start' out of bounds. Expected 0 to 1, got: "+s;var a=t.end;if(isNaN(a)&&void 0!==a)return"'end' out of bounds. Expected 0 to 1, got: "+a;var o=t.orient;return o&&"fixed"!=o&&"auto"!=o&&"cw"!=o&&"ccw"!=o?'Invalid orientation value. Expected ["fixed", "auto", "cw", "ccw", undefined], got: '+o:void 0},createjs.MotionGuidePlugin=t}(),window.createjs=window.createjs||{},function(){"use strict";var t=createjs.TweenJS=createjs.TweenJS||{};t.version="1.0.0",t.buildDate="Thu, 14 Sep 2017 19:47:47 GMT"}()},232:function(t,e,i){var r;!function(n,s){"use strict";var a="function",o="undefined",c="object",u="string",l="major",h="model",d="name",f="type",p="vendor",_="version",v="architecture",m="console",g="mobile",y="tablet",b="smarttv",w="wearable",T="embedded",E="Amazon",S="Apple",x="ASUS",j="BlackBerry",A="Browser",L="Chrome",R="Firefox",P="Google",C="Huawei",k="LG",I="Microsoft",O="Motorola",M="Opera",D="Samsung",F="Sharp",N="Sony",B="Xiaomi",U="Zebra",H="Facebook",X="Chromium OS",q="Mac OS",G=function(t){for(var e={},i=0;i<t.length;i++)e[t[i].toUpperCase()]=t[i];return e},Y=function(t,e){return typeof t===u&&-1!==V(e).indexOf(V(t))},V=function(t){return t.toLowerCase()},z=function(t,e){if(typeof t===u)return t=t.replace(/^\s\s*/,""),typeof e===o?t:t.substring(0,500)},W=function(t,e){for(var i,r,n,o,u,l,h=0;h<e.length&&!u;){var d=e[h],f=e[h+1];for(i=r=0;i<d.length&&!u&&d[i];)if(u=d[i++].exec(t))for(n=0;n<f.length;n++)l=u[++r],typeof(o=f[n])===c&&o.length>0?2===o.length?typeof o[1]==a?this[o[0]]=o[1].call(this,l):this[o[0]]=o[1]:3===o.length?typeof o[1]!==a||o[1].exec&&o[1].test?this[o[0]]=l?l.replace(o[1],o[2]):s:this[o[0]]=l?o[1].call(this,l,o[2]):s:4===o.length&&(this[o[0]]=l?o[3].call(this,l.replace(o[1],o[2])):s):this[o]=l||s;h+=2}},J=function(t,e){for(var i in e)if(typeof e[i]===c&&e[i].length>0){for(var r=0;r<e[i].length;r++)if(Y(e[i][r],t))return"?"===i?s:i}else if(Y(e[i],t))return"?"===i?s:i;return t},Q={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},K={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[_,[d,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[_,[d,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[d,_],[/opios[\/ ]+([\w\.]+)/i],[_,[d,M+" Mini"]],[/\bopr\/([\w\.]+)/i],[_,[d,M]],[/\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\/ ]?([\w\.]+)/i],[_,[d,"Baidu"]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant|iemobile|slim)\s?(?:browser)?[\/ ]?([\w\.]*)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[d,_],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[_,[d,"UC"+A]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i,/micromessenger\/([\w\.]+)/i],[_,[d,"WeChat"]],[/konqueror\/([\w\.]+)/i],[_,[d,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[_,[d,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[_,[d,"Yandex"]],[/slbrowser\/([\w\.]+)/i],[_,[d,"Smart Lenovo "+A]],[/(avast|avg)\/([\w\.]+)/i],[[d,/(.+)/,"$1 Secure "+A],_],[/\bfocus\/([\w\.]+)/i],[_,[d,R+" Focus"]],[/\bopt\/([\w\.]+)/i],[_,[d,M+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[_,[d,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[_,[d,"Dolphin"]],[/coast\/([\w\.]+)/i],[_,[d,M+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[_,[d,"MIUI "+A]],[/fxios\/([-\w\.]+)/i],[_,[d,R]],[/\bqihu|(qi?ho?o?|360)browser/i],[[d,"360 "+A]],[/(oculus|sailfish|huawei|vivo)browser\/([\w\.]+)/i],[[d,/(.+)/,"$1 "+A],_],[/samsungbrowser\/([\w\.]+)/i],[_,[d,D+" Internet"]],[/(comodo_dragon)\/([\w\.]+)/i],[[d,/_/g," "],_],[/metasr[\/ ]?([\d\.]+)/i],[_,[d,"Sogou Explorer"]],[/(sogou)mo\w+\/([\d\.]+)/i],[[d,"Sogou Mobile"],_],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|2345Explorer)[\/ ]?([\w\.]+)/i],[d,_],[/(lbbrowser)/i,/\[(linkedin)app\]/i],[d],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[d,H],_],[/(Klarna)\/([\w\.]+)/i,/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(alipay)client\/([\w\.]+)/i,/(chromium|instagram|snapchat)[\/ ]([-\w\.]+)/i],[d,_],[/\bgsa\/([\w\.]+) .*safari\//i],[_,[d,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[_,[d,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[_,[d,L+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[d,L+" WebView"],_],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[_,[d,"Android "+A]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[d,_],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[_,[d,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[_,d],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[d,[_,J,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[d,_],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[d,"Netscape"],_],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[_,[d,R+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[d,_],[/(cobalt)\/([\w\.]+)/i],[d,[_,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[v,"amd64"]],[/(ia32(?=;))/i],[[v,V]],[/((?:i[346]|x)86)[;\)]/i],[[v,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[v,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[v,"armhf"]],[/windows (ce|mobile); ppc;/i],[[v,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[v,/ower/,"",V]],[/(sun4\w)[;\)]/i],[[v,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[v,V]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[h,[p,D],[f,y]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[h,[p,D],[f,g]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[h,[p,S],[f,g]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[h,[p,S],[f,y]],[/(macintosh);/i],[h,[p,S]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[h,[p,F],[f,g]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[h,[p,C],[f,y]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[h,[p,C],[f,g]],[/\b(poco[\w ]+|m2\d{3}j\d\d[a-z]{2})(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/oid[^\)]+; (m?[12][0-389][01]\w{3,6}[c-y])( bui|; wv|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[h,/_/g," "],[p,B],[f,g]],[/oid[^\)]+; (2\d{4}(283|rpbf)[cgl])( bui|\))/i,/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[h,/_/g," "],[p,B],[f,y]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[h,[p,"OPPO"],[f,g]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[h,[p,"Vivo"],[f,g]],[/\b(rmx[1-3]\d{3})(?: bui|;|\))/i],[h,[p,"Realme"],[f,g]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[h,[p,O],[f,g]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[h,[p,O],[f,y]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[h,[p,k],[f,y]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[h,[p,k],[f,g]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[h,[p,"Lenovo"],[f,y]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[h,/_/g," "],[p,"Nokia"],[f,g]],[/(pixel c)\b/i],[h,[p,P],[f,y]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[h,[p,P],[f,g]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[h,[p,N],[f,g]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[h,"Xperia Tablet"],[p,N],[f,y]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[h,[p,"OnePlus"],[f,g]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[h,[p,E],[f,y]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[h,/(.+)/g,"Fire Phone $1"],[p,E],[f,g]],[/(playbook);[-\w\),; ]+(rim)/i],[h,p,[f,y]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[h,[p,j],[f,g]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[h,[p,x],[f,y]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[h,[p,x],[f,g]],[/(nexus 9)/i],[h,[p,"HTC"],[f,y]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[p,[h,/_/g," "],[f,g]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[h,[p,"Acer"],[f,y]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[h,[p,"Meizu"],[f,g]],[/; ((?:power )?armor(?:[\w ]{0,8}))(?: bui|\))/i],[h,[p,"Ulefone"],[f,g]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron|infinix|tecno)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[p,h,[f,g]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[p,h,[f,y]],[/(surface duo)/i],[h,[p,I],[f,y]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[h,[p,"Fairphone"],[f,g]],[/(u304aa)/i],[h,[p,"AT&T"],[f,g]],[/\bsie-(\w*)/i],[h,[p,"Siemens"],[f,g]],[/\b(rct\w+) b/i],[h,[p,"RCA"],[f,y]],[/\b(venue[\d ]{2,7}) b/i],[h,[p,"Dell"],[f,y]],[/\b(q(?:mv|ta)\w+) b/i],[h,[p,"Verizon"],[f,y]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[h,[p,"Barnes & Noble"],[f,y]],[/\b(tm\d{3}\w+) b/i],[h,[p,"NuVision"],[f,y]],[/\b(k88) b/i],[h,[p,"ZTE"],[f,y]],[/\b(nx\d{3}j) b/i],[h,[p,"ZTE"],[f,g]],[/\b(gen\d{3}) b.+49h/i],[h,[p,"Swiss"],[f,g]],[/\b(zur\d{3}) b/i],[h,[p,"Swiss"],[f,y]],[/\b((zeki)?tb.*\b) b/i],[h,[p,"Zeki"],[f,y]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[p,"Dragon Touch"],h,[f,y]],[/\b(ns-?\w{0,9}) b/i],[h,[p,"Insignia"],[f,y]],[/\b((nxa|next)-?\w{0,9}) b/i],[h,[p,"NextBook"],[f,y]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[p,"Voice"],h,[f,g]],[/\b(lvtel\-)?(v1[12]) b/i],[[p,"LvTel"],h,[f,g]],[/\b(ph-1) /i],[h,[p,"Essential"],[f,g]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[h,[p,"Envizen"],[f,y]],[/\b(trio[-\w\. ]+) b/i],[h,[p,"MachSpeed"],[f,y]],[/\btu_(1491) b/i],[h,[p,"Rotor"],[f,y]],[/(shield[\w ]+) b/i],[h,[p,"Nvidia"],[f,y]],[/(sprint) (\w+)/i],[p,h,[f,g]],[/(kin\.[onetw]{3})/i],[[h,/\./g," "],[p,I],[f,g]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[h,[p,U],[f,y]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[h,[p,U],[f,g]],[/smart-tv.+(samsung)/i],[p,[f,b]],[/hbbtv.+maple;(\d+)/i],[[h,/^/,"SmartTV"],[p,D],[f,b]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[p,k],[f,b]],[/(apple) ?tv/i],[p,[h,S+" TV"],[f,b]],[/crkey/i],[[h,L+"cast"],[p,P],[f,b]],[/droid.+aft(\w+)( bui|\))/i],[h,[p,E],[f,b]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[h,[p,F],[f,b]],[/(bravia[\w ]+)( bui|\))/i],[h,[p,N],[f,b]],[/(mitv-\w{5}) bui/i],[h,[p,B],[f,b]],[/Hbbtv.*(technisat) (.*);/i],[p,h,[f,b]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[p,z],[h,z],[f,b]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[f,b]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[p,h,[f,m]],[/droid.+; (shield) bui/i],[h,[p,"Nvidia"],[f,m]],[/(playstation [345portablevi]+)/i],[h,[p,N],[f,m]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[h,[p,I],[f,m]],[/((pebble))app/i],[p,h,[f,w]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[h,[p,S],[f,w]],[/droid.+; (glass) \d/i],[h,[p,P],[f,w]],[/droid.+; (wt63?0{2,3})\)/i],[h,[p,U],[f,w]],[/(quest( 2| pro)?)/i],[h,[p,H],[f,w]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[p,[f,T]],[/(aeobc)\b/i],[h,[p,E],[f,T]],[/droid .+?; ([^;]+?)(?: bui|; wv\)|\) applew).+? mobile safari/i],[h,[f,g]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[h,[f,y]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[f,y]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[f,g]],[/(android[-\w\. ]{0,9});.+buil/i],[h,[p,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[_,[d,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[_,[d,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[d,_],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[_,d]],os:[[/microsoft (windows) (vista|xp)/i],[d,_],[/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i],[d,[_,J,Q]],[/windows nt 6\.2; (arm)/i,/windows[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i,/(?:win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[_,J,Q],[d,"Windows"]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/(?:ios;fbsv\/|iphone.+ios[\/ ])([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[_,/_/g,"."],[d,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[d,q],[_,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[_,d],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[d,_],[/\(bb(10);/i],[_,[d,j]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[_,[d,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[_,[d,R+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[_,[d,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[_,[d,"watchOS"]],[/crkey\/([\d\.]+)/i],[_,[d,L+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[d,X],_],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[d,_],[/(sunos) ?([\w\.\d]*)/i],[[d,"Solaris"],_],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[d,_]]},Z=function(t,e){if(typeof t===c&&(e=t,t=s),!(this instanceof Z))return new Z(t,e).getResult();var i=typeof n!==o&&n.navigator?n.navigator:s,r=t||(i&&i.userAgent?i.userAgent:""),m=i&&i.userAgentData?i.userAgentData:s,b=e?function(t,e){var i={};for(var r in t)e[r]&&e[r].length%2==0?i[r]=e[r].concat(t[r]):i[r]=t[r];return i}(K,e):K,w=i&&i.userAgent==r;return this.getBrowser=function(){var t,e={};return e[d]=s,e[_]=s,W.call(e,r,b.browser),e[l]=typeof(t=e[_])===u?t.replace(/[^\d\.]/g,"").split(".")[0]:s,w&&i&&i.brave&&typeof i.brave.isBrave==a&&(e[d]="Brave"),e},this.getCPU=function(){var t={};return t[v]=s,W.call(t,r,b.cpu),t},this.getDevice=function(){var t={};return t[p]=s,t[h]=s,t[f]=s,W.call(t,r,b.device),w&&!t[f]&&m&&m.mobile&&(t[f]=g),w&&"Macintosh"==t[h]&&i&&typeof i.standalone!==o&&i.maxTouchPoints&&i.maxTouchPoints>2&&(t[h]="iPad",t[f]=y),t},this.getEngine=function(){var t={};return t[d]=s,t[_]=s,W.call(t,r,b.engine),t},this.getOS=function(){var t={};return t[d]=s,t[_]=s,W.call(t,r,b.os),w&&!t[d]&&m&&"Unknown"!=m.platform&&(t[d]=m.platform.replace(/chrome os/i,X).replace(/macos/i,q)),t},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return r},this.setUA=function(t){return r=typeof t===u&&t.length>500?z(t,500):t,this},this.setUA(r),this};Z.VERSION="1.0.37",Z.BROWSER=G([d,_,l]),Z.CPU=G([v]),Z.DEVICE=G([h,p,f,m,g,b,y,w,T]),Z.ENGINE=Z.OS=G([d,_]),typeof e!==o?(t.exports&&(e=t.exports=Z),e.UAParser=Z):i.amdO?(r=function(){return Z}.call(e,i,e,t))===s||(t.exports=r):typeof n!==o&&(n.UAParser=Z);var $=typeof n!==o&&(n.jQuery||n.Zepto);if($&&!$.ua){var tt=new Z;$.ua=tt.getResult(),$.ua.get=function(){return tt.getUA()},$.ua.set=function(t){tt.setUA(t);var e=tt.getResult();for(var i in e)$.ua[i]=e[i]}}}("object"==typeof window?window:this)}},r={};function n(t){var e=r[t];if(void 0!==e)return e.exports;var s=r[t]={id:t,loaded:!1,exports:{}};return i[t].call(s.exports,s,s.exports,n),s.loaded=!0,s.exports}n.m=i,n.amdO={},n.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return n.d(e,{a:e}),e},n.d=(t,e)=>{for(var i in e)n.o(e,i)&&!n.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},n.f={},n.e=t=>Promise.all(Object.keys(n.f).reduce(((e,i)=>(n.f[i](t,e),e)),[])),n.u=t=>"./js/"+t+".bundle.js",n.miniCssF=t=>"./css/"+t+".bundle.css",n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),n.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),t={},e="workspace_wp:",n.l=(i,r,s,a)=>{if(t[i])t[i].push(r);else{var o,c;if(void 0!==s)for(var u=document.getElementsByTagName("script"),l=0;l<u.length;l++){var h=u[l];if(h.getAttribute("src")==i||h.getAttribute("data-webpack")==e+s){o=h;break}}o||(c=!0,(o=document.createElement("script")).charset="utf-8",o.timeout=120,n.nc&&o.setAttribute("nonce",n.nc),o.setAttribute("data-webpack",e+s),o.src=i),t[i]=[r];var d=(e,r)=>{o.onerror=o.onload=null,clearTimeout(f);var n=t[i];if(delete t[i],o.parentNode&&o.parentNode.removeChild(o),n&&n.forEach((t=>t(r))),e)return e(r)},f=setTimeout(d.bind(null,void 0,{type:"timeout",target:o}),12e4);o.onerror=d.bind(null,o.onerror),o.onload=d.bind(null,o.onload),c&&document.head.appendChild(o)}},n.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.nmd=t=>(t.paths=[],t.children||(t.children=[]),t),(()=>{var t;n.g.importScripts&&(t=n.g.location+"");var e=n.g.document;if(!t&&e&&(e.currentScript&&(t=e.currentScript.src),!t)){var i=e.getElementsByTagName("script");if(i.length)for(var r=i.length-1;r>-1&&(!t||!/^http(s?):/.test(t));)t=i[r--].src}if(!t)throw new Error("Automatic publicPath is not supported in this browser");t=t.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),n.p=t+"../"})(),(()=>{if("undefined"!=typeof document){var t=t=>new Promise(((e,i)=>{var r=n.miniCssF(t),s=n.p+r;if(((t,e)=>{for(var i=document.getElementsByTagName("link"),r=0;r<i.length;r++){var n=(a=i[r]).getAttribute("data-href")||a.getAttribute("href");if("stylesheet"===a.rel&&(n===t||n===e))return a}var s=document.getElementsByTagName("style");for(r=0;r<s.length;r++){var a;if((n=(a=s[r]).getAttribute("data-href"))===t||n===e)return a}})(r,s))return e();((t,e,i,r,s)=>{var a=document.createElement("link");a.rel="stylesheet",a.type="text/css",n.nc&&(a.nonce=n.nc),a.onerror=a.onload=i=>{if(a.onerror=a.onload=null,"load"===i.type)r();else{var n=i&&i.type,o=i&&i.target&&i.target.href||e,c=new Error("Loading CSS chunk "+t+" failed.\n("+n+": "+o+")");c.name="ChunkLoadError",c.code="CSS_CHUNK_LOAD_FAILED",c.type=n,c.request=o,a.parentNode&&a.parentNode.removeChild(a),s(c)}},a.href=e,i?i.parentNode.insertBefore(a,i.nextSibling):document.head.appendChild(a)})(t,s,null,e,i)})),e={396:0};n.f.miniCss=(i,r)=>{e[i]?r.push(e[i]):0!==e[i]&&{119:1}[i]&&r.push(e[i]=t(i).then((()=>{e[i]=0}),(t=>{throw delete e[i],t})))}}})(),(()=>{var t={396:0};n.f.j=(e,i)=>{var r=n.o(t,e)?t[e]:void 0;if(0!==r)if(r)i.push(r[2]);else{var s=new Promise(((i,n)=>r=t[e]=[i,n]));i.push(r[2]=s);var a=n.p+n.u(e),o=new Error;n.l(a,(i=>{if(n.o(t,e)&&(0!==(r=t[e])&&(t[e]=void 0),r)){var s=i&&("load"===i.type?"missing":i.type),a=i&&i.target&&i.target.src;o.message="Loading chunk "+e+" failed.\n("+s+": "+a+")",o.name="ChunkLoadError",o.type=s,o.request=a,r[1](o)}}),"chunk-"+e,e)}};var e=(e,i)=>{var r,s,[a,o,c]=i,u=0;if(a.some((e=>0!==t[e]))){for(r in o)n.o(o,r)&&(n.m[r]=o[r]);if(c)c(n)}for(e&&e(i);u<a.length;u++)s=a[u],n.o(t,s)&&t[s]&&t[s][0](),t[s]=0},i=self.webpackChunkworkspace_wp=self.webpackChunkworkspace_wp||[];i.forEach(e.bind(null,0)),i.push=e.bind(null,i.push.bind(i))})(),(()=>{"use strict";var t=n(232),e=n.n(t);function i(t,e,i,r,n,s,a){try{var o=t[s](a),c=o.value}catch(t){return void i(t)}o.done?e(c):Promise.resolve(c).then(r,n)}function r(t){return function(){var e=this,r=arguments;return new Promise((function(n,s){var a=t.apply(e,r);function o(t){i(a,n,s,o,c,"next",t)}function c(t){i(a,n,s,o,c,"throw",t)}o(void 0)}))}}function s(t,e){for(var i=0;i<e.length;i++){var r=e[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function a(t,e){var i,r,n,s,a={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return s={next:o(0),throw:o(1),return:o(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function o(s){return function(o){return function(s){if(i)throw new TypeError("Generator is already executing.");for(;a;)try{if(i=1,r&&(n=2&s[0]?r.return:s[0]?r.throw||((n=r.return)&&n.call(r),0):r.next)&&!(n=n.call(r,s[1])).done)return n;switch(r=0,n&&(s=[2&s[0],n.value]),s[0]){case 0:case 1:n=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,r=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!(n=a.trys,(n=n.length>0&&n[n.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!n||s[1]>n[0]&&s[1]<n[3])){a.label=s[1];break}if(6===s[0]&&a.label<n[1]){a.label=n[1],n=s;break}if(n&&a.label<n[2]){a.label=n[2],a.ops.push(s);break}n[2]&&a.ops.pop(),a.trys.pop();continue}s=e.call(t,a)}catch(t){s=[6,t],r=0}finally{i=n=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,o])}}}var o=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}var i,n,o;return i=t,o=[{key:"sendMessage",value:function(t,e){var i=new CustomEvent("MESSAGE",{detail:e});t.dispatchEvent(i)}},{key:"setScale",value:function(t,e,i){var r=window.innerWidth/e,n=window.innerHeight/i,s=Math.min(r,n),a=Math.abs(Math.round((e*s-window.innerWidth)/2)),o=Math.abs(Math.round((i*s-window.innerHeight)/2));return t.style.transformOrigin="top left",t.style.left="".concat(a,"px"),t.style.top="".concat(o,"px"),t.style.transform="scale(".concat(s,")"),{x:a,y:o,scale:s}}},{key:"sleep",value:function(t){return new Promise((function(e,i){setTimeout(e,t)}))}},{key:"getStyle",value:function(t,e){return getComputedStyle(t).getPropertyValue(e)}},{key:"getFontSize",value:function(e){return parseInt(t.getStyle(e,"font-size").replace("px",""))}},{key:"removeClassByPrefix",value:function(t,e){var i=new RegExp("(^|\\s)".concat(e,"\\S+"),"g");t.className=t.className.replace(i,"")}},{key:"hitTest",value:function(t,e){var i=t.getBoundingClientRect(),r=e.getBoundingClientRect(),n=i.left,s=i.top,a=i.right,o=i.bottom,c=r.left,u=r.top,l=r.right,h=r.bottom,d=!0;n<c&&a<c&&(d=!1),n>l&&a>l&&(d=!1);var f=!0;return s<u&&o<u&&(f=!1),s>h&&o>h&&(f=!1),!(!d||!f)}},{key:"loadScripts",value:function(e){return r((function(){var i;return a(this,(function(r){switch(r.label){case 0:i=0,r.label=1;case 1:return i<e.length?[4,t.loadScript(e[i])]:[3,4];case 2:r.sent(),console.log("".concat(i,"번째 스크립트 로드 완료")),r.label=3;case 3:return++i,[3,1];case 4:return[2,new Promise((function(t,e){t()}))]}}))}))()}},{key:"loadScript",value:function(t){return new Promise((function(e,i){var r=document.createElement("script");r.src=t,r.async=!1,document.body.appendChild(r),r.addEventListener("load",(function(){e()}))}))}},{key:"loadStyles",value:function(e){return r((function(){var i;return a(this,(function(r){switch(r.label){case 0:i=0,r.label=1;case 1:return i<e.length?[4,t.loadStyle(e[i])]:[3,4];case 2:r.sent(),console.log("".concat(i,"번째 CSS 로드 완료")),r.label=3;case 3:return++i,[3,1];case 4:return[2,new Promise((function(t,e){t()}))]}}))}))()}},{key:"loadStyle",value:function(t){return new Promise((function(e,i){var r=document.getElementsByTagName("head")[0],n=document.createElement("link");n.rel="stylesheet",n.href=t,r.appendChild(n),n.addEventListener("load",(function(){e()}))}))}},{key:"consoleStyle",value:function(t){var e,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;e=i?i.join(";"):["padding : 30px 20px","margin : 20px 0","background : linear-gradient(#0099FF, #FFFFFF)","font-size : 25px","font-weight : bold","text-align : center","color : #ffffff"].join(";"),console.log("%c ".concat(t),e)}},{key:"lockChild",value:function(t){var e=!0,i=!1,r=void 0;try{for(var n,s=t.children[Symbol.iterator]();!(e=(n=s.next()).done);e=!0)n.value.style.pointerEvents="none"}catch(t){i=!0,r=t}finally{try{e||null==s.return||s.return()}finally{if(i)throw r}}}},{key:"shuffle",value:function(t){for(var e=t.length-1;e>0;--e){var i=Math.floor(Math.random()*(e+1)),r=t[e];t[e]=t[i],t[i]=r}return t}},{key:"isMobileIOS",value:function(){var t=(new(e())).getDevice();return!("mobile"!==t.type&&"tablet"!==t.type||"iPhone"!==t.model&&"iPad"!==t.model)}}],(n=null)&&s(i.prototype,n),o&&s(i,o),t}();function c(t,e){for(var i=0;i<e.length;i++){var r=e[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function u(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}var l=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),u(this,"audio",void 0),u(this,"eventMap",void 0),u(this,"audioOption",void 0),u(this,"bindPlay",void 0),u(this,"bindPause",void 0),u(this,"bindEnded",void 0),this.audioOption=e,this.audio=new Audio,this.audio.preload="none",this.audio.src=e.src,e.loop&&(this.audio.loop=e.loop),e.volume&&(this.audio.volume=e.volume),e.autoplay&&this.play(),this.eventMap={play:[],pause:[],ended:[]},this.bindPlay=this.hnPlay.bind(this),this.bindPause=this.hnPause.bind(this),this.bindEnded=this.hnEnded.bind(this),this.audio.addEventListener("play",this.bindPlay),this.audio.addEventListener("pause",this.bindPause),this.audio.addEventListener("ended",this.bindEnded)}var e,i,r;return e=t,i=[{key:"on",value:function(t,e){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.eventMap[t].push({fn:e,once:i})}},{key:"off",value:function(t,e){var i=this.eventMap[t];if(e){for(var r=0;r<i.length;++r)if(e===i[r].fn){i.splice(r,1);break}}else this.eventMap[t]=[]}},{key:"offAll",value:function(){var t=this;Object.keys(this.eventMap).forEach((function(e){t.eventMap[e]=[]}))}},{key:"emit",value:function(t){for(var e=this.eventMap[t],i=e.length-1;i>=0;--i){var r=e[i];r.fn(),r.once&&this.off(t,r.fn)}}},{key:"hnPlay",value:function(){this.emit("play")}},{key:"hnPause",value:function(){this.emit("pause")}},{key:"hnEnded",value:function(){this.stop(),this.emit("ended")}},{key:"load",value:function(){this.audio.load()}},{key:"play",value:function(){this.stop(),this.audio.play()}},{key:"resume",value:function(){this.audio.play()}},{key:"pause",value:function(){this.audio.pause()}},{key:"stop",value:function(){this.audio.readyState>0&&(this.audio.pause(),this.audio.currentTime=0)}},{key:"seek",value:function(t){this.audio.currentTime=t/1e3}},{key:"paused",get:function(){var t=!1;return this.audio.paused&&0!==this.audio.currentTime&&(t=!0),t}},{key:"currentTime",get:function(){return this.audio.currentTime}},{key:"duration",get:function(){return this.audio.duration}},{key:"volume",set:function(t){t<0||t>1||(this.audio.volume=t)}}],i&&c(e.prototype,i),r&&c(e,r),t}();function h(t,e){for(var i=0;i<e.length;i++){var r=e[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function d(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}const f=new(function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),d(this,"sounds",new Map),d(this,"pauseSounds",[]),this.sounds=new Map,this.pauseSounds=[]}var e,i,r;return e=t,i=[{key:"add",value:function(t){var e=this;Array.isArray(t)?t.forEach((function(t){e._add(t)})):this._add(t)}},{key:"_add",value:function(t){if(this.isContain(t.id))console.log("".concat(t.id," 오디오 아이디는 이미 가지고 있음"));else{var e=Object.assign({volume:1,loop:!1,ignoreStop:!1,tag:"default"},t),i=new l(e);this.sounds.set(t.id,function(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{},r=Object.keys(i);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(i).filter((function(t){return Object.getOwnPropertyDescriptor(i,t).enumerable})))),r.forEach((function(e){d(t,e,i[e])}))}return t}({sound:i},e))}}},{key:"get",value:function(t){var e=null;return this.sounds.get(t)&&(e=this.sounds.get(t).sound),e}},{key:"on",value:function(t,e,i){var r=arguments.length>3&&void 0!==arguments[3]&&arguments[3],n=this.get(t);n&&n.on(e,i,r)}},{key:"off",value:function(t,e,i){var r=this.get(t);r&&r.off(e,i)}},{key:"play",value:function(t,e){var i=this.get(t);i&&(e&&(this.off(t,"ended"),this.on(t,"ended",e,!0)),i.play())}},{key:"resume",value:function(t){var e=this.get(t);e&&e.resume()}},{key:"pause",value:function(t){var e=this.get(t);e&&e.pause()}},{key:"stop",value:function(t){var e=this.get(t);e&&e.stop()}},{key:"seek",value:function(t,e){var i=this.get(t);i&&i.seek(e)}},{key:"stopAll",value:function(){var t=!0,e=!1,i=void 0;try{for(var r,n=this.sounds.values()[Symbol.iterator]();!(t=(r=n.next()).done);t=!0){var s=r.value;s.ignoreStop||"bgm"===s.tag||s.sound.stop()}}catch(t){e=!0,i=t}finally{try{t||null==n.return||n.return()}finally{if(e)throw i}}}},{key:"resumeAll",value:function(){this.pauseSounds.forEach((function(t){t.resume()})),this.pauseSounds=[]}},{key:"pauseAll",value:function(){var t=!0,e=!1,i=void 0;try{for(var r,n=this.sounds.values()[Symbol.iterator]();!(t=(r=n.next()).done);t=!0){var s=r.value;s.sound.paused||(s.sound.pause(),this.pauseSounds.push(s.sound))}}catch(t){e=!0,i=t}finally{try{t||null==n.return||n.return()}finally{if(e)throw i}}}},{key:"getPaused",value:function(t){var e=this.get(t);return!!e&&e.paused}},{key:"getDuration",value:function(t){var e=-1,i=this.get(t);return i?e=i.duration:e}},{key:"getCurrentTime",value:function(t){var e=-1,i=this.get(t);return i?e=i.currentTime:e}},{key:"setVolume",value:function(t,e){if(e){var i=this.get(e);if(!i)return;i.volume=t}else{var r=!0,n=!1,s=void 0;try{for(var a,o=this.sounds.values()[Symbol.iterator]();!(r=(a=o.next()).done);r=!0)a.value.sound.volume=t}catch(t){n=!0,s=t}finally{try{r||null==o.return||o.return()}finally{if(n)throw s}}}}},{key:"loadAll",value:function(){var t=!0,e=!1,i=void 0;try{for(var r,n=this.sounds.values()[Symbol.iterator]();!(t=(r=n.next()).done);t=!0)r.value.sound.load()}catch(t){e=!0,i=t}finally{try{t||null==n.return||n.return()}finally{if(e)throw i}}}},{key:"isContain",value:function(t){var e=!1,i=!0,r=!1,n=void 0;try{for(var s,a=this.sounds.keys()[Symbol.iterator]();!(i=(s=a.next()).done);i=!0)if(t===s.value){e=!0;break}}catch(t){r=!0,n=t}finally{try{i||null==a.return||a.return()}finally{if(r)throw n}}return e}}],i&&h(e.prototype,i),r&&h(e,r),t}());function p(t,e){for(var i=0;i<e.length;i++){var r=e[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function _(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}var v=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),_(this,"name",void 0),_(this,"id",void 0),_(this,"messageHandler",void 0),this.name="MODULE",this.messageHandler=this.hnMessage.bind(this),window.addEventListener("MESSAGE",this.messageHandler)}var e,i,r;return e=t,(i=[{key:"hnMessage",value:function(t){var e=t.detail;"KEYDOWN_ENTER_SPACE"===e.message&&this.keydownEnterSpace(e.target)}},{key:"init",value:function(){}},{key:"reset",value:function(){}},{key:"restore",value:function(){}},{key:"show",value:function(){}},{key:"hide",value:function(){}},{key:"keydownEnterSpace",value:function(t){}},{key:"playEffectButton",value:function(){f.play("button")}},{key:"playEffectExample",value:function(){f.play("example")}}])&&p(e.prototype,i),r&&p(e,r),t}();function m(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function g(t,e){for(var i=0;i<e.length;i++){var r=e[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function y(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function b(t,e,i){return b="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,i){var r=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=w(t)););return t}(t,e);if(r){var n=Object.getOwnPropertyDescriptor(r,e);return n.get?n.get.call(i||t):n.value}},b(t,e,i||t)}function w(t){return w=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},w(t)}function T(t,e){return!e||"object"!=((i=e)&&"undefined"!=typeof Symbol&&i.constructor===Symbol?"symbol":typeof i)&&"function"!=typeof e?m(t):e;var i}function E(t,e){return E=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},E(t,e)}function S(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var i,r=w(t);if(e){var n=w(this).constructor;i=Reflect.construct(r,arguments,n)}else i=r.apply(this,arguments);return T(this,i)}}var x=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&E(t,e)}(s,t);var e,i,r,n=S(s);function s(t,e){var i;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,s),y(m(i=n.call(this)),"tabList",void 0),y(m(i),"tabPannel",void 0),y(m(i),"prevButton",void 0),y(m(i),"nextButton",void 0),y(m(i),"tabs",void 0),y(m(i),"pannels",void 0),y(m(i),"index",0),y(m(i),"prevIndex",0),i.tabList=document.querySelector(t),i.tabPannel=document.querySelector(e),i.prevButton=i.tabList.querySelector(".btn-prev"),i.nextButton=i.tabList.querySelector(".btn-next"),i.tabs=Array.from(i.tabList.querySelectorAll(".tab")),i.pannels=Array.from(i.tabPannel.querySelectorAll(".tabpannel")),i.tabs.forEach((function(t,e){t.addEventListener("click",(function(){i.playEffectButton(),i.setTab(e)})),t.ariaSelected="false",t.ariaLabel="".concat(e+1,"페이지로 가기")})),i.pannels.forEach((function(t,e){t.ariaLabel="".concat(e+1,"번 페이지")})),i.prevButton.addEventListener("click",(function(){var t=i.index-1;t>-1&&(i.playEffectButton(),i.setTab(t))})),i.nextButton.addEventListener("click",(function(){var t=i.index+1;t<i.tabs.length&&(i.playEffectButton(),i.setTab(t))})),i.prevButton.ariaLabel="이전 페이지로 가기",i.nextButton.ariaLabel="다음 페이지로 가기",i.setTab(0),i}return e=s,(i=[{key:"currentPannel",get:function(){return this.pannels[this.index]}},{key:"reset",value:function(){b(w(s.prototype),"reset",this).call(this),this.setTab(0)}},{key:"setTab",value:function(t){this.pannels.forEach((function(t){t.classList.add("hide")})),this.tabs.forEach((function(t){t.ariaSelected="false"})),this.pannels[t].classList.remove("hide"),this.tabs[t].ariaSelected="true",this.prevIndex=this.index,this.index=t,0===t?(this.prevButton.classList.add("disable"),this.nextButton.classList.remove("disable")):t===this.tabs.length-1?(this.prevButton.classList.remove("disable"),this.nextButton.classList.add("disable")):(this.prevButton.classList.remove("disable"),this.nextButton.classList.remove("disable")),o.sendMessage(window,{message:"TAB_UI_SET",self:this,tabCurrentIndex:this.index,tabPrevIndex:this.prevIndex})}}])&&g(e.prototype,i),r&&g(e,r),s}(v);n(778);function j(t,e){for(var i=0;i<e.length;i++){var r=e[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}var A=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}var e,i,r;return e=t,r=[{key:"sendMessage",value:function(t,e,i){var r=new CustomEvent(e,{detail:i});t.dispatchEvent(r)}},{key:"consoleStyle",value:function(t){var e,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;e=i?i.join(";"):["padding : 30px 20px","margin : 20px 0","background : linear-gradient(#0099FF, #FFFFFF)","font-size : 25px","font-weight : bold","text-align : center","color : #ffffff"].join(";"),console.log("%c ".concat(t),e)}},{key:"calculateDistance",value:function(t,e){var i=e.x-t.x,r=e.y-t.y;return Math.sqrt(Math.pow(i,2)+Math.pow(r,2))}},{key:"sleep",value:function(t){return new Promise((function(e,i){setTimeout(e,t)}))}}],(i=null)&&j(e.prototype,i),r&&j(e,r),t}();function L(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,r=new Array(e);i<e;i++)r[i]=t[i];return r}function R(t,e){for(var i=0;i<e.length;i++){var r=e[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function P(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function C(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var i=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=i){var r,n,s=[],a=!0,o=!1;try{for(i=i.call(t);!(a=(r=i.next()).done)&&(s.push(r.value),!e||s.length!==e);a=!0);}catch(t){o=!0,n=t}finally{try{a||null==i.return||i.return()}finally{if(o)throw n}}return s}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return L(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);"Object"===i&&t.constructor&&(i=t.constructor.name);if("Map"===i||"Set"===i)return Array.from(i);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return L(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var k=function(){function t(e,i,r,n){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),P(this,"mc",void 0),P(this,"s",void 0),P(this,"e",void 0),P(this,"queue",void 0),P(this,"bindTick",void 0),this.mc=e,this.s=i,this.e=r,this.queue=n,this.queue||(this.queue=new Map),this.bindTick=this.hnTick.bind(this)}var e,i,r;return e=t,(i=[{key:"start",value:function(){this.resume()}},{key:"end",value:function(){A.sendMessage(window,"FRAME_CALL_END",{mc:this.mc}),this.dispose()}},{key:"dispose",value:function(){this.mc&&(this.pause(),this.queue=null)}},{key:"resume",value:function(){this.mc&&(this.mc.addEventListener("tick",this.bindTick),this.mc.visible=!0,this.mc.gotoAndPlay(this.s))}},{key:"pause",value:function(){this.mc&&(this.mc.removeEventListener("tick",this.bindTick),this.mc.stop())}},{key:"hnTick",value:function(t){if(this.queue){var e,i,r=!0,n=!1,s=void 0;try{for(var a,o=this.queue[Symbol.iterator]();!(r=(a=o.next()).done);r=!0){var c=C(a.value,2),u=c[0],l=c[1];if("string"==typeof u){if("end"===u&&this.mc.currentFrame===this.mc.totalFrames-1){e=u,i=l;break}if(this.mc.currentLabel===u){e=u,i=l;break}}else if(this.mc.currentFrame===u){e=u,i=l;break}}}catch(t){n=!0,s=t}finally{try{r||null==o.return||o.return()}finally{if(n)throw s}}"string"==typeof this.e?this.mc.currentLabel===this.e&&this.end():this.mc.currentFrame===this.e&&this.end(),e&&i&&this.executeQueue(e,i)}}},{key:"executeQueue",value:function(t,e){e(),this.queue&&this.queue.delete(t)}}])&&R(e.prototype,i),r&&R(e,r),t}();function I(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,r=new Array(e);i<e;i++)r[i]=t[i];return r}function O(t,e){for(var i=0;i<e.length;i++){var r=e[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function M(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function D(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var i=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=i){var r,n,s=[],a=!0,o=!1;try{for(i=i.call(t);!(a=(r=i.next()).done)&&(s.push(r.value),!e||s.length!==e);a=!0);}catch(t){o=!0,n=t}finally{try{a||null==i.return||i.return()}finally{if(o)throw n}}return s}}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return I(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);"Object"===i&&t.constructor&&(i=t.constructor.name);if("Map"===i||"Set"===i)return Array.from(i);if("Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i))return I(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var F=function(){function t(e){var i=this;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),M(this,"args",void 0),M(this,"doc",document),M(this,"container",void 0),M(this,"canvas",void 0),M(this,"exportRoot",void 0),M(this,"stage",void 0),M(this,"lib",void 0),M(this,"frameCallMap",new Map),this.args=e,this.container=this.doc.querySelector(this.args.node),this.container.style.width="".concat(this.args.canvasSize.w,"px"),this.container.style.height="".concat(this.args.canvasSize.h,"px"),this.canvas=this.doc.createElement("canvas"),this.canvas.style.position="absolute",this.canvas.width=this.args.canvasSize.w,this.canvas.height=this.args.canvasSize.h,this.container.appendChild(this.canvas),window.addEventListener("FRAME_CALL_END",(function(t){var e=t.detail.mc,r=!0,n=!1,s=void 0;try{for(var a,o=i.frameCallMap[Symbol.iterator]();!(r=(a=o.next()).done);r=!0){var c=D(a.value,2),u=c[0];c[1];u===e&&i.frameCallMap.delete(u)}}catch(t){n=!0,s=t}finally{try{r||null==o.return||o.return()}finally{if(n)throw s}}})),this.loadAnimation()}var e,i,r;return e=t,i=[{key:"getContainer",value:function(){return this.container}},{key:"loadAnimation",value:function(){var t=this,e=this.doc.createElement("script");e.src=this.args.src,e.async=!1,this.doc.body.appendChild(e),e.addEventListener("load",(function(){console.log("load complete :: ".concat(t.args.src)),t.createNode()}))}},{key:"createNode",value:function(){var t=this,e=Object.keys(window.AdobeAn.compositions)[0],i=window.AdobeAn.getComposition(e),r=new createjs.LoadQueue(!1);if(r.installPlugin(createjs.Sound),r.addEventListener("fileload",(function(e){t.handleFileLoad(e,i)})),r.addEventListener("complete",(function(e){t.handleComplete(e,i)})),o.isMobileIOS()){var n=i.getLibrary();createjs.MotionGuidePlugin.install();var s=n.properties.manifest;s.map((function(e){t.args.manifestSrc&&(e.src=t.args.manifestSrc.concat(e.src))})),s.length>0?r.loadManifest(s):this.setupComplete(n)}else{var a=i.getLibrary();createjs.MotionGuidePlugin.install();var c=a.properties.manifest,u=[];c.map((function(e){t.args.manifestSrc&&(e.src=t.args.manifestSrc.concat(e.src)),-1==e.src.indexOf(".mp3")?u.push(e):f.add({id:e.id,src:e.src,tag:t.args.stageContent})})),c.length>0?r.loadManifest(u):this.setupComplete(a)}}},{key:"handleFileLoad",value:function(t,e){var i=e.getImages();t&&"image"==t.item.type&&(i[t.item.id]=t.result)}},{key:"handleComplete",value:function(t,e){for(var i=e.getLibrary(),r=e.getSpriteSheet(),n=t.target,s=i.ssMetadata,a=0;a<s.length;a++)r[s[a].name]=new createjs.SpriteSheet({images:[n.getResult(s[a].name)],frames:s[a].frames});this.setupComplete(i)}},{key:"setupComplete",value:function(e){this.lib=e,this.exportRoot=new e[this.args.stageContent],this.exportRoot.name="exportRoot_".concat(this.args.stageContent),this.stage=new e.Stage(this.canvas),createjs.Touch.enable(this.stage,!0,!1),this.stage.enableMouseOver(),this.addGlobal(),createjs.Ticker.framerate=e.properties.fps,createjs.Ticker.timingMode=createjs.Ticker.TIMEOUT,createjs.Ticker.interval=30,createjs.Ticker.addEventListener("tick",this.stage),this.stage.addChild(this.exportRoot),this.stage.tickEnabled=!1,o.sendMessage(window,{message:t.READY_COMPLETE,self:this}),this.args.autoplay&&this.start()}},{key:"start",value:function(){this.stage.tickEnabled=!0,o.sendMessage(window,{message:t.START,self:this})}},{key:"pause",value:function(){this.stage&&(this.stage.tickEnabled=!1)}},{key:"resume",value:function(){this.stage&&(this.stage.tickEnabled=!0)}},{key:"addGlobal",value:function(){window.playSound=this.playSound.bind(this)}},{key:"playSound",value:function(t,e,i){o.isMobileIOS()?createjs.Sound.play(t,{interrupt:createjs.Sound.INTERRUPT_EARLY,loop:e,offset:i}).volume=window.globalVolume:f.play(t)}},{key:"play",value:function(t,e,i,r){"end"===i&&(i=t.totalFrames-1);var n,s=new Map(r);this.frameCallMap.has(t)?(n=this.frameCallMap.get(t))&&(n.dispose(),n=new k(t,e,i,s),this.frameCallMap.set(t,n),n.start()):(n=new k(t,e,i,s),this.frameCallMap.set(t,n),n.start())}},{key:"stopPlayQueue",value:function(t){var e=!0,i=!1,r=void 0;try{for(var n,s=this.frameCallMap[Symbol.iterator]();!(e=(n=s.next()).done);e=!0){var a=D(n.value,2),o=a[0],c=a[1];o===t&&(c.dispose(),this.frameCallMap.delete(o))}}catch(t){i=!0,r=t}finally{try{e||null==s.return||s.return()}finally{if(i)throw r}}}},{key:"stopAllPlayQueue",value:function(){var t=!0,e=!1,i=void 0;try{for(var r,n=this.frameCallMap[Symbol.iterator]();!(t=(r=n.next()).done);t=!0){var s=D(r.value,2),a=s[0];s[1].dispose(),this.frameCallMap.delete(a)}}catch(t){e=!0,i=t}finally{try{t||null==n.return||n.return()}finally{if(e)throw i}}}},{key:"playOnce",value:function(t){var e=this;return new Promise((function(i){e.play(t,0,"end",[["end",function(){i()}]])}))}},{key:"playFromTo",value:function(t,e,i){var r=this;return new Promise((function(n){r.play(t,e,i,[[i,function(){n()}]])}))}},{key:"playOnceLabelMC",value:function(t,e){var i=this;return new Promise((function(r,n){if(i.hasLabel(t,e)){t.gotoAndStop(e);var s=i.getChildAt(t,0);i.play(s,0,"end",[["end",function(){r()}]])}else console.log("무비클립에 ".concat(e,"라벨이 없음")),r()}))}},{key:"lock",value:function(){console.log("lock!!!!"),this.exportRoot.mouseEnabled=!1}},{key:"unlock",value:function(){console.log("unlock!!!!"),this.exportRoot.mouseEnabled=!0}},{key:"getRoot",value:function(){return this.exportRoot}},{key:"getLib",value:function(){return this.lib}},{key:"getStage",value:function(){return this.stage}},{key:"getCanvas",value:function(){return this.canvas}},{key:"get",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return e?e.getChildByName(t):this.exportRoot.getChildByName(t)}},{key:"find",value:function(t){var e,i,r,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,s=(e=n||this.exportRoot).getChildByName(t);if(s)return s;for(var a=e.children,o=0;o<a.length;++o){var c=a[o];if(i=c,null!=(r=createjs.MovieClip)&&"undefined"!=typeof Symbol&&r[Symbol.hasInstance]?r[Symbol.hasInstance](i):i instanceof r){var u=this.find(t,c);if(u)return u}}}},{key:"setHitArea",value:function(t){var e=t.nominalBounds,i=new createjs.Shape;i.graphics.beginFill("#ff0000").drawRect(e.x,e.y,e.width,e.height),t.hitArea=i}},{key:"getBound",value:function(t){var e=t,i=e.getBounds();return i||(i=e.nominalBounds),i}},{key:"getNominalBound",value:function(t){return t.nominalBounds}},{key:"hitTest",value:function(t,e,i){if(!t.visible||!t.mouseEnabled)return!1;var r=t.globalToLocal(e,i),n=this.getBound(t);return r.x>=0&&r.x<=n.width&&r.y>=0&&r.y<=n.height}},{key:"overlap",value:function(t,e){var i=this.setupPoint(t),r=this.setupPoint(e);return!(i.tr.y>r.bl.y||i.bl.y<r.tr.y||i.tr.x<r.bl.x||i.bl.x>r.tr.x)}},{key:"setupPoint",value:function(t){var e=this.getBound(t),i={x:t.x-t.regX,y:t.y-t.regY},r=t.parent.localToGlobal(i.x,i.y);return{tr:{x:r.x+e.width,y:r.y},bl:{x:r.x,y:r.y+e.height}}}},{key:"disableButton",value:function(t){t.alpha=.5,t.mouseEnabled=!1}},{key:"enableButton",value:function(t){t.alpha=1,t.mouseEnabled=!0}},{key:"isClicked",value:function(t,e){if(t.name===e)return!0;do{if(t.name===e)return!0}while(t=t.parent);return!1}},{key:"alignInY",value:function(t){for(var e,i,r,n,s=0;s<t.length;++s)for(var a=s+1;a<t.length;++a)if(e=Math.floor(t[s].x),r=Math.floor(t[s].y),i=Math.floor(t[a].x),r>(n=Math.floor(t[a].y))){var o;o=t[s],t[s]=t[a],t[a]=o}else if(Math.abs(r-n)<5&&e>i){var c;c=t[s],t[s]=t[a],t[a]=c}}},{key:"alignInX",value:function(t){for(var e,i,r,n,s=0;s<t.length;++s)for(var a=s+1;a<t.length;++a)if(e=Math.floor(t[s].x),r=Math.floor(t[s].y),i=Math.floor(t[a].x),n=Math.floor(t[a].y),e>i){var o;o=t[s],t[s]=t[a],t[a]=o}else if(Math.abs(e-i)<5&&r>n){var c;c=t[s],t[s]=t[a],t[a]=c}}},{key:"getMovieClipsByName",value:function(t,e){for(var i,r=[],n=0;i=this.find("".concat(t,"_").concat(n),e);++n)r.push(i);return 0===r.length&&console.warn("".concat(t," 무비클립을 찾을수 없음")),r}},{key:"getChildAt",value:function(t,e){return t.getChildAt(e)}},{key:"hasLabel",value:function(t,e){return t.labels.some((function(t){return t.label===e}))}}],i&&O(e.prototype,i),r&&O(e,r),t}();function N(t,e){for(var i=0;i<e.length;i++){var r=e[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function B(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}M(F,"START","ccstart"),M(F,"READY_COMPLETE","ccreadycomplete");var U=function(){function t(e){var i=this;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),B(this,"root",void 0),B(this,"cc",void 0),B(this,"mcRoot",void 0),B(this,"mcAni",void 0),this.root=document.querySelector(".cc-pop-quiz"),this.cc=new F(e),window.addEventListener("MESSAGE",(function(t){var e=t.detail;if(e.message===F.READY_COMPLETE)e.self===i.cc&&i.initCC()}))}var e,i,r;return e=t,(i=[{key:"initCC",value:function(){var t=this;this.mcRoot=this.cc.getRoot(),this.cc.start(),this.mcRoot.stop(),this.mcAni=this.cc.find("mc_ani"),this.playIntro();var e=this.cc.find("btn_close");e.gotoAndStop(0),e.cursor="pointer",e.addEventListener("rollover",(function(){e.gotoAndStop(1)})),e.addEventListener("rollout",(function(){e.gotoAndStop(0)})),e.addEventListener("click",(function(){f.stopAll(),f.play("button"),t.mcAni.gotoAndStop(0),t.mcAni.visible=!1,t.root.classList.add("remove")}))}},{key:"playIntro",value:function(){var t=this,e="e_intro";this.cc.play(this.mcAni,"s_intro",e,[[e,function(){t.mcAni.stop(),t.cc.find("btn_start").addEventListener("click",(function(){f.stopAll(),f.play("button"),t.cc.play(t.mcAni,"s_ending","e_ending",[["e_ending",function(){t.mcAni.stop()}]])}))}]])}}])&&N(e.prototype,i),r&&N(e,r),t}();function H(t,e){for(var i=0;i<e.length;i++){var r=e[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function X(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}n.e(119).then(n.bind(n,119)),window.bound={x:0,y:0,scale:1},window.globalVolume=1;var q=function(){function t(e){var i=this;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),X(this,"options",void 0),X(this,"contentRoot",void 0),X(this,"tabUI",void 0),X(this,"tabInfo",void 0),X(this,"messageHandler",void 0),this.options=e,this.contentRoot=document.querySelector(".root"),document.querySelectorAll("img").forEach((function(t){t.draggable=!1})),this.contentRoot.addEventListener("keydown",(function(t){"Enter"!==t.key&&" "!==t.key&&"Spacebar"!==t.key||o.sendMessage(window,{message:"KEYDOWN_ENTER_SPACE",target:t.target})})),f.add({id:"button",src:"./common/audio/button.mp3",ignoreStop:!0}),f.add({id:"right",src:"./common/audio/correct.mp3",ignoreStop:!0}),f.add({id:"wrong",src:"./common/audio/wrong.mp3",ignoreStop:!0}),f.add({id:"example",src:"./common/audio/example.mp3",ignoreStop:!0}),f.add({id:"coloring",src:"./common/audio/praise_coloring.mp3",ignoreStop:!0}),f.add({id:"save",src:"./common/audio/save.mp3",ignoreStop:!0}),window.bound=o.setScale(this.contentRoot,1280,720),setTimeout((function(){window.bound=o.setScale(i.contentRoot,1280,720)}),1e3),window.addEventListener("resize",(function(){window.bound=o.setScale(i.contentRoot,1280,720)})),window.addEventListener("orientationchange",(function(){setTimeout((function(){window.bound=o.setScale(i.contentRoot,1280,720)}),1e3)})),this.contentRoot.style.visibility="inherit",this.messageHandler=this.hnMessage.bind(this),window.addEventListener("MESSAGE",this.messageHandler),this.init()}var e,i,r;return e=t,(i=[{key:"init",value:function(){var t=this.options.pages;if(1===t.length){var e=t[0];e.init(),e.activate()}else t.forEach((function(t){t.init()})),this.setupTab();this.setupHOver(),this.setupPopQuiz()}},{key:"hnMessage",value:function(t){var e=t.detail;"TAB_UI_SET"===e.message&&(this.tabInfo={currentTab:e.tabCurrentIndex,prevTab:e.tabPrevIndex},this.updateTab())}},{key:"setupPopQuiz",value:function(){this.options.popQuizConfig&&(window.location.protocol.includes("file")?alert("깜짝 퀴즈는 서버 환경에서만 동작하는 기능이 있습니다. 서버 환경에서 테스트해주세요."):new U(this.options.popQuizConfig))}},{key:"setupTab",value:function(){this.tabUI=new x(".tablist-container",".tabpannel-container")}},{key:"updateTab",value:function(){var t=this.tabInfo.currentTab;this.options.pages.forEach((function(e,i){i===t?e.activate():e.deactivate()}))}},{key:"setupHOver",value:function(){document.querySelectorAll("[data-hover=true]").forEach((function(t){t.addEventListener("pointerover",(function(e){"mouse"===e.pointerType&&t.classList.add("mouse-over")})),t.addEventListener("pointerout",(function(e){"mouse"===e.pointerType&&t.classList.remove("mouse-over")}))}))}}])&&H(e.prototype,i),r&&H(e,r),t}();function G(t,e){for(var i=0;i<e.length;i++){var r=e[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function Y(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}var V=function(){function t(e,i){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),Y(this,"button",void 0),Y(this,"clickHandler",void 0),Y(this,"options",void 0),this.button=e,this.options=i,f.add(i),this.clickHandler=this.hnClick.bind(this),this.button.addEventListener("click",this.clickHandler)}var e,i,r;return e=t,(i=[{key:"stop",value:function(){f.stop(this.options.id)}},{key:"hnClick",value:function(){f.stopAll(),f.play(this.options.id),o.sendMessage(window,{message:"PLAY_DIRECTION",self:this})}}])&&G(e.prototype,i),r&&G(e,r),t}();function z(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function W(t,e){for(var i=0;i<e.length;i++){var r=e[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function J(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function Q(t,e,i){return Q="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,i){var r=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=K(t)););return t}(t,e);if(r){var n=Object.getOwnPropertyDescriptor(r,e);return n.get?n.get.call(i||t):n.value}},Q(t,e,i||t)}function K(t){return K=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},K(t)}function Z(t,e){return!e||"object"!=((i=e)&&"undefined"!=typeof Symbol&&i.constructor===Symbol?"symbol":typeof i)&&"function"!=typeof e?z(t):e;var i}function $(t,e){return $=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},$(t,e)}function tt(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var i,r=K(t);if(e){var n=K(this).constructor;i=Reflect.construct(r,arguments,n)}else i=r.apply(this,arguments);return Z(this,i)}}var et=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&$(t,e)}(s,t);var e,i,r,n=tt(s);function s(){var t;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,s),J(z(t=n.call(this)),"texts",void 0),J(z(t),"ids",[]),t.texts=Array.from(document.querySelectorAll("[data-audio]")),console.log("sound text count :: ",t.texts.length),t.texts.forEach((function(e,i){if(e.dataset.audio){e.role="button",e.ariaPressed="false",e.tabIndex=0,e.ariaLabel=e.ariaLabel||"음성 재생";var r={id:"audio-text-".concat(i),src:e.dataset.audio};f.add(r),t.ids.push(r.id)}e.style.cursor="pointer",e.addEventListener("pointerover",(function(t){"mouse"===t.pointerType&&(e.classList.contains("on")||e.classList.add("over"))})),e.addEventListener("pointerout",(function(t){"mouse"===t.pointerType&&(e.classList.contains("on")||e.classList.remove("over"))})),e.addEventListener("click",(function(){t.resetTexts(),e.classList.add("on"),t.playSound(e)}))})),t}return e=s,(i=[{key:"reset",value:function(){Q(K(s.prototype),"reset",this).call(this),this.resetTexts(),f.stopAll()}},{key:"keydownEnterSpace",value:function(t){Q(K(s.prototype),"keydownEnterSpace",this).call(this,t);for(var e=0,i=this.texts.length;e<i;++e){var r=this.texts[e];if(t===r){this.resetTexts(),r.classList.add("on"),this.playSound(r);break}}}},{key:"playSound",value:function(t){var e=this.texts.indexOf(t);f.stopAll(),t.ariaPressed="true",f.play(this.ids[e],(function(){t.classList.remove("on"),t.ariaPressed="false"}))}},{key:"resetTexts",value:function(){this.texts.forEach((function(t){t.classList.remove("over","on")}))}}])&&W(e.prototype,i),r&&W(e,r),s}(v);function it(t,e){for(var i=0;i<e.length;i++){var r=e[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function rt(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function nt(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function st(t,e){for(var i=0;i<e.length;i++){var r=e[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function at(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function ot(t,e,i){return ot="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,i){var r=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=ct(t)););return t}(t,e);if(r){var n=Object.getOwnPropertyDescriptor(r,e);return n.get?n.get.call(i||t):n.value}},ot(t,e,i||t)}function ct(t){return ct=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},ct(t)}function ut(t,e){return!e||"object"!=((i=e)&&"undefined"!=typeof Symbol&&i.constructor===Symbol?"symbol":typeof i)&&"function"!=typeof e?nt(t):e;var i}function lt(t,e){return lt=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},lt(t,e)}function ht(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var i,r=ct(t);if(e){var n=ct(this).constructor;i=Reflect.construct(r,arguments,n)}else i=r.apply(this,arguments);return ut(this,i)}}var dt=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&lt(t,e)}(s,t);var e,i,r,n=ht(s);function s(t){var e;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,s),at(nt(e=n.call(this,t)),"cc",void 0),at(nt(e),"playingAni",void 0),at(nt(e),"chars",void 0),at(nt(e),"speakers",void 0),at(nt(e),"items",void 0),at(nt(e),"itemIndex",void 0),at(nt(e),"repeatBar",void 0),at(nt(e),"charOrder",void 0),at(nt(e),"playInfo",void 0),e.cc=new F({node:".animate-cc",src:"./cc/repeat/animation.js",manifestSrc:"./cc/repeat/",stageContent:"animation",canvasSize:{w:1280,h:720},autoplay:!1}),e.charOrder=t.charOrder,window.addEventListener("MESSAGE",(function(t){var i=t.detail;if(i.message===F.READY_COMPLETE)i.self===e.cc&&(e.initSounds(),e.initCC())})),e.chars=[],e}return e=s,(i=[{key:"init",value:function(){var t=this;ot(ct(s.prototype),"init",this).call(this),this.items=Array.from(document.querySelectorAll(".btn-item")),this.speakers=Array.from(document.querySelectorAll(".btn-speaker")),this.playInfo={char_0:new Array(this.items.length).fill(0),char_1:new Array(this.items.length).fill(0)},this.hide(this.speakers),this.items.forEach((function(e,i){e.addEventListener("click",(function(){f.stopAll(),t.stopAnimation(),f.play("button"),t.itemIndex=i,t.charOrder?(t.hide(t.speakers),t.show(t.speakers[t.charOrder[i]])):t.show(t.speakers),t.items.filter((function(t){return t!==e})).forEach((function(t){t.checked=!1})),0===t.items.filter((function(t){return t.checked})).length&&t.hide(t.speakers),t.chars.forEach((function(e){e.gotoAndStop("idle_".concat(t.itemIndex))}))}))})),this.speakers.forEach((function(e,i){e.addEventListener("click",(function(){f.stopAll(),t.stopAnimation(),e.classList.add("on");var r=t.chars[i];t.playAnimation(r,t.itemIndex)}))})),this.setMicEvent()}},{key:"reset",value:function(){ot(ct(s.prototype),"reset",this).call(this),this.items.forEach((function(t){t.checked=!1})),this.hide(this.speakers),this.stopAnimation()}},{key:"activate",value:function(){ot(ct(s.prototype),"activate",this).call(this)}},{key:"deactivate",value:function(){ot(ct(s.prototype),"deactivate",this).call(this),this.reset()}},{key:"initCC",value:function(){var t,e;this.cc.start(),this.cc.getRoot().stop(),null===(t=this.cc.find("mc_char_0"))||void 0===t||t.gotoAndStop(0),null===(e=this.cc.find("mc_char_1"))||void 0===e||e.gotoAndStop(0);for(var i=0;i<this.speakers.length;i++){var r=this.cc.find("mc_char_".concat(i));this.chars.push(r)}}},{key:"directionPlayed",value:function(){ot(ct(s.prototype),"directionPlayed",this).call(this),this.stopAnimation()}},{key:"openScript",value:function(t,e){ot(ct(s.prototype),"openScript",this).call(this,t,e),f.stopAll(),this.stopAnimation()}},{key:"playAnimation",value:function(t,e){var i,r=this,n=this.speakers.find((function(t){return t.classList.contains("on")})),s=null===(i=f.get("script_".concat(e)))||void 0===i?void 0:i.duration;this.repeatBar=document.querySelector(".repeat-bar-".concat(null==n?void 0:n.dataset.char)),t.gotoAndStop("item_".concat(e)),this.playingAni=this.cc.getChildAt(t,0),this.cc.stopPlayQueue(this.playingAni),this.cc.play(this.playingAni,0,"end",[["end",function(){t.gotoAndStop("idle_".concat(r.itemIndex)),r.repeatBar.classList.remove("hide"),r.handleRepeat(null!=s?s:0),null==n||n.classList.add("hide"),null==n||n.classList.remove("on")}]])}},{key:"stopAnimation",value:function(){var t,e;this.speakers.forEach((function(t){t.classList.remove("on")})),this.repeatBar&&this.resetRepeatBar(0),this.playingAni&&(this.playingAni.gotoAndStop("idle_".concat(this.itemIndex)),this.cc.stopPlayQueue(this.playingAni),null===(t=this.cc.find("mc_char_0"))||void 0===t||t.gotoAndStop("idle_".concat(this.itemIndex)),null===(e=this.cc.find("mc_char_1"))||void 0===e||e.gotoAndStop("idle_".concat(this.itemIndex)))}},{key:"setMicEvent",value:function(){var t=this;Array.from(document.querySelectorAll(".repeat-bar")).forEach((function(e){e.querySelector(".progress-mic").addEventListener("transitionend",t.micEnd.bind(t))}))}},{key:"handleRepeat",value:function(t){var e=this.repeatBar.querySelector(".played"),i=this.repeatBar.querySelector(".progress-mic"),r=this.repeatBar.querySelector(".progress-bar").offsetWidth,n=this.repeatBar.classList[1].split("-")[2]?parseInt(window.getComputedStyle(this.repeatBar).getPropertyValue("padding-left")):parseInt(window.getComputedStyle(this.repeatBar).getPropertyValue("padding-right"));e.style.transition="".concat(t,"s width linear"),e.style.width="100%",i.style.transition="transform ".concat(t,"s linear"),i.style.transform="translateX(".concat(r-n,"px)"),this.resetRepeatBar(t)}},{key:"resetRepeatBar",value:function(t){var e=this,i=this.repeatBar.querySelector(".played"),r=this.repeatBar.querySelector(".progress-mic"),n=function(){e.repeatBar.classList.add("hide"),i.style.transition="",i.style.width="0",r.style.transition="",r.style.transform="translateX(0)",e.charOrder||e.speakers.forEach((function(t){t.classList.remove("hide")}))};t?setTimeout(n,1e3*t+10):n()}},{key:"micEnd",value:function(t){var e=this.repeatBar.classList[1].split("-")[2];this.playInfo["char_".concat(e)][this.itemIndex]+=1,console.log(e,this.itemIndex),this.playInfo.char_0[this.itemIndex]>=1&&this.playInfo.char_1[this.itemIndex]>=1&&(this.items[this.itemIndex].classList.add("active"),this.items[this.itemIndex].classList.remove("init")),console.log("micEnd",this.playInfo)}},{key:"hide",value:function(t){Array.isArray(t)?t.forEach((function(t){t.classList.add("hide")})):t.classList.add("hide")}},{key:"show",value:function(t){Array.isArray(t)?t.forEach((function(t){t.classList.remove("hide")})):t.classList.remove("hide")}},{key:"initSounds",value:function(){[{id:"script_0",src:"./media/audio/e3_02_04_01_01_script_01.mp3"},{id:"script_1",src:"./media/audio/e3_02_04_01_01_script_02.mp3"},{id:"script_2",src:"./media/audio/e3_02_04_01_01_script_03.mp3"},{id:"script_3",src:"./media/audio/e3_02_04_01_01_script_04.mp3"},{id:"script_4",src:"./media/audio/e3_02_04_01_01_script_05.mp3"},{id:"script_5",src:"./media/audio/e3_02_04_01_01_script_06.mp3"}].forEach((function(t){f.add(t)})),f.loadAll()}}])&&st(e.prototype,i),r&&st(e,r),s}(function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),rt(this,"pageOptions",void 0),rt(this,"root",void 0),rt(this,"scriptButtons",void 0),rt(this,"audioText",void 0),rt(this,"modules",void 0),rt(this,"directionSounds",void 0),rt(this,"sTime",void 0),rt(this,"active",!0),rt(this,"messageHandler",void 0);var i=e.root;this.pageOptions=e,this.root=document.querySelector(i),this.messageHandler=this.hnMessage.bind(this),window.addEventListener("MESSAGE",this.messageHandler)}var e,i,r;return e=t,(i=[{key:"init",value:function(){this.pageOptions.modules&&(this.modules=this.pageOptions.modules,this.modules.forEach((function(t){t.init()}))),this.pageOptions.scriptIDs&&this.setScript(this.pageOptions.scriptIDs),this.setDirection(),this.setBoard(),this.setRecordTool(),this.audioText=new et}},{key:"reset",value:function(){}},{key:"activate",value:function(){this.active=!0,this.modules&&this.modules.forEach((function(t){t.restore()}))}},{key:"deactivate",value:function(){this.active=!1,this.resetModules(),this.resetQuizes()}},{key:"hnMessage",value:function(t){if(this.active){var e=t.detail;switch(e.message){case"QUIZ_MANAGER_SOLVED":case"RECORD_TOOL_RECORD_COMPLETE":this.quizSolved();break;case"RESET_MODULE":this.resetModules(e.self);break;case"OPEN_SCRIPT":this.openScript(e.scriptID,e.openButton);break;case"PLAY_DIRECTION":this.directionPlayed();break;case"KEYDOWN_ENTER_SPACE":this.keydownEnterSpace(e.target)}}}},{key:"keydownEnterSpace",value:function(t){}},{key:"resetModules",value:function(t){this.stopAllDirection(),this.modules&&this.modules.forEach((function(e){e!==t&&e.reset()}))}},{key:"resetQuizes",value:function(){this.stopAllDirection(),this.modules&&this.modules.forEach((function(t){"QUIZ_MANAGER"===t.name&&t.clear()}))}},{key:"hasRestoreData",value:function(t){return!!(t.data&&t.data.input.length>0)}},{key:"enableScript",value:function(){this.scriptButtons&&1===this.scriptButtons.length&&this.scriptButtons[0].classList.remove("disable")}},{key:"quizSolved",value:function(){this.enableScript(),this.modules&&this.modules.forEach((function(t){"DRAWTOOL"===t.name&&t.show()}))}},{key:"directionPlayed",value:function(){this.modules&&this.modules.forEach((function(t){"SOUND_BOX"!==t.name&&"SOUND_BUTTON"!==t.name&&"EXAMPLE_SOUND"!==t.name||t.reset()}))}},{key:"openScript",value:function(t,e){f.stopAll(),this.modules&&this.modules.forEach((function(i){i.id&&t&&i.id===t?(i.setupFocus(e),i.show()):i.reset()}))}},{key:"saveStudyAct",value:function(t,e){window.setActivityData("STUDY_ACT",JSON.stringify(t),JSON.stringify(e))}},{key:"playEffectButton",value:function(){f.play("button")}},{key:"playEffectExample",value:function(){f.play("example")}},{key:"playEffectSave",value:function(){f.play("save")}},{key:"stopAllDirection",value:function(){this.directionSounds&&this.directionSounds.forEach((function(t){t.stop()}))}},{key:"setDirection",value:function(){var t=this,e=this.pageOptions.directions;if(e){this.directionSounds=[];var i=Array.from(this.root.querySelectorAll(".btn-dir"));e.list.forEach((function(e,r){t.directionSounds[r]=new V(i[r],e)}))}}},{key:"setBoard",value:function(){var t=this.root.querySelector(".btn-board");t&&(t.dataset.tab="0",t.addEventListener("click",(function(){var e=t.dataset.tab;console.log("board tab index :: ",e),e&&window.callContentsTool("CLASSBOARD",e)})))}},{key:"setScript",value:function(t){var e=this;this.scriptButtons=Array.from(this.root.querySelectorAll(".main-button .btn-script")),this.scriptButtons.forEach((function(i,r){i.addEventListener("click",(function(){"none"!==o.getStyle(i,"pointer-events")&&(e.playEffectButton(),e.openScript(t[r],i))})),i.dataset.scriptId=t[r],i.ariaLabel="대본 버튼은 질문을 풀어야 활성화됩니다."}))}},{key:"setRecordTool",value:function(){var t=this,e=this.root.querySelector(".btn-record-tool");e&&e.addEventListener("click",(function(){var i=e.dataset.id,r=e.dataset.disables;r&&r.split(",").forEach((function(e){var i=t.root.querySelector(e);i&&i.classList.remove("disable")})),window.callContentsTool("TOOL",JSON.stringify({id:i,text:"",imgPath:"",audioPath:""}))}))}}])&&it(e.prototype,i),r&&it(e,r),t}());function ft(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function pt(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,t.__proto__=e}var _t,vt,mt,gt,yt,bt,wt,Tt,Et,St,xt,jt,At,Lt,Rt,Pt,Ct,kt={autoSleep:120,force3D:"auto",nullTargetWarn:1,units:{lineHeight:""}},It={duration:.5,overwrite:!1,delay:0},Ot=1e8,Mt=1e-8,Dt=2*Math.PI,Ft=Dt/4,Nt=0,Bt=Math.sqrt,Ut=Math.cos,Ht=Math.sin,Xt=function(t){return"string"==typeof t},qt=function(t){return"function"==typeof t},Gt=function(t){return"number"==typeof t},Yt=function(t){return void 0===t},Vt=function(t){return"object"==typeof t},zt=function(t){return!1!==t},Wt=function(){return"undefined"!=typeof window},Jt=function(t){return qt(t)||Xt(t)},Qt="function"==typeof ArrayBuffer&&ArrayBuffer.isView||function(){},Kt=Array.isArray,Zt=/(?:-?\.?\d|\.)+/gi,$t=/[-+=.]*\d+[.e\-+]*\d*[e\-+]*\d*/g,te=/[-+=.]*\d+[.e-]*\d*[a-z%]*/g,ee=/[-+=.]*\d+\.?\d*(?:e-|e\+)?\d*/gi,ie=/[+-]=-?[.\d]+/,re=/[^,'"\[\]\s]+/gi,ne=/^[+\-=e\s\d]*\d+[.\d]*([a-z]*|%)\s*$/i,se={},ae={},oe=function(t){return(ae=De(t,se))&&Fr},ce=function(t,e){return console.warn("Invalid property",t,"set to",e,"Missing plugin? gsap.registerPlugin()")},ue=function(t,e){return!e&&console.warn(t)},le=function(t,e){return t&&(se[t]=e)&&ae&&(ae[t]=e)||se},he=function(){return 0},de={suppressEvents:!0,isStart:!0,kill:!1},fe={suppressEvents:!0,kill:!1},pe={suppressEvents:!0},_e={},ve=[],me={},ge={},ye={},be=30,we=[],Te="",Ee=function(t){var e,i,r=t[0];if(Vt(r)||qt(r)||(t=[t]),!(e=(r._gsap||{}).harness)){for(i=we.length;i--&&!we[i].targetTest(r););e=we[i]}for(i=t.length;i--;)t[i]&&(t[i]._gsap||(t[i]._gsap=new Ki(t[i],e)))||t.splice(i,1);return t},Se=function(t){return t._gsap||Ee(pi(t))[0]._gsap},xe=function(t,e,i){return(i=t[e])&&qt(i)?t[e]():Yt(i)&&t.getAttribute&&t.getAttribute(e)||i},je=function(t,e){return(t=t.split(",")).forEach(e)||t},Ae=function(t){return Math.round(1e5*t)/1e5||0},Le=function(t){return Math.round(1e7*t)/1e7||0},Re=function(t,e){var i=e.charAt(0),r=parseFloat(e.substr(2));return t=parseFloat(t),"+"===i?t+r:"-"===i?t-r:"*"===i?t*r:t/r},Pe=function(t,e){for(var i=e.length,r=0;t.indexOf(e[r])<0&&++r<i;);return r<i},Ce=function(){var t,e,i=ve.length,r=ve.slice(0);for(me={},ve.length=0,t=0;t<i;t++)(e=r[t])&&e._lazy&&(e.render(e._lazy[0],e._lazy[1],!0)._lazy=0)},ke=function(t,e,i,r){ve.length&&!vt&&Ce(),t.render(e,i,r||vt&&e<0&&(t._initted||t._startAt)),ve.length&&!vt&&Ce()},Ie=function(t){var e=parseFloat(t);return(e||0===e)&&(t+"").match(re).length<2?e:Xt(t)?t.trim():t},Oe=function(t){return t},Me=function(t,e){for(var i in e)i in t||(t[i]=e[i]);return t},De=function(t,e){for(var i in e)t[i]=e[i];return t},Fe=function t(e,i){for(var r in i)"__proto__"!==r&&"constructor"!==r&&"prototype"!==r&&(e[r]=Vt(i[r])?t(e[r]||(e[r]={}),i[r]):i[r]);return e},Ne=function(t,e){var i,r={};for(i in t)i in e||(r[i]=t[i]);return r},Be=function(t){var e,i=t.parent||gt,r=t.keyframes?(e=Kt(t.keyframes),function(t,i){for(var r in i)r in t||"duration"===r&&e||"ease"===r||(t[r]=i[r])}):Me;if(zt(t.inherit))for(;i;)r(t,i.vars.defaults),i=i.parent||i._dp;return t},Ue=function(t,e,i,r,n){void 0===i&&(i="_first"),void 0===r&&(r="_last");var s,a=t[r];if(n)for(s=e[n];a&&a[n]>s;)a=a._prev;return a?(e._next=a._next,a._next=e):(e._next=t[i],t[i]=e),e._next?e._next._prev=e:t[r]=e,e._prev=a,e.parent=e._dp=t,e},He=function(t,e,i,r){void 0===i&&(i="_first"),void 0===r&&(r="_last");var n=e._prev,s=e._next;n?n._next=s:t[i]===e&&(t[i]=s),s?s._prev=n:t[r]===e&&(t[r]=n),e._next=e._prev=e.parent=null},Xe=function(t,e){t.parent&&(!e||t.parent.autoRemoveChildren)&&t.parent.remove&&t.parent.remove(t),t._act=0},qe=function(t,e){if(t&&(!e||e._end>t._dur||e._start<0))for(var i=t;i;)i._dirty=1,i=i.parent;return t},Ge=function(t,e,i,r){return t._startAt&&(vt?t._startAt.revert(fe):t.vars.immediateRender&&!t.vars.autoRevert||t._startAt.render(e,!0,r))},Ye=function t(e){return!e||e._ts&&t(e.parent)},Ve=function(t){return t._repeat?ze(t._tTime,t=t.duration()+t._rDelay)*t:0},ze=function(t,e){var i=Math.floor(t/=e);return t&&i===t?i-1:i},We=function(t,e){return(t-e._start)*e._ts+(e._ts>=0?0:e._dirty?e.totalDuration():e._tDur)},Je=function(t){return t._end=Le(t._start+(t._tDur/Math.abs(t._ts||t._rts||Mt)||0))},Qe=function(t,e){var i=t._dp;return i&&i.smoothChildTiming&&t._ts&&(t._start=Le(i._time-(t._ts>0?e/t._ts:((t._dirty?t.totalDuration():t._tDur)-e)/-t._ts)),Je(t),i._dirty||qe(i,t)),t},Ke=function(t,e){var i;if((e._time||!e._dur&&e._initted||e._start<t._time&&(e._dur||!e.add))&&(i=We(t.rawTime(),e),(!e._dur||ui(0,e.totalDuration(),i)-e._tTime>Mt)&&e.render(i,!0)),qe(t,e)._dp&&t._initted&&t._time>=t._dur&&t._ts){if(t._dur<t.duration())for(i=t;i._dp;)i.rawTime()>=0&&i.totalTime(i._tTime),i=i._dp;t._zTime=-1e-8}},Ze=function(t,e,i,r){return e.parent&&Xe(e),e._start=Le((Gt(i)?i:i||t!==gt?ai(t,i,e):t._time)+e._delay),e._end=Le(e._start+(e.totalDuration()/Math.abs(e.timeScale())||0)),Ue(t,e,"_first","_last",t._sort?"_start":0),ii(e)||(t._recent=e),r||Ke(t,e),t._ts<0&&Qe(t,t._tTime),t},$e=function(t,e){return(se.ScrollTrigger||ce("scrollTrigger",e))&&se.ScrollTrigger.create(e,t)},ti=function(t,e,i,r,n){return sr(t,e,n),t._initted?!i&&t._pt&&!vt&&(t._dur&&!1!==t.vars.lazy||!t._dur&&t.vars.lazy)&&Et!==Ni.frame?(ve.push(t),t._lazy=[n,r],1):void 0:1},ei=function t(e){var i=e.parent;return i&&i._ts&&i._initted&&!i._lock&&(i.rawTime()<0||t(i))},ii=function(t){var e=t.data;return"isFromStart"===e||"isStart"===e},ri=function(t,e,i,r){var n=t._repeat,s=Le(e)||0,a=t._tTime/t._tDur;return a&&!r&&(t._time*=s/t._dur),t._dur=s,t._tDur=n?n<0?1e10:Le(s*(n+1)+t._rDelay*n):s,a>0&&!r&&Qe(t,t._tTime=t._tDur*a),t.parent&&Je(t),i||qe(t.parent,t),t},ni=function(t){return t instanceof $i?qe(t):ri(t,t._dur)},si={_start:0,endTime:he,totalDuration:he},ai=function t(e,i,r){var n,s,a,o=e.labels,c=e._recent||si,u=e.duration()>=Ot?c.endTime(!1):e._dur;return Xt(i)&&(isNaN(i)||i in o)?(s=i.charAt(0),a="%"===i.substr(-1),n=i.indexOf("="),"<"===s||">"===s?(n>=0&&(i=i.replace(/=/,"")),("<"===s?c._start:c.endTime(c._repeat>=0))+(parseFloat(i.substr(1))||0)*(a?(n<0?c:r).totalDuration()/100:1)):n<0?(i in o||(o[i]=u),o[i]):(s=parseFloat(i.charAt(n-1)+i.substr(n+1)),a&&r&&(s=s/100*(Kt(r)?r[0]:r).totalDuration()),n>1?t(e,i.substr(0,n-1),r)+s:u+s)):null==i?u:+i},oi=function(t,e,i){var r,n,s=Gt(e[1]),a=(s?2:1)+(t<2?0:1),o=e[a];if(s&&(o.duration=e[1]),o.parent=i,t){for(r=o,n=i;n&&!("immediateRender"in r);)r=n.vars.defaults||{},n=zt(n.vars.inherit)&&n.parent;o.immediateRender=zt(r.immediateRender),t<2?o.runBackwards=1:o.startAt=e[a-1]}return new lr(e[0],o,e[a+1])},ci=function(t,e){return t||0===t?e(t):e},ui=function(t,e,i){return i<t?t:i>e?e:i},li=function(t,e){return Xt(t)&&(e=ne.exec(t))?e[1]:""},hi=[].slice,di=function(t,e){return t&&Vt(t)&&"length"in t&&(!e&&!t.length||t.length-1 in t&&Vt(t[0]))&&!t.nodeType&&t!==yt},fi=function(t,e,i){return void 0===i&&(i=[]),t.forEach((function(t){var r;return Xt(t)&&!e||di(t,1)?(r=i).push.apply(r,pi(t)):i.push(t)}))||i},pi=function(t,e,i){return mt&&!e&&mt.selector?mt.selector(t):!Xt(t)||i||!bt&&Bi()?Kt(t)?fi(t,i):di(t)?hi.call(t,0):t?[t]:[]:hi.call((e||wt).querySelectorAll(t),0)},_i=function(t){return t=pi(t)[0]||ue("Invalid scope")||{},function(e){var i=t.current||t.nativeElement||t;return pi(e,i.querySelectorAll?i:i===t?ue("Invalid scope")||wt.createElement("div"):t)}},vi=function(t){return t.sort((function(){return.5-Math.random()}))},mi=function(t){if(qt(t))return t;var e=Vt(t)?t:{each:t},i=Vi(e.ease),r=e.from||0,n=parseFloat(e.base)||0,s={},a=r>0&&r<1,o=isNaN(r)||a,c=e.axis,u=r,l=r;return Xt(r)?u=l={center:.5,edges:.5,end:1}[r]||0:!a&&o&&(u=r[0],l=r[1]),function(t,a,h){var d,f,p,_,v,m,g,y,b,w=(h||e).length,T=s[w];if(!T){if(!(b="auto"===e.grid?0:(e.grid||[1,Ot])[1])){for(g=-Ot;g<(g=h[b++].getBoundingClientRect().left)&&b<w;);b<w&&b--}for(T=s[w]=[],d=o?Math.min(b,w)*u-.5:r%b,f=b===Ot?0:o?w*l/b-.5:r/b|0,g=0,y=Ot,m=0;m<w;m++)p=m%b-d,_=f-(m/b|0),T[m]=v=c?Math.abs("y"===c?_:p):Bt(p*p+_*_),v>g&&(g=v),v<y&&(y=v);"random"===r&&vi(T),T.max=g-y,T.min=y,T.v=w=(parseFloat(e.amount)||parseFloat(e.each)*(b>w?w-1:c?"y"===c?w/b:b:Math.max(b,w/b))||0)*("edges"===r?-1:1),T.b=w<0?n-w:n,T.u=li(e.amount||e.each)||0,i=i&&w<0?Gi(i):i}return w=(T[t]-T.min)/T.max||0,Le(T.b+(i?i(w):w)*T.v)+T.u}},gi=function(t){var e=Math.pow(10,((t+"").split(".")[1]||"").length);return function(i){var r=Le(Math.round(parseFloat(i)/t)*t*e);return(r-r%1)/e+(Gt(i)?0:li(i))}},yi=function(t,e){var i,r,n=Kt(t);return!n&&Vt(t)&&(i=n=t.radius||Ot,t.values?(t=pi(t.values),(r=!Gt(t[0]))&&(i*=i)):t=gi(t.increment)),ci(e,n?qt(t)?function(e){return r=t(e),Math.abs(r-e)<=i?r:e}:function(e){for(var n,s,a=parseFloat(r?e.x:e),o=parseFloat(r?e.y:0),c=Ot,u=0,l=t.length;l--;)(n=r?(n=t[l].x-a)*n+(s=t[l].y-o)*s:Math.abs(t[l]-a))<c&&(c=n,u=l);return u=!i||c<=i?t[u]:e,r||u===e||Gt(e)?u:u+li(e)}:gi(t))},bi=function(t,e,i,r){return ci(Kt(t)?!e:!0===i?!!(i=0):!r,(function(){return Kt(t)?t[~~(Math.random()*t.length)]:(i=i||1e-5)&&(r=i<1?Math.pow(10,(i+"").length-2):1)&&Math.floor(Math.round((t-i/2+Math.random()*(e-t+.99*i))/i)*i*r)/r}))},wi=function(t,e,i){return ci(i,(function(i){return t[~~e(i)]}))},Ti=function(t){for(var e,i,r,n,s=0,a="";~(e=t.indexOf("random(",s));)r=t.indexOf(")",e),n="["===t.charAt(e+7),i=t.substr(e+7,r-e-7).match(n?re:Zt),a+=t.substr(s,e-s)+bi(n?i:+i[0],n?0:+i[1],+i[2]||1e-5),s=r+1;return a+t.substr(s,t.length-s)},Ei=function(t,e,i,r,n){var s=e-t,a=r-i;return ci(n,(function(e){return i+((e-t)/s*a||0)}))},Si=function(t,e,i){var r,n,s,a=t.labels,o=Ot;for(r in a)(n=a[r]-e)<0==!!i&&n&&o>(n=Math.abs(n))&&(s=r,o=n);return s},xi=function(t,e,i){var r,n,s,a=t.vars,o=a[e],c=mt,u=t._ctx;if(o)return r=a[e+"Params"],n=a.callbackScope||t,i&&ve.length&&Ce(),u&&(mt=u),s=r?o.apply(n,r):o.call(n),mt=c,s},ji=function(t){return Xe(t),t.scrollTrigger&&t.scrollTrigger.kill(!!vt),t.progress()<1&&xi(t,"onInterrupt"),t},Ai=[],Li=function(t){if(t)if(t=!t.name&&t.default||t,Wt()||t.headless){var e=t.name,i=qt(t),r=e&&!i&&t.init?function(){this._props=[]}:t,n={init:he,render:yr,add:rr,kill:wr,modifier:br,rawVars:0},s={targetTest:0,get:0,getSetter:_r,aliases:{},register:0};if(Bi(),t!==r){if(ge[e])return;Me(r,Me(Ne(t,n),s)),De(r.prototype,De(n,Ne(t,s))),ge[r.prop=e]=r,t.targetTest&&(we.push(r),_e[e]=1),e=("css"===e?"CSS":e.charAt(0).toUpperCase()+e.substr(1))+"Plugin"}le(e,r),t.register&&t.register(Fr,r,Sr)}else Ai.push(t)},Ri=255,Pi={aqua:[0,Ri,Ri],lime:[0,Ri,0],silver:[192,192,192],black:[0,0,0],maroon:[128,0,0],teal:[0,128,128],blue:[0,0,Ri],navy:[0,0,128],white:[Ri,Ri,Ri],olive:[128,128,0],yellow:[Ri,Ri,0],orange:[Ri,165,0],gray:[128,128,128],purple:[128,0,128],green:[0,128,0],red:[Ri,0,0],pink:[Ri,192,203],cyan:[0,Ri,Ri],transparent:[Ri,Ri,Ri,0]},Ci=function(t,e,i){return(6*(t+=t<0?1:t>1?-1:0)<1?e+(i-e)*t*6:t<.5?i:3*t<2?e+(i-e)*(2/3-t)*6:e)*Ri+.5|0},ki=function(t,e,i){var r,n,s,a,o,c,u,l,h,d,f=t?Gt(t)?[t>>16,t>>8&Ri,t&Ri]:0:Pi.black;if(!f){if(","===t.substr(-1)&&(t=t.substr(0,t.length-1)),Pi[t])f=Pi[t];else if("#"===t.charAt(0)){if(t.length<6&&(r=t.charAt(1),n=t.charAt(2),s=t.charAt(3),t="#"+r+r+n+n+s+s+(5===t.length?t.charAt(4)+t.charAt(4):"")),9===t.length)return[(f=parseInt(t.substr(1,6),16))>>16,f>>8&Ri,f&Ri,parseInt(t.substr(7),16)/255];f=[(t=parseInt(t.substr(1),16))>>16,t>>8&Ri,t&Ri]}else if("hsl"===t.substr(0,3))if(f=d=t.match(Zt),e){if(~t.indexOf("="))return f=t.match($t),i&&f.length<4&&(f[3]=1),f}else a=+f[0]%360/360,o=+f[1]/100,r=2*(c=+f[2]/100)-(n=c<=.5?c*(o+1):c+o-c*o),f.length>3&&(f[3]*=1),f[0]=Ci(a+1/3,r,n),f[1]=Ci(a,r,n),f[2]=Ci(a-1/3,r,n);else f=t.match(Zt)||Pi.transparent;f=f.map(Number)}return e&&!d&&(r=f[0]/Ri,n=f[1]/Ri,s=f[2]/Ri,c=((u=Math.max(r,n,s))+(l=Math.min(r,n,s)))/2,u===l?a=o=0:(h=u-l,o=c>.5?h/(2-u-l):h/(u+l),a=u===r?(n-s)/h+(n<s?6:0):u===n?(s-r)/h+2:(r-n)/h+4,a*=60),f[0]=~~(a+.5),f[1]=~~(100*o+.5),f[2]=~~(100*c+.5)),i&&f.length<4&&(f[3]=1),f},Ii=function(t){var e=[],i=[],r=-1;return t.split(Mi).forEach((function(t){var n=t.match(te)||[];e.push.apply(e,n),i.push(r+=n.length+1)})),e.c=i,e},Oi=function(t,e,i){var r,n,s,a,o="",c=(t+o).match(Mi),u=e?"hsla(":"rgba(",l=0;if(!c)return t;if(c=c.map((function(t){return(t=ki(t,e,1))&&u+(e?t[0]+","+t[1]+"%,"+t[2]+"%,"+t[3]:t.join(","))+")"})),i&&(s=Ii(t),(r=i.c).join(o)!==s.c.join(o)))for(a=(n=t.replace(Mi,"1").split(te)).length-1;l<a;l++)o+=n[l]+(~r.indexOf(l)?c.shift()||u+"0,0,0,0)":(s.length?s:c.length?c:i).shift());if(!n)for(a=(n=t.split(Mi)).length-1;l<a;l++)o+=n[l]+c[l];return o+n[a]},Mi=function(){var t,e="(?:\\b(?:(?:rgb|rgba|hsl|hsla)\\(.+?\\))|\\B#(?:[0-9a-f]{3,4}){1,2}\\b";for(t in Pi)e+="|"+t+"\\b";return new RegExp(e+")","gi")}(),Di=/hsl[a]?\(/,Fi=function(t){var e,i=t.join(" ");if(Mi.lastIndex=0,Mi.test(i))return e=Di.test(i),t[1]=Oi(t[1],e),t[0]=Oi(t[0],e,Ii(t[1])),!0},Ni=function(){var t,e,i,r,n,s,a=Date.now,o=500,c=33,u=a(),l=u,h=1e3/240,d=h,f=[],p=function i(p){var _,v,m,g,y=a()-l,b=!0===p;if((y>o||y<0)&&(u+=y-c),((_=(m=(l+=y)-u)-d)>0||b)&&(g=++r.frame,n=m-1e3*r.time,r.time=m/=1e3,d+=_+(_>=h?4:h-_),v=1),b||(t=e(i)),v)for(s=0;s<f.length;s++)f[s](m,n,g,p)};return r={time:0,frame:0,tick:function(){p(!0)},deltaRatio:function(t){return n/(1e3/(t||60))},wake:function(){Tt&&(!bt&&Wt()&&(yt=bt=window,wt=yt.document||{},se.gsap=Fr,(yt.gsapVersions||(yt.gsapVersions=[])).push(Fr.version),oe(ae||yt.GreenSockGlobals||!yt.gsap&&yt||{}),Ai.forEach(Li)),i="undefined"!=typeof requestAnimationFrame&&requestAnimationFrame,t&&r.sleep(),e=i||function(t){return setTimeout(t,d-1e3*r.time+1|0)},xt=1,p(2))},sleep:function(){(i?cancelAnimationFrame:clearTimeout)(t),xt=0,e=he},lagSmoothing:function(t,e){o=t||1/0,c=Math.min(e||33,o)},fps:function(t){h=1e3/(t||240),d=1e3*r.time+h},add:function(t,e,i){var n=e?function(e,i,s,a){t(e,i,s,a),r.remove(n)}:t;return r.remove(t),f[i?"unshift":"push"](n),Bi(),n},remove:function(t,e){~(e=f.indexOf(t))&&f.splice(e,1)&&s>=e&&s--},_listeners:f}}(),Bi=function(){return!xt&&Ni.wake()},Ui={},Hi=/^[\d.\-M][\d.\-,\s]/,Xi=/["']/g,qi=function(t){for(var e,i,r,n={},s=t.substr(1,t.length-3).split(":"),a=s[0],o=1,c=s.length;o<c;o++)i=s[o],e=o!==c-1?i.lastIndexOf(","):i.length,r=i.substr(0,e),n[a]=isNaN(r)?r.replace(Xi,"").trim():+r,a=i.substr(e+1).trim();return n},Gi=function(t){return function(e){return 1-t(1-e)}},Yi=function t(e,i){for(var r,n=e._first;n;)n instanceof $i?t(n,i):!n.vars.yoyoEase||n._yoyo&&n._repeat||n._yoyo===i||(n.timeline?t(n.timeline,i):(r=n._ease,n._ease=n._yEase,n._yEase=r,n._yoyo=i)),n=n._next},Vi=function(t,e){return t&&(qt(t)?t:Ui[t]||function(t){var e,i,r,n,s=(t+"").split("("),a=Ui[s[0]];return a&&s.length>1&&a.config?a.config.apply(null,~t.indexOf("{")?[qi(s[1])]:(e=t,i=e.indexOf("(")+1,r=e.indexOf(")"),n=e.indexOf("(",i),e.substring(i,~n&&n<r?e.indexOf(")",r+1):r)).split(",").map(Ie)):Ui._CE&&Hi.test(t)?Ui._CE("",t):a}(t))||e},zi=function(t,e,i,r){void 0===i&&(i=function(t){return 1-e(1-t)}),void 0===r&&(r=function(t){return t<.5?e(2*t)/2:1-e(2*(1-t))/2});var n,s={easeIn:e,easeOut:i,easeInOut:r};return je(t,(function(t){for(var e in Ui[t]=se[t]=s,Ui[n=t.toLowerCase()]=i,s)Ui[n+("easeIn"===e?".in":"easeOut"===e?".out":".inOut")]=Ui[t+"."+e]=s[e]})),s},Wi=function(t){return function(e){return e<.5?(1-t(1-2*e))/2:.5+t(2*(e-.5))/2}},Ji=function t(e,i,r){var n=i>=1?i:1,s=(r||(e?.3:.45))/(i<1?i:1),a=s/Dt*(Math.asin(1/n)||0),o=function(t){return 1===t?1:n*Math.pow(2,-10*t)*Ht((t-a)*s)+1},c="out"===e?o:"in"===e?function(t){return 1-o(1-t)}:Wi(o);return s=Dt/s,c.config=function(i,r){return t(e,i,r)},c},Qi=function t(e,i){void 0===i&&(i=1.70158);var r=function(t){return t?--t*t*((i+1)*t+i)+1:0},n="out"===e?r:"in"===e?function(t){return 1-r(1-t)}:Wi(r);return n.config=function(i){return t(e,i)},n};je("Linear,Quad,Cubic,Quart,Quint,Strong",(function(t,e){var i=e<5?e+1:e;zi(t+",Power"+(i-1),e?function(t){return Math.pow(t,i)}:function(t){return t},(function(t){return 1-Math.pow(1-t,i)}),(function(t){return t<.5?Math.pow(2*t,i)/2:1-Math.pow(2*(1-t),i)/2}))})),Ui.Linear.easeNone=Ui.none=Ui.Linear.easeIn,zi("Elastic",Ji("in"),Ji("out"),Ji()),jt=7.5625,Rt=2*(Lt=1/(At=2.75)),Pt=2.5*Lt,zi("Bounce",(function(t){return 1-Ct(1-t)}),Ct=function(t){return t<Lt?jt*t*t:t<Rt?jt*Math.pow(t-1.5/At,2)+.75:t<Pt?jt*(t-=2.25/At)*t+.9375:jt*Math.pow(t-2.625/At,2)+.984375}),zi("Expo",(function(t){return t?Math.pow(2,10*(t-1)):0})),zi("Circ",(function(t){return-(Bt(1-t*t)-1)})),zi("Sine",(function(t){return 1===t?1:1-Ut(t*Ft)})),zi("Back",Qi("in"),Qi("out"),Qi()),Ui.SteppedEase=Ui.steps=se.SteppedEase={config:function(t,e){void 0===t&&(t=1);var i=1/t,r=t+(e?0:1),n=e?1:0;return function(t){return((r*ui(0,.99999999,t)|0)+n)*i}}},It.ease=Ui["quad.out"],je("onComplete,onUpdate,onStart,onRepeat,onReverseComplete,onInterrupt",(function(t){return Te+=t+","+t+"Params,"}));var Ki=function(t,e){this.id=Nt++,t._gsap=this,this.target=t,this.harness=e,this.get=e?e.get:xe,this.set=e?e.getSetter:_r},Zi=function(){function t(t){this.vars=t,this._delay=+t.delay||0,(this._repeat=t.repeat===1/0?-2:t.repeat||0)&&(this._rDelay=t.repeatDelay||0,this._yoyo=!!t.yoyo||!!t.yoyoEase),this._ts=1,ri(this,+t.duration,1,1),this.data=t.data,mt&&(this._ctx=mt,mt.data.push(this)),xt||Ni.wake()}var e=t.prototype;return e.delay=function(t){return t||0===t?(this.parent&&this.parent.smoothChildTiming&&this.startTime(this._start+t-this._delay),this._delay=t,this):this._delay},e.duration=function(t){return arguments.length?this.totalDuration(this._repeat>0?t+(t+this._rDelay)*this._repeat:t):this.totalDuration()&&this._dur},e.totalDuration=function(t){return arguments.length?(this._dirty=0,ri(this,this._repeat<0?t:(t-this._repeat*this._rDelay)/(this._repeat+1))):this._tDur},e.totalTime=function(t,e){if(Bi(),!arguments.length)return this._tTime;var i=this._dp;if(i&&i.smoothChildTiming&&this._ts){for(Qe(this,t),!i._dp||i.parent||Ke(i,this);i&&i.parent;)i.parent._time!==i._start+(i._ts>=0?i._tTime/i._ts:(i.totalDuration()-i._tTime)/-i._ts)&&i.totalTime(i._tTime,!0),i=i.parent;!this.parent&&this._dp.autoRemoveChildren&&(this._ts>0&&t<this._tDur||this._ts<0&&t>0||!this._tDur&&!t)&&Ze(this._dp,this,this._start-this._delay)}return(this._tTime!==t||!this._dur&&!e||this._initted&&Math.abs(this._zTime)===Mt||!t&&!this._initted&&(this.add||this._ptLookup))&&(this._ts||(this._pTime=t),ke(this,t,e)),this},e.time=function(t,e){return arguments.length?this.totalTime(Math.min(this.totalDuration(),t+Ve(this))%(this._dur+this._rDelay)||(t?this._dur:0),e):this._time},e.totalProgress=function(t,e){return arguments.length?this.totalTime(this.totalDuration()*t,e):this.totalDuration()?Math.min(1,this._tTime/this._tDur):this.rawTime()>0?1:0},e.progress=function(t,e){return arguments.length?this.totalTime(this.duration()*(!this._yoyo||1&this.iteration()?t:1-t)+Ve(this),e):this.duration()?Math.min(1,this._time/this._dur):this.rawTime()>0?1:0},e.iteration=function(t,e){var i=this.duration()+this._rDelay;return arguments.length?this.totalTime(this._time+(t-1)*i,e):this._repeat?ze(this._tTime,i)+1:1},e.timeScale=function(t,e){if(!arguments.length)return-1e-8===this._rts?0:this._rts;if(this._rts===t)return this;var i=this.parent&&this._ts?We(this.parent._time,this):this._tTime;return this._rts=+t||0,this._ts=this._ps||-1e-8===t?0:this._rts,this.totalTime(ui(-Math.abs(this._delay),this._tDur,i),!1!==e),Je(this),function(t){for(var e=t.parent;e&&e.parent;)e._dirty=1,e.totalDuration(),e=e.parent;return t}(this)},e.paused=function(t){return arguments.length?(this._ps!==t&&(this._ps=t,t?(this._pTime=this._tTime||Math.max(-this._delay,this.rawTime()),this._ts=this._act=0):(Bi(),this._ts=this._rts,this.totalTime(this.parent&&!this.parent.smoothChildTiming?this.rawTime():this._tTime||this._pTime,1===this.progress()&&Math.abs(this._zTime)!==Mt&&(this._tTime-=Mt)))),this):this._ps},e.startTime=function(t){if(arguments.length){this._start=t;var e=this.parent||this._dp;return e&&(e._sort||!this.parent)&&Ze(e,this,t-this._delay),this}return this._start},e.endTime=function(t){return this._start+(zt(t)?this.totalDuration():this.duration())/Math.abs(this._ts||1)},e.rawTime=function(t){var e=this.parent||this._dp;return e?t&&(!this._ts||this._repeat&&this._time&&this.totalProgress()<1)?this._tTime%(this._dur+this._rDelay):this._ts?We(e.rawTime(t),this):this._tTime:this._tTime},e.revert=function(t){void 0===t&&(t=pe);var e=vt;return vt=t,(this._initted||this._startAt)&&(this.timeline&&this.timeline.revert(t),this.totalTime(-.01,t.suppressEvents)),"nested"!==this.data&&!1!==t.kill&&this.kill(),vt=e,this},e.globalTime=function(t){for(var e=this,i=arguments.length?t:e.rawTime();e;)i=e._start+i/(Math.abs(e._ts)||1),e=e._dp;return!this.parent&&this._sat?this._sat.globalTime(t):i},e.repeat=function(t){return arguments.length?(this._repeat=t===1/0?-2:t,ni(this)):-2===this._repeat?1/0:this._repeat},e.repeatDelay=function(t){if(arguments.length){var e=this._time;return this._rDelay=t,ni(this),e?this.time(e):this}return this._rDelay},e.yoyo=function(t){return arguments.length?(this._yoyo=t,this):this._yoyo},e.seek=function(t,e){return this.totalTime(ai(this,t),zt(e))},e.restart=function(t,e){return this.play().totalTime(t?-this._delay:0,zt(e))},e.play=function(t,e){return null!=t&&this.seek(t,e),this.reversed(!1).paused(!1)},e.reverse=function(t,e){return null!=t&&this.seek(t||this.totalDuration(),e),this.reversed(!0).paused(!1)},e.pause=function(t,e){return null!=t&&this.seek(t,e),this.paused(!0)},e.resume=function(){return this.paused(!1)},e.reversed=function(t){return arguments.length?(!!t!==this.reversed()&&this.timeScale(-this._rts||(t?-1e-8:0)),this):this._rts<0},e.invalidate=function(){return this._initted=this._act=0,this._zTime=-1e-8,this},e.isActive=function(){var t,e=this.parent||this._dp,i=this._start;return!(e&&!(this._ts&&this._initted&&e.isActive()&&(t=e.rawTime(!0))>=i&&t<this.endTime(!0)-Mt))},e.eventCallback=function(t,e,i){var r=this.vars;return arguments.length>1?(e?(r[t]=e,i&&(r[t+"Params"]=i),"onUpdate"===t&&(this._onUpdate=e)):delete r[t],this):r[t]},e.then=function(t){var e=this;return new Promise((function(i){var r=qt(t)?t:Oe,n=function(){var t=e.then;e.then=null,qt(r)&&(r=r(e))&&(r.then||r===e)&&(e.then=t),i(r),e.then=t};e._initted&&1===e.totalProgress()&&e._ts>=0||!e._tTime&&e._ts<0?n():e._prom=n}))},e.kill=function(){ji(this)},t}();Me(Zi.prototype,{_time:0,_start:0,_end:0,_tTime:0,_tDur:0,_dirty:0,_repeat:0,_yoyo:!1,parent:null,_initted:!1,_rDelay:0,_ts:1,_dp:0,ratio:0,_zTime:-1e-8,_prom:0,_ps:!1,_rts:1});var $i=function(t){function e(e,i){var r;return void 0===e&&(e={}),(r=t.call(this,e)||this).labels={},r.smoothChildTiming=!!e.smoothChildTiming,r.autoRemoveChildren=!!e.autoRemoveChildren,r._sort=zt(e.sortChildren),gt&&Ze(e.parent||gt,ft(r),i),e.reversed&&r.reverse(),e.paused&&r.paused(!0),e.scrollTrigger&&$e(ft(r),e.scrollTrigger),r}pt(e,t);var i=e.prototype;return i.to=function(t,e,i){return oi(0,arguments,this),this},i.from=function(t,e,i){return oi(1,arguments,this),this},i.fromTo=function(t,e,i,r){return oi(2,arguments,this),this},i.set=function(t,e,i){return e.duration=0,e.parent=this,Be(e).repeatDelay||(e.repeat=0),e.immediateRender=!!e.immediateRender,new lr(t,e,ai(this,i),1),this},i.call=function(t,e,i){return Ze(this,lr.delayedCall(0,t,e),i)},i.staggerTo=function(t,e,i,r,n,s,a){return i.duration=e,i.stagger=i.stagger||r,i.onComplete=s,i.onCompleteParams=a,i.parent=this,new lr(t,i,ai(this,n)),this},i.staggerFrom=function(t,e,i,r,n,s,a){return i.runBackwards=1,Be(i).immediateRender=zt(i.immediateRender),this.staggerTo(t,e,i,r,n,s,a)},i.staggerFromTo=function(t,e,i,r,n,s,a,o){return r.startAt=i,Be(r).immediateRender=zt(r.immediateRender),this.staggerTo(t,e,r,n,s,a,o)},i.render=function(t,e,i){var r,n,s,a,o,c,u,l,h,d,f,p,_=this._time,v=this._dirty?this.totalDuration():this._tDur,m=this._dur,g=t<=0?0:Le(t),y=this._zTime<0!=t<0&&(this._initted||!m);if(this!==gt&&g>v&&t>=0&&(g=v),g!==this._tTime||i||y){if(_!==this._time&&m&&(g+=this._time-_,t+=this._time-_),r=g,h=this._start,c=!(l=this._ts),y&&(m||(_=this._zTime),(t||!e)&&(this._zTime=t)),this._repeat){if(f=this._yoyo,o=m+this._rDelay,this._repeat<-1&&t<0)return this.totalTime(100*o+t,e,i);if(r=Le(g%o),g===v?(a=this._repeat,r=m):((a=~~(g/o))&&a===g/o&&(r=m,a--),r>m&&(r=m)),d=ze(this._tTime,o),!_&&this._tTime&&d!==a&&this._tTime-d*o-this._dur<=0&&(d=a),f&&1&a&&(r=m-r,p=1),a!==d&&!this._lock){var b=f&&1&d,w=b===(f&&1&a);if(a<d&&(b=!b),_=b?0:g%m?m:g,this._lock=1,this.render(_||(p?0:Le(a*o)),e,!m)._lock=0,this._tTime=g,!e&&this.parent&&xi(this,"onRepeat"),this.vars.repeatRefresh&&!p&&(this.invalidate()._lock=1),_&&_!==this._time||c!==!this._ts||this.vars.onRepeat&&!this.parent&&!this._act)return this;if(m=this._dur,v=this._tDur,w&&(this._lock=2,_=b?m:-1e-4,this.render(_,!0),this.vars.repeatRefresh&&!p&&this.invalidate()),this._lock=0,!this._ts&&!c)return this;Yi(this,p)}}if(this._hasPause&&!this._forcing&&this._lock<2&&(u=function(t,e,i){var r;if(i>e)for(r=t._first;r&&r._start<=i;){if("isPause"===r.data&&r._start>e)return r;r=r._next}else for(r=t._last;r&&r._start>=i;){if("isPause"===r.data&&r._start<e)return r;r=r._prev}}(this,Le(_),Le(r)),u&&(g-=r-(r=u._start))),this._tTime=g,this._time=r,this._act=!l,this._initted||(this._onUpdate=this.vars.onUpdate,this._initted=1,this._zTime=t,_=0),!_&&r&&!e&&!a&&(xi(this,"onStart"),this._tTime!==g))return this;if(r>=_&&t>=0)for(n=this._first;n;){if(s=n._next,(n._act||r>=n._start)&&n._ts&&u!==n){if(n.parent!==this)return this.render(t,e,i);if(n.render(n._ts>0?(r-n._start)*n._ts:(n._dirty?n.totalDuration():n._tDur)+(r-n._start)*n._ts,e,i),r!==this._time||!this._ts&&!c){u=0,s&&(g+=this._zTime=-1e-8);break}}n=s}else{n=this._last;for(var T=t<0?t:r;n;){if(s=n._prev,(n._act||T<=n._end)&&n._ts&&u!==n){if(n.parent!==this)return this.render(t,e,i);if(n.render(n._ts>0?(T-n._start)*n._ts:(n._dirty?n.totalDuration():n._tDur)+(T-n._start)*n._ts,e,i||vt&&(n._initted||n._startAt)),r!==this._time||!this._ts&&!c){u=0,s&&(g+=this._zTime=T?-1e-8:Mt);break}}n=s}}if(u&&!e&&(this.pause(),u.render(r>=_?0:-1e-8)._zTime=r>=_?1:-1,this._ts))return this._start=h,Je(this),this.render(t,e,i);this._onUpdate&&!e&&xi(this,"onUpdate",!0),(g===v&&this._tTime>=this.totalDuration()||!g&&_)&&(h!==this._start&&Math.abs(l)===Math.abs(this._ts)||this._lock||((t||!m)&&(g===v&&this._ts>0||!g&&this._ts<0)&&Xe(this,1),e||t<0&&!_||!g&&!_&&v||(xi(this,g===v&&t>=0?"onComplete":"onReverseComplete",!0),this._prom&&!(g<v&&this.timeScale()>0)&&this._prom())))}return this},i.add=function(t,e){var i=this;if(Gt(e)||(e=ai(this,e,t)),!(t instanceof Zi)){if(Kt(t))return t.forEach((function(t){return i.add(t,e)})),this;if(Xt(t))return this.addLabel(t,e);if(!qt(t))return this;t=lr.delayedCall(0,t)}return this!==t?Ze(this,t,e):this},i.getChildren=function(t,e,i,r){void 0===t&&(t=!0),void 0===e&&(e=!0),void 0===i&&(i=!0),void 0===r&&(r=-Ot);for(var n=[],s=this._first;s;)s._start>=r&&(s instanceof lr?e&&n.push(s):(i&&n.push(s),t&&n.push.apply(n,s.getChildren(!0,e,i)))),s=s._next;return n},i.getById=function(t){for(var e=this.getChildren(1,1,1),i=e.length;i--;)if(e[i].vars.id===t)return e[i]},i.remove=function(t){return Xt(t)?this.removeLabel(t):qt(t)?this.killTweensOf(t):(He(this,t),t===this._recent&&(this._recent=this._last),qe(this))},i.totalTime=function(e,i){return arguments.length?(this._forcing=1,!this._dp&&this._ts&&(this._start=Le(Ni.time-(this._ts>0?e/this._ts:(this.totalDuration()-e)/-this._ts))),t.prototype.totalTime.call(this,e,i),this._forcing=0,this):this._tTime},i.addLabel=function(t,e){return this.labels[t]=ai(this,e),this},i.removeLabel=function(t){return delete this.labels[t],this},i.addPause=function(t,e,i){var r=lr.delayedCall(0,e||he,i);return r.data="isPause",this._hasPause=1,Ze(this,r,ai(this,t))},i.removePause=function(t){var e=this._first;for(t=ai(this,t);e;)e._start===t&&"isPause"===e.data&&Xe(e),e=e._next},i.killTweensOf=function(t,e,i){for(var r=this.getTweensOf(t,i),n=r.length;n--;)tr!==r[n]&&r[n].kill(t,e);return this},i.getTweensOf=function(t,e){for(var i,r=[],n=pi(t),s=this._first,a=Gt(e);s;)s instanceof lr?Pe(s._targets,n)&&(a?(!tr||s._initted&&s._ts)&&s.globalTime(0)<=e&&s.globalTime(s.totalDuration())>e:!e||s.isActive())&&r.push(s):(i=s.getTweensOf(n,e)).length&&r.push.apply(r,i),s=s._next;return r},i.tweenTo=function(t,e){e=e||{};var i,r=this,n=ai(r,t),s=e,a=s.startAt,o=s.onStart,c=s.onStartParams,u=s.immediateRender,l=lr.to(r,Me({ease:e.ease||"none",lazy:!1,immediateRender:!1,time:n,overwrite:"auto",duration:e.duration||Math.abs((n-(a&&"time"in a?a.time:r._time))/r.timeScale())||Mt,onStart:function(){if(r.pause(),!i){var t=e.duration||Math.abs((n-(a&&"time"in a?a.time:r._time))/r.timeScale());l._dur!==t&&ri(l,t,0,1).render(l._time,!0,!0),i=1}o&&o.apply(l,c||[])}},e));return u?l.render(0):l},i.tweenFromTo=function(t,e,i){return this.tweenTo(e,Me({startAt:{time:ai(this,t)}},i))},i.recent=function(){return this._recent},i.nextLabel=function(t){return void 0===t&&(t=this._time),Si(this,ai(this,t))},i.previousLabel=function(t){return void 0===t&&(t=this._time),Si(this,ai(this,t),1)},i.currentLabel=function(t){return arguments.length?this.seek(t,!0):this.previousLabel(this._time+Mt)},i.shiftChildren=function(t,e,i){void 0===i&&(i=0);for(var r,n=this._first,s=this.labels;n;)n._start>=i&&(n._start+=t,n._end+=t),n=n._next;if(e)for(r in s)s[r]>=i&&(s[r]+=t);return qe(this)},i.invalidate=function(e){var i=this._first;for(this._lock=0;i;)i.invalidate(e),i=i._next;return t.prototype.invalidate.call(this,e)},i.clear=function(t){void 0===t&&(t=!0);for(var e,i=this._first;i;)e=i._next,this.remove(i),i=e;return this._dp&&(this._time=this._tTime=this._pTime=0),t&&(this.labels={}),qe(this)},i.totalDuration=function(t){var e,i,r,n=0,s=this,a=s._last,o=Ot;if(arguments.length)return s.timeScale((s._repeat<0?s.duration():s.totalDuration())/(s.reversed()?-t:t));if(s._dirty){for(r=s.parent;a;)e=a._prev,a._dirty&&a.totalDuration(),(i=a._start)>o&&s._sort&&a._ts&&!s._lock?(s._lock=1,Ze(s,a,i-a._delay,1)._lock=0):o=i,i<0&&a._ts&&(n-=i,(!r&&!s._dp||r&&r.smoothChildTiming)&&(s._start+=i/s._ts,s._time-=i,s._tTime-=i),s.shiftChildren(-i,!1,-Infinity),o=0),a._end>n&&a._ts&&(n=a._end),a=e;ri(s,s===gt&&s._time>n?s._time:n,1,1),s._dirty=0}return s._tDur},e.updateRoot=function(t){if(gt._ts&&(ke(gt,We(t,gt)),Et=Ni.frame),Ni.frame>=be){be+=kt.autoSleep||120;var e=gt._first;if((!e||!e._ts)&&kt.autoSleep&&Ni._listeners.length<2){for(;e&&!e._ts;)e=e._next;e||Ni.sleep()}}},e}(Zi);Me($i.prototype,{_lock:0,_hasPause:0,_forcing:0});var tr,er,ir=function(t,e,i,r,n,s,a){var o,c,u,l,h,d,f,p,_=new Sr(this._pt,t,e,0,1,gr,null,n),v=0,m=0;for(_.b=i,_.e=r,i+="",(f=~(r+="").indexOf("random("))&&(r=Ti(r)),s&&(s(p=[i,r],t,e),i=p[0],r=p[1]),c=i.match(ee)||[];o=ee.exec(r);)l=o[0],h=r.substring(v,o.index),u?u=(u+1)%5:"rgba("===h.substr(-5)&&(u=1),l!==c[m++]&&(d=parseFloat(c[m-1])||0,_._pt={_next:_._pt,p:h||1===m?h:",",s:d,c:"="===l.charAt(1)?Re(d,l)-d:parseFloat(l)-d,m:u&&u<4?Math.round:0},v=ee.lastIndex);return _.c=v<r.length?r.substring(v,r.length):"",_.fp=a,(ie.test(r)||f)&&(_.e=0),this._pt=_,_},rr=function(t,e,i,r,n,s,a,o,c,u){qt(r)&&(r=r(n||0,t,s));var l,h=t[e],d="get"!==i?i:qt(h)?c?t[e.indexOf("set")||!qt(t["get"+e.substr(3)])?e:"get"+e.substr(3)](c):t[e]():h,f=qt(h)?c?fr:dr:hr;if(Xt(r)&&(~r.indexOf("random(")&&(r=Ti(r)),"="===r.charAt(1)&&((l=Re(d,r)+(li(d)||0))||0===l)&&(r=l)),!u||d!==r||er)return isNaN(d*r)||""===r?(!h&&!(e in t)&&ce(e,r),ir.call(this,t,e,d,r,f,o||kt.stringFilter,c)):(l=new Sr(this._pt,t,e,+d||0,r-(d||0),"boolean"==typeof h?mr:vr,0,f),c&&(l.fp=c),a&&l.modifier(a,this,t),this._pt=l)},nr=function(t,e,i,r,n,s){var a,o,c,u;if(ge[t]&&!1!==(a=new ge[t]).init(n,a.rawVars?e[t]:function(t,e,i,r,n){if(qt(t)&&(t=or(t,n,e,i,r)),!Vt(t)||t.style&&t.nodeType||Kt(t)||Qt(t))return Xt(t)?or(t,n,e,i,r):t;var s,a={};for(s in t)a[s]=or(t[s],n,e,i,r);return a}(e[t],r,n,s,i),i,r,s)&&(i._pt=o=new Sr(i._pt,n,t,0,1,a.render,a,0,a.priority),i!==St))for(c=i._ptLookup[i._targets.indexOf(n)],u=a._props.length;u--;)c[a._props[u]]=o;return a},sr=function t(e,i,r){var n,s,a,o,c,u,l,h,d,f,p,_,v,m=e.vars,g=m.ease,y=m.startAt,b=m.immediateRender,w=m.lazy,T=m.onUpdate,E=m.runBackwards,S=m.yoyoEase,x=m.keyframes,j=m.autoRevert,A=e._dur,L=e._startAt,R=e._targets,P=e.parent,C=P&&"nested"===P.data?P.vars.targets:R,k="auto"===e._overwrite&&!_t,I=e.timeline;if(I&&(!x||!g)&&(g="none"),e._ease=Vi(g,It.ease),e._yEase=S?Gi(Vi(!0===S?g:S,It.ease)):0,S&&e._yoyo&&!e._repeat&&(S=e._yEase,e._yEase=e._ease,e._ease=S),e._from=!I&&!!m.runBackwards,!I||x&&!m.stagger){if(_=(h=R[0]?Se(R[0]).harness:0)&&m[h.prop],n=Ne(m,_e),L&&(L._zTime<0&&L.progress(1),i<0&&E&&b&&!j?L.render(-1,!0):L.revert(E&&A?fe:de),L._lazy=0),y){if(Xe(e._startAt=lr.set(R,Me({data:"isStart",overwrite:!1,parent:P,immediateRender:!0,lazy:!L&&zt(w),startAt:null,delay:0,onUpdate:T&&function(){return xi(e,"onUpdate")},stagger:0},y))),e._startAt._dp=0,e._startAt._sat=e,i<0&&(vt||!b&&!j)&&e._startAt.revert(fe),b&&A&&i<=0&&r<=0)return void(i&&(e._zTime=i))}else if(E&&A&&!L)if(i&&(b=!1),a=Me({overwrite:!1,data:"isFromStart",lazy:b&&!L&&zt(w),immediateRender:b,stagger:0,parent:P},n),_&&(a[h.prop]=_),Xe(e._startAt=lr.set(R,a)),e._startAt._dp=0,e._startAt._sat=e,i<0&&(vt?e._startAt.revert(fe):e._startAt.render(-1,!0)),e._zTime=i,b){if(!i)return}else t(e._startAt,Mt,Mt);for(e._pt=e._ptCache=0,w=A&&zt(w)||w&&!A,s=0;s<R.length;s++){if(l=(c=R[s])._gsap||Ee(R)[s]._gsap,e._ptLookup[s]=f={},me[l.id]&&ve.length&&Ce(),p=C===R?s:C.indexOf(c),h&&!1!==(d=new h).init(c,_||n,e,p,C)&&(e._pt=o=new Sr(e._pt,c,d.name,0,1,d.render,d,0,d.priority),d._props.forEach((function(t){f[t]=o})),d.priority&&(u=1)),!h||_)for(a in n)ge[a]&&(d=nr(a,n,e,p,c,C))?d.priority&&(u=1):f[a]=o=rr.call(e,c,a,"get",n[a],p,C,0,m.stringFilter);e._op&&e._op[s]&&e.kill(c,e._op[s]),k&&e._pt&&(tr=e,gt.killTweensOf(c,f,e.globalTime(i)),v=!e.parent,tr=0),e._pt&&w&&(me[l.id]=1)}u&&Er(e),e._onInit&&e._onInit(e)}e._onUpdate=T,e._initted=(!e._op||e._pt)&&!v,x&&i<=0&&I.render(Ot,!0,!0)},ar=function(t,e,i,r){var n,s,a=e.ease||r||"power1.inOut";if(Kt(e))s=i[t]||(i[t]=[]),e.forEach((function(t,i){return s.push({t:i/(e.length-1)*100,v:t,e:a})}));else for(n in e)s=i[n]||(i[n]=[]),"ease"===n||s.push({t:parseFloat(t),v:e[n],e:a})},or=function(t,e,i,r,n){return qt(t)?t.call(e,i,r,n):Xt(t)&&~t.indexOf("random(")?Ti(t):t},cr=Te+"repeat,repeatDelay,yoyo,repeatRefresh,yoyoEase,autoRevert",ur={};je(cr+",id,stagger,delay,duration,paused,scrollTrigger",(function(t){return ur[t]=1}));var lr=function(t){function e(e,i,r,n){var s;"number"==typeof i&&(r.duration=i,i=r,r=null);var a,o,c,u,l,h,d,f,p=(s=t.call(this,n?i:Be(i))||this).vars,_=p.duration,v=p.delay,m=p.immediateRender,g=p.stagger,y=p.overwrite,b=p.keyframes,w=p.defaults,T=p.scrollTrigger,E=p.yoyoEase,S=i.parent||gt,x=(Kt(e)||Qt(e)?Gt(e[0]):"length"in i)?[e]:pi(e);if(s._targets=x.length?Ee(x):ue("GSAP target "+e+" not found. https://gsap.com",!kt.nullTargetWarn)||[],s._ptLookup=[],s._overwrite=y,b||g||Jt(_)||Jt(v)){if(i=s.vars,(a=s.timeline=new $i({data:"nested",defaults:w||{},targets:S&&"nested"===S.data?S.vars.targets:x})).kill(),a.parent=a._dp=ft(s),a._start=0,g||Jt(_)||Jt(v)){if(u=x.length,d=g&&mi(g),Vt(g))for(l in g)~cr.indexOf(l)&&(f||(f={}),f[l]=g[l]);for(o=0;o<u;o++)(c=Ne(i,ur)).stagger=0,E&&(c.yoyoEase=E),f&&De(c,f),h=x[o],c.duration=+or(_,ft(s),o,h,x),c.delay=(+or(v,ft(s),o,h,x)||0)-s._delay,!g&&1===u&&c.delay&&(s._delay=v=c.delay,s._start+=v,c.delay=0),a.to(h,c,d?d(o,h,x):0),a._ease=Ui.none;a.duration()?_=v=0:s.timeline=0}else if(b){Be(Me(a.vars.defaults,{ease:"none"})),a._ease=Vi(b.ease||i.ease||"none");var j,A,L,R=0;if(Kt(b))b.forEach((function(t){return a.to(x,t,">")})),a.duration();else{for(l in c={},b)"ease"===l||"easeEach"===l||ar(l,b[l],c,b.easeEach);for(l in c)for(j=c[l].sort((function(t,e){return t.t-e.t})),R=0,o=0;o<j.length;o++)(L={ease:(A=j[o]).e,duration:(A.t-(o?j[o-1].t:0))/100*_})[l]=A.v,a.to(x,L,R),R+=L.duration;a.duration()<_&&a.to({},{duration:_-a.duration()})}}_||s.duration(_=a.duration())}else s.timeline=0;return!0!==y||_t||(tr=ft(s),gt.killTweensOf(x),tr=0),Ze(S,ft(s),r),i.reversed&&s.reverse(),i.paused&&s.paused(!0),(m||!_&&!b&&s._start===Le(S._time)&&zt(m)&&Ye(ft(s))&&"nested"!==S.data)&&(s._tTime=-1e-8,s.render(Math.max(0,-v)||0)),T&&$e(ft(s),T),s}pt(e,t);var i=e.prototype;return i.render=function(t,e,i){var r,n,s,a,o,c,u,l,h,d=this._time,f=this._tDur,p=this._dur,_=t<0,v=t>f-Mt&&!_?f:t<Mt?0:t;if(p){if(v!==this._tTime||!t||i||!this._initted&&this._tTime||this._startAt&&this._zTime<0!==_){if(r=v,l=this.timeline,this._repeat){if(a=p+this._rDelay,this._repeat<-1&&_)return this.totalTime(100*a+t,e,i);if(r=Le(v%a),v===f?(s=this._repeat,r=p):((s=~~(v/a))&&s===Le(v/a)&&(r=p,s--),r>p&&(r=p)),(c=this._yoyo&&1&s)&&(h=this._yEase,r=p-r),o=ze(this._tTime,a),r===d&&!i&&this._initted&&s===o)return this._tTime=v,this;s!==o&&(l&&this._yEase&&Yi(l,c),this.vars.repeatRefresh&&!c&&!this._lock&&this._time!==a&&this._initted&&(this._lock=i=1,this.render(Le(a*s),!0).invalidate()._lock=0))}if(!this._initted){if(ti(this,_?t:r,i,e,v))return this._tTime=0,this;if(!(d===this._time||i&&this.vars.repeatRefresh&&s!==o))return this;if(p!==this._dur)return this.render(t,e,i)}if(this._tTime=v,this._time=r,!this._act&&this._ts&&(this._act=1,this._lazy=0),this.ratio=u=(h||this._ease)(r/p),this._from&&(this.ratio=u=1-u),r&&!d&&!e&&!s&&(xi(this,"onStart"),this._tTime!==v))return this;for(n=this._pt;n;)n.r(u,n.d),n=n._next;l&&l.render(t<0?t:l._dur*l._ease(r/this._dur),e,i)||this._startAt&&(this._zTime=t),this._onUpdate&&!e&&(_&&Ge(this,t,0,i),xi(this,"onUpdate")),this._repeat&&s!==o&&this.vars.onRepeat&&!e&&this.parent&&xi(this,"onRepeat"),v!==this._tDur&&v||this._tTime!==v||(_&&!this._onUpdate&&Ge(this,t,0,!0),(t||!p)&&(v===this._tDur&&this._ts>0||!v&&this._ts<0)&&Xe(this,1),e||_&&!d||!(v||d||c)||(xi(this,v===f?"onComplete":"onReverseComplete",!0),this._prom&&!(v<f&&this.timeScale()>0)&&this._prom()))}}else!function(t,e,i,r){var n,s,a,o=t.ratio,c=e<0||!e&&(!t._start&&ei(t)&&(t._initted||!ii(t))||(t._ts<0||t._dp._ts<0)&&!ii(t))?0:1,u=t._rDelay,l=0;if(u&&t._repeat&&(l=ui(0,t._tDur,e),s=ze(l,u),t._yoyo&&1&s&&(c=1-c),s!==ze(t._tTime,u)&&(o=1-c,t.vars.repeatRefresh&&t._initted&&t.invalidate())),c!==o||vt||r||t._zTime===Mt||!e&&t._zTime){if(!t._initted&&ti(t,e,r,i,l))return;for(a=t._zTime,t._zTime=e||(i?Mt:0),i||(i=e&&!a),t.ratio=c,t._from&&(c=1-c),t._time=0,t._tTime=l,n=t._pt;n;)n.r(c,n.d),n=n._next;e<0&&Ge(t,e,0,!0),t._onUpdate&&!i&&xi(t,"onUpdate"),l&&t._repeat&&!i&&t.parent&&xi(t,"onRepeat"),(e>=t._tDur||e<0)&&t.ratio===c&&(c&&Xe(t,1),i||vt||(xi(t,c?"onComplete":"onReverseComplete",!0),t._prom&&t._prom()))}else t._zTime||(t._zTime=e)}(this,t,e,i);return this},i.targets=function(){return this._targets},i.invalidate=function(e){return(!e||!this.vars.runBackwards)&&(this._startAt=0),this._pt=this._op=this._onUpdate=this._lazy=this.ratio=0,this._ptLookup=[],this.timeline&&this.timeline.invalidate(e),t.prototype.invalidate.call(this,e)},i.resetTo=function(t,e,i,r,n){xt||Ni.wake(),this._ts||this.play();var s=Math.min(this._dur,(this._dp._time-this._start)*this._ts);return this._initted||sr(this,s),function(t,e,i,r,n,s,a,o){var c,u,l,h,d=(t._pt&&t._ptCache||(t._ptCache={}))[e];if(!d)for(d=t._ptCache[e]=[],l=t._ptLookup,h=t._targets.length;h--;){if((c=l[h][e])&&c.d&&c.d._pt)for(c=c.d._pt;c&&c.p!==e&&c.fp!==e;)c=c._next;if(!c)return er=1,t.vars[e]="+=0",sr(t,a),er=0,o?ue(e+" not eligible for reset"):1;d.push(c)}for(h=d.length;h--;)(c=(u=d[h])._pt||u).s=!r&&0!==r||n?c.s+(r||0)+s*c.c:r,c.c=i-c.s,u.e&&(u.e=Ae(i)+li(u.e)),u.b&&(u.b=c.s+li(u.b))}(this,t,e,i,r,this._ease(s/this._dur),s,n)?this.resetTo(t,e,i,r,1):(Qe(this,0),this.parent||Ue(this._dp,this,"_first","_last",this._dp._sort?"_start":0),this.render(0))},i.kill=function(t,e){if(void 0===e&&(e="all"),!(t||e&&"all"!==e))return this._lazy=this._pt=0,this.parent?ji(this):this;if(this.timeline){var i=this.timeline.totalDuration();return this.timeline.killTweensOf(t,e,tr&&!0!==tr.vars.overwrite)._first||ji(this),this.parent&&i!==this.timeline.totalDuration()&&ri(this,this._dur*this.timeline._tDur/i,0,1),this}var r,n,s,a,o,c,u,l=this._targets,h=t?pi(t):l,d=this._ptLookup,f=this._pt;if((!e||"all"===e)&&function(t,e){for(var i=t.length,r=i===e.length;r&&i--&&t[i]===e[i];);return i<0}(l,h))return"all"===e&&(this._pt=0),ji(this);for(r=this._op=this._op||[],"all"!==e&&(Xt(e)&&(o={},je(e,(function(t){return o[t]=1})),e=o),e=function(t,e){var i,r,n,s,a=t[0]?Se(t[0]).harness:0,o=a&&a.aliases;if(!o)return e;for(r in i=De({},e),o)if(r in i)for(n=(s=o[r].split(",")).length;n--;)i[s[n]]=i[r];return i}(l,e)),u=l.length;u--;)if(~h.indexOf(l[u]))for(o in n=d[u],"all"===e?(r[u]=e,a=n,s={}):(s=r[u]=r[u]||{},a=e),a)(c=n&&n[o])&&("kill"in c.d&&!0!==c.d.kill(o)||He(this,c,"_pt"),delete n[o]),"all"!==s&&(s[o]=1);return this._initted&&!this._pt&&f&&ji(this),this},e.to=function(t,i){return new e(t,i,arguments[2])},e.from=function(t,e){return oi(1,arguments)},e.delayedCall=function(t,i,r,n){return new e(i,0,{immediateRender:!1,lazy:!1,overwrite:!1,delay:t,onComplete:i,onReverseComplete:i,onCompleteParams:r,onReverseCompleteParams:r,callbackScope:n})},e.fromTo=function(t,e,i){return oi(2,arguments)},e.set=function(t,i){return i.duration=0,i.repeatDelay||(i.repeat=0),new e(t,i)},e.killTweensOf=function(t,e,i){return gt.killTweensOf(t,e,i)},e}(Zi);Me(lr.prototype,{_targets:[],_lazy:0,_startAt:0,_op:0,_onInit:0}),je("staggerTo,staggerFrom,staggerFromTo",(function(t){lr[t]=function(){var e=new $i,i=hi.call(arguments,0);return i.splice("staggerFromTo"===t?5:4,0,0),e[t].apply(e,i)}}));var hr=function(t,e,i){return t[e]=i},dr=function(t,e,i){return t[e](i)},fr=function(t,e,i,r){return t[e](r.fp,i)},pr=function(t,e,i){return t.setAttribute(e,i)},_r=function(t,e){return qt(t[e])?dr:Yt(t[e])&&t.setAttribute?pr:hr},vr=function(t,e){return e.set(e.t,e.p,Math.round(1e6*(e.s+e.c*t))/1e6,e)},mr=function(t,e){return e.set(e.t,e.p,!!(e.s+e.c*t),e)},gr=function(t,e){var i=e._pt,r="";if(!t&&e.b)r=e.b;else if(1===t&&e.e)r=e.e;else{for(;i;)r=i.p+(i.m?i.m(i.s+i.c*t):Math.round(1e4*(i.s+i.c*t))/1e4)+r,i=i._next;r+=e.c}e.set(e.t,e.p,r,e)},yr=function(t,e){for(var i=e._pt;i;)i.r(t,i.d),i=i._next},br=function(t,e,i,r){for(var n,s=this._pt;s;)n=s._next,s.p===r&&s.modifier(t,e,i),s=n},wr=function(t){for(var e,i,r=this._pt;r;)i=r._next,r.p===t&&!r.op||r.op===t?He(this,r,"_pt"):r.dep||(e=1),r=i;return!e},Tr=function(t,e,i,r){r.mSet(t,e,r.m.call(r.tween,i,r.mt),r)},Er=function(t){for(var e,i,r,n,s=t._pt;s;){for(e=s._next,i=r;i&&i.pr>s.pr;)i=i._next;(s._prev=i?i._prev:n)?s._prev._next=s:r=s,(s._next=i)?i._prev=s:n=s,s=e}t._pt=r},Sr=function(){function t(t,e,i,r,n,s,a,o,c){this.t=e,this.s=r,this.c=n,this.p=i,this.r=s||vr,this.d=a||this,this.set=o||hr,this.pr=c||0,this._next=t,t&&(t._prev=this)}return t.prototype.modifier=function(t,e,i){this.mSet=this.mSet||this.set,this.set=Tr,this.m=t,this.mt=i,this.tween=e},t}();je(Te+"parent,duration,ease,delay,overwrite,runBackwards,startAt,yoyo,immediateRender,repeat,repeatDelay,data,paused,reversed,lazy,callbackScope,stringFilter,id,yoyoEase,stagger,inherit,repeatRefresh,keyframes,autoRevert,scrollTrigger",(function(t){return _e[t]=1})),se.TweenMax=se.TweenLite=lr,se.TimelineLite=se.TimelineMax=$i,gt=new $i({sortChildren:!1,defaults:It,autoRemoveChildren:!0,id:"root",smoothChildTiming:!0}),kt.stringFilter=Fi;var xr=[],jr={},Ar=[],Lr=0,Rr=0,Pr=function(t){return(jr[t]||Ar).map((function(t){return t()}))},Cr=function(){var t=Date.now(),e=[];t-Lr>2&&(Pr("matchMediaInit"),xr.forEach((function(t){var i,r,n,s,a=t.queries,o=t.conditions;for(r in a)(i=yt.matchMedia(a[r]).matches)&&(n=1),i!==o[r]&&(o[r]=i,s=1);s&&(t.revert(),n&&e.push(t))})),Pr("matchMediaRevert"),e.forEach((function(t){return t.onMatch(t,(function(e){return t.add(null,e)}))})),Lr=t,Pr("matchMedia"))},kr=function(){function t(t,e){this.selector=e&&_i(e),this.data=[],this._r=[],this.isReverted=!1,this.id=Rr++,t&&this.add(t)}var e=t.prototype;return e.add=function(t,e,i){qt(t)&&(i=e,e=t,t=qt);var r=this,n=function(){var t,n=mt,s=r.selector;return n&&n!==r&&n.data.push(r),i&&(r.selector=_i(i)),mt=r,t=e.apply(r,arguments),qt(t)&&r._r.push(t),mt=n,r.selector=s,r.isReverted=!1,t};return r.last=n,t===qt?n(r,(function(t){return r.add(null,t)})):t?r[t]=n:n},e.ignore=function(t){var e=mt;mt=null,t(this),mt=e},e.getTweens=function(){var e=[];return this.data.forEach((function(i){return i instanceof t?e.push.apply(e,i.getTweens()):i instanceof lr&&!(i.parent&&"nested"===i.parent.data)&&e.push(i)})),e},e.clear=function(){this._r.length=this.data.length=0},e.kill=function(t,e){var i=this;if(t?function(){for(var e,r=i.getTweens(),n=i.data.length;n--;)"isFlip"===(e=i.data[n]).data&&(e.revert(),e.getChildren(!0,!0,!1).forEach((function(t){return r.splice(r.indexOf(t),1)})));for(r.map((function(t){return{g:t._dur||t._delay||t._sat&&!t._sat.vars.immediateRender?t.globalTime(0):-1/0,t}})).sort((function(t,e){return e.g-t.g||-1/0})).forEach((function(e){return e.t.revert(t)})),n=i.data.length;n--;)(e=i.data[n])instanceof $i?"nested"!==e.data&&(e.scrollTrigger&&e.scrollTrigger.revert(),e.kill()):!(e instanceof lr)&&e.revert&&e.revert(t);i._r.forEach((function(e){return e(t,i)})),i.isReverted=!0}():this.data.forEach((function(t){return t.kill&&t.kill()})),this.clear(),e)for(var r=xr.length;r--;)xr[r].id===this.id&&xr.splice(r,1)},e.revert=function(t){this.kill(t||{})},t}(),Ir=function(){function t(t){this.contexts=[],this.scope=t,mt&&mt.data.push(this)}var e=t.prototype;return e.add=function(t,e,i){Vt(t)||(t={matches:t});var r,n,s,a=new kr(0,i||this.scope),o=a.conditions={};for(n in mt&&!a.selector&&(a.selector=mt.selector),this.contexts.push(a),e=a.add("onMatch",e),a.queries=t,t)"all"===n?s=1:(r=yt.matchMedia(t[n]))&&(xr.indexOf(a)<0&&xr.push(a),(o[n]=r.matches)&&(s=1),r.addListener?r.addListener(Cr):r.addEventListener("change",Cr));return s&&e(a,(function(t){return a.add(null,t)})),this},e.revert=function(t){this.kill(t||{})},e.kill=function(t){this.contexts.forEach((function(e){return e.kill(t,!0)}))},t}(),Or={registerPlugin:function(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];e.forEach((function(t){return Li(t)}))},timeline:function(t){return new $i(t)},getTweensOf:function(t,e){return gt.getTweensOf(t,e)},getProperty:function(t,e,i,r){Xt(t)&&(t=pi(t)[0]);var n=Se(t||{}).get,s=i?Oe:Ie;return"native"===i&&(i=""),t?e?s((ge[e]&&ge[e].get||n)(t,e,i,r)):function(e,i,r){return s((ge[e]&&ge[e].get||n)(t,e,i,r))}:t},quickSetter:function(t,e,i){if((t=pi(t)).length>1){var r=t.map((function(t){return Fr.quickSetter(t,e,i)})),n=r.length;return function(t){for(var e=n;e--;)r[e](t)}}t=t[0]||{};var s=ge[e],a=Se(t),o=a.harness&&(a.harness.aliases||{})[e]||e,c=s?function(e){var r=new s;St._pt=0,r.init(t,i?e+i:e,St,0,[t]),r.render(1,r),St._pt&&yr(1,St)}:a.set(t,o);return s?c:function(e){return c(t,o,i?e+i:e,a,1)}},quickTo:function(t,e,i){var r,n=Fr.to(t,De(((r={})[e]="+=0.1",r.paused=!0,r),i||{})),s=function(t,i,r){return n.resetTo(e,t,i,r)};return s.tween=n,s},isTweening:function(t){return gt.getTweensOf(t,!0).length>0},defaults:function(t){return t&&t.ease&&(t.ease=Vi(t.ease,It.ease)),Fe(It,t||{})},config:function(t){return Fe(kt,t||{})},registerEffect:function(t){var e=t.name,i=t.effect,r=t.plugins,n=t.defaults,s=t.extendTimeline;(r||"").split(",").forEach((function(t){return t&&!ge[t]&&!se[t]&&ue(e+" effect requires "+t+" plugin.")})),ye[e]=function(t,e,r){return i(pi(t),Me(e||{},n),r)},s&&($i.prototype[e]=function(t,i,r){return this.add(ye[e](t,Vt(i)?i:(r=i)&&{},this),r)})},registerEase:function(t,e){Ui[t]=Vi(e)},parseEase:function(t,e){return arguments.length?Vi(t,e):Ui},getById:function(t){return gt.getById(t)},exportRoot:function(t,e){void 0===t&&(t={});var i,r,n=new $i(t);for(n.smoothChildTiming=zt(t.smoothChildTiming),gt.remove(n),n._dp=0,n._time=n._tTime=gt._time,i=gt._first;i;)r=i._next,!e&&!i._dur&&i instanceof lr&&i.vars.onComplete===i._targets[0]||Ze(n,i,i._start-i._delay),i=r;return Ze(gt,n,0),n},context:function(t,e){return t?new kr(t,e):mt},matchMedia:function(t){return new Ir(t)},matchMediaRefresh:function(){return xr.forEach((function(t){var e,i,r=t.conditions;for(i in r)r[i]&&(r[i]=!1,e=1);e&&t.revert()}))||Cr()},addEventListener:function(t,e){var i=jr[t]||(jr[t]=[]);~i.indexOf(e)||i.push(e)},removeEventListener:function(t,e){var i=jr[t],r=i&&i.indexOf(e);r>=0&&i.splice(r,1)},utils:{wrap:function t(e,i,r){var n=i-e;return Kt(e)?wi(e,t(0,e.length),i):ci(r,(function(t){return(n+(t-e)%n)%n+e}))},wrapYoyo:function t(e,i,r){var n=i-e,s=2*n;return Kt(e)?wi(e,t(0,e.length-1),i):ci(r,(function(t){return e+((t=(s+(t-e)%s)%s||0)>n?s-t:t)}))},distribute:mi,random:bi,snap:yi,normalize:function(t,e,i){return Ei(t,e,0,1,i)},getUnit:li,clamp:function(t,e,i){return ci(i,(function(i){return ui(t,e,i)}))},splitColor:ki,toArray:pi,selector:_i,mapRange:Ei,pipe:function(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];return function(t){return e.reduce((function(t,e){return e(t)}),t)}},unitize:function(t,e){return function(i){return t(parseFloat(i))+(e||li(i))}},interpolate:function t(e,i,r,n){var s=isNaN(e+i)?0:function(t){return(1-t)*e+t*i};if(!s){var a,o,c,u,l,h=Xt(e),d={};if(!0===r&&(n=1)&&(r=null),h)e={p:e},i={p:i};else if(Kt(e)&&!Kt(i)){for(c=[],u=e.length,l=u-2,o=1;o<u;o++)c.push(t(e[o-1],e[o]));u--,s=function(t){t*=u;var e=Math.min(l,~~t);return c[e](t-e)},r=i}else n||(e=De(Kt(e)?[]:{},e));if(!c){for(a in i)rr.call(d,e,a,"get",i[a]);s=function(t){return yr(t,d)||(h?e.p:e)}}}return ci(r,s)},shuffle:vi},install:oe,effects:ye,ticker:Ni,updateRoot:$i.updateRoot,plugins:ge,globalTimeline:gt,core:{PropTween:Sr,globals:le,Tween:lr,Timeline:$i,Animation:Zi,getCache:Se,_removeLinkedListItem:He,reverting:function(){return vt},context:function(t){return t&&mt&&(mt.data.push(t),t._ctx=mt),mt},suppressOverwrites:function(t){return _t=t}}};je("to,from,fromTo,delayedCall,set,killTweensOf",(function(t){return Or[t]=lr[t]})),Ni.add($i.updateRoot),St=Or.to({},{duration:0});var Mr=function(t,e){for(var i=t._pt;i&&i.p!==e&&i.op!==e&&i.fp!==e;)i=i._next;return i},Dr=function(t,e){return{name:t,rawVars:1,init:function(t,i,r){r._onInit=function(t){var r,n;if(Xt(i)&&(r={},je(i,(function(t){return r[t]=1})),i=r),e){for(n in r={},i)r[n]=e(i[n]);i=r}!function(t,e){var i,r,n,s=t._targets;for(i in e)for(r=s.length;r--;)(n=t._ptLookup[r][i])&&(n=n.d)&&(n._pt&&(n=Mr(n,i)),n&&n.modifier&&n.modifier(e[i],t,s[r],i))}(t,i)}}}},Fr=Or.registerPlugin({name:"attr",init:function(t,e,i,r,n){var s,a,o;for(s in this.tween=i,e)o=t.getAttribute(s)||"",(a=this.add(t,"setAttribute",(o||0)+"",e[s],r,n,0,0,s)).op=s,a.b=o,this._props.push(s)},render:function(t,e){for(var i=e._pt;i;)vt?i.set(i.t,i.p,i.b,i):i.r(t,i.d),i=i._next}},{name:"endArray",init:function(t,e){for(var i=e.length;i--;)this.add(t,i,t[i]||0,e[i],0,0,0,0,0,1)}},Dr("roundProps",gi),Dr("modifiers"),Dr("snap",yi))||Or;lr.version=$i.version=Fr.version="3.12.5",Tt=1,Wt()&&Bi();Ui.Power0,Ui.Power1,Ui.Power2,Ui.Power3,Ui.Power4,Ui.Linear,Ui.Quad,Ui.Cubic,Ui.Quart,Ui.Quint,Ui.Strong,Ui.Elastic,Ui.Back,Ui.SteppedEase,Ui.Bounce,Ui.Sine,Ui.Expo,Ui.Circ;var Nr,Br,Ur,Hr,Xr,qr,Gr,Yr,Vr={},zr=180/Math.PI,Wr=Math.PI/180,Jr=Math.atan2,Qr=/([A-Z])/g,Kr=/(left|right|width|margin|padding|x)/i,Zr=/[\s,\(]\S/,$r={autoAlpha:"opacity,visibility",scale:"scaleX,scaleY",alpha:"opacity"},tn=function(t,e){return e.set(e.t,e.p,Math.round(1e4*(e.s+e.c*t))/1e4+e.u,e)},en=function(t,e){return e.set(e.t,e.p,1===t?e.e:Math.round(1e4*(e.s+e.c*t))/1e4+e.u,e)},rn=function(t,e){return e.set(e.t,e.p,t?Math.round(1e4*(e.s+e.c*t))/1e4+e.u:e.b,e)},nn=function(t,e){var i=e.s+e.c*t;e.set(e.t,e.p,~~(i+(i<0?-.5:.5))+e.u,e)},sn=function(t,e){return e.set(e.t,e.p,t?e.e:e.b,e)},an=function(t,e){return e.set(e.t,e.p,1!==t?e.b:e.e,e)},on=function(t,e,i){return t.style[e]=i},cn=function(t,e,i){return t.style.setProperty(e,i)},un=function(t,e,i){return t._gsap[e]=i},ln=function(t,e,i){return t._gsap.scaleX=t._gsap.scaleY=i},hn=function(t,e,i,r,n){var s=t._gsap;s.scaleX=s.scaleY=i,s.renderTransform(n,s)},dn=function(t,e,i,r,n){var s=t._gsap;s[e]=i,s.renderTransform(n,s)},fn="transform",pn=fn+"Origin",_n=function t(e,i){var r=this,n=this.target,s=n.style,a=n._gsap;if(e in Vr&&s){if(this.tfm=this.tfm||{},"transform"===e)return $r.transform.split(",").forEach((function(e){return t.call(r,e,i)}));if(~(e=$r[e]||e).indexOf(",")?e.split(",").forEach((function(t){return r.tfm[t]=In(n,t)})):this.tfm[e]=a.x?a[e]:In(n,e),e===pn&&(this.tfm.zOrigin=a.zOrigin),this.props.indexOf(fn)>=0)return;a.svg&&(this.svgo=n.getAttribute("data-svg-origin"),this.props.push(pn,i,"")),e=fn}(s||i)&&this.props.push(e,i,s[e])},vn=function(t){t.translate&&(t.removeProperty("translate"),t.removeProperty("scale"),t.removeProperty("rotate"))},mn=function(){var t,e,i=this.props,r=this.target,n=r.style,s=r._gsap;for(t=0;t<i.length;t+=3)i[t+1]?r[i[t]]=i[t+2]:i[t+2]?n[i[t]]=i[t+2]:n.removeProperty("--"===i[t].substr(0,2)?i[t]:i[t].replace(Qr,"-$1").toLowerCase());if(this.tfm){for(e in this.tfm)s[e]=this.tfm[e];s.svg&&(s.renderTransform(),r.setAttribute("data-svg-origin",this.svgo||"")),(t=Gr())&&t.isStart||n[fn]||(vn(n),s.zOrigin&&n[pn]&&(n[pn]+=" "+s.zOrigin+"px",s.zOrigin=0,s.renderTransform()),s.uncache=1)}},gn=function(t,e){var i={target:t,props:[],revert:mn,save:_n};return t._gsap||Fr.core.getCache(t),e&&e.split(",").forEach((function(t){return i.save(t)})),i},yn=function(t,e){var i=Br.createElementNS?Br.createElementNS((e||"http://www.w3.org/1999/xhtml").replace(/^https/,"http"),t):Br.createElement(t);return i&&i.style?i:Br.createElement(t)},bn=function t(e,i,r){var n=getComputedStyle(e);return n[i]||n.getPropertyValue(i.replace(Qr,"-$1").toLowerCase())||n.getPropertyValue(i)||!r&&t(e,Tn(i)||i,1)||""},wn="O,Moz,ms,Ms,Webkit".split(","),Tn=function(t,e,i){var r=(e||Xr).style,n=5;if(t in r&&!i)return t;for(t=t.charAt(0).toUpperCase()+t.substr(1);n--&&!(wn[n]+t in r););return n<0?null:(3===n?"ms":n>=0?wn[n]:"")+t},En=function(){"undefined"!=typeof window&&window.document&&(Nr=window,Br=Nr.document,Ur=Br.documentElement,Xr=yn("div")||{style:{}},yn("div"),fn=Tn(fn),pn=fn+"Origin",Xr.style.cssText="border-width:0;line-height:0;position:absolute;padding:0",Yr=!!Tn("perspective"),Gr=Fr.core.reverting,Hr=1)},Sn=function t(e){var i,r=yn("svg",this.ownerSVGElement&&this.ownerSVGElement.getAttribute("xmlns")||"http://www.w3.org/2000/svg"),n=this.parentNode,s=this.nextSibling,a=this.style.cssText;if(Ur.appendChild(r),r.appendChild(this),this.style.display="block",e)try{i=this.getBBox(),this._gsapBBox=this.getBBox,this.getBBox=t}catch(t){}else this._gsapBBox&&(i=this._gsapBBox());return n&&(s?n.insertBefore(this,s):n.appendChild(this)),Ur.removeChild(r),this.style.cssText=a,i},xn=function(t,e){for(var i=e.length;i--;)if(t.hasAttribute(e[i]))return t.getAttribute(e[i])},jn=function(t){var e;try{e=t.getBBox()}catch(i){e=Sn.call(t,!0)}return e&&(e.width||e.height)||t.getBBox===Sn||(e=Sn.call(t,!0)),!e||e.width||e.x||e.y?e:{x:+xn(t,["x","cx","x1"])||0,y:+xn(t,["y","cy","y1"])||0,width:0,height:0}},An=function(t){return!(!t.getCTM||t.parentNode&&!t.ownerSVGElement||!jn(t))},Ln=function(t,e){if(e){var i,r=t.style;e in Vr&&e!==pn&&(e=fn),r.removeProperty?("ms"!==(i=e.substr(0,2))&&"webkit"!==e.substr(0,6)||(e="-"+e),r.removeProperty("--"===i?e:e.replace(Qr,"-$1").toLowerCase())):r.removeAttribute(e)}},Rn=function(t,e,i,r,n,s){var a=new Sr(t._pt,e,i,0,1,s?an:sn);return t._pt=a,a.b=r,a.e=n,t._props.push(i),a},Pn={deg:1,rad:1,turn:1},Cn={grid:1,flex:1},kn=function t(e,i,r,n){var s,a,o,c,u=parseFloat(r)||0,l=(r+"").trim().substr((u+"").length)||"px",h=Xr.style,d=Kr.test(i),f="svg"===e.tagName.toLowerCase(),p=(f?"client":"offset")+(d?"Width":"Height"),_=100,v="px"===n,m="%"===n;if(n===l||!u||Pn[n]||Pn[l])return u;if("px"!==l&&!v&&(u=t(e,i,r,"px")),c=e.getCTM&&An(e),(m||"%"===l)&&(Vr[i]||~i.indexOf("adius")))return s=c?e.getBBox()[d?"width":"height"]:e[p],Ae(m?u/s*_:u/100*s);if(h[d?"width":"height"]=_+(v?l:n),a=~i.indexOf("adius")||"em"===n&&e.appendChild&&!f?e:e.parentNode,c&&(a=(e.ownerSVGElement||{}).parentNode),a&&a!==Br&&a.appendChild||(a=Br.body),(o=a._gsap)&&m&&o.width&&d&&o.time===Ni.time&&!o.uncache)return Ae(u/o.width*_);if(!m||"height"!==i&&"width"!==i)(m||"%"===l)&&!Cn[bn(a,"display")]&&(h.position=bn(e,"position")),a===e&&(h.position="static"),a.appendChild(Xr),s=Xr[p],a.removeChild(Xr),h.position="absolute";else{var g=e.style[i];e.style[i]=_+n,s=e[p],g?e.style[i]=g:Ln(e,i)}return d&&m&&((o=Se(a)).time=Ni.time,o.width=a[p]),Ae(v?s*u/_:s&&u?_/s*u:0)},In=function(t,e,i,r){var n;return Hr||En(),e in $r&&"transform"!==e&&~(e=$r[e]).indexOf(",")&&(e=e.split(",")[0]),Vr[e]&&"transform"!==e?(n=Gn(t,r),n="transformOrigin"!==e?n[e]:n.svg?n.origin:Yn(bn(t,pn))+" "+n.zOrigin+"px"):(!(n=t.style[e])||"auto"===n||r||~(n+"").indexOf("calc("))&&(n=Fn[e]&&Fn[e](t,e,i)||bn(t,e)||xe(t,e)||("opacity"===e?1:0)),i&&!~(n+"").trim().indexOf(" ")?kn(t,e,n,i)+i:n},On=function(t,e,i,r){if(!i||"none"===i){var n=Tn(e,t,1),s=n&&bn(t,n,1);s&&s!==i?(e=n,i=s):"borderColor"===e&&(i=bn(t,"borderTopColor"))}var a,o,c,u,l,h,d,f,p,_,v,m=new Sr(this._pt,t.style,e,0,1,gr),g=0,y=0;if(m.b=i,m.e=r,i+="","auto"===(r+="")&&(h=t.style[e],t.style[e]=r,r=bn(t,e)||r,h?t.style[e]=h:Ln(t,e)),Fi(a=[i,r]),r=a[1],c=(i=a[0]).match(te)||[],(r.match(te)||[]).length){for(;o=te.exec(r);)d=o[0],p=r.substring(g,o.index),l?l=(l+1)%5:"rgba("!==p.substr(-5)&&"hsla("!==p.substr(-5)||(l=1),d!==(h=c[y++]||"")&&(u=parseFloat(h)||0,v=h.substr((u+"").length),"="===d.charAt(1)&&(d=Re(u,d)+v),f=parseFloat(d),_=d.substr((f+"").length),g=te.lastIndex-_.length,_||(_=_||kt.units[e]||v,g===r.length&&(r+=_,m.e+=_)),v!==_&&(u=kn(t,e,h,_)||0),m._pt={_next:m._pt,p:p||1===y?p:",",s:u,c:f-u,m:l&&l<4||"zIndex"===e?Math.round:0});m.c=g<r.length?r.substring(g,r.length):""}else m.r="display"===e&&"none"===r?an:sn;return ie.test(r)&&(m.e=0),this._pt=m,m},Mn={top:"0%",bottom:"100%",left:"0%",right:"100%",center:"50%"},Dn=function(t,e){if(e.tween&&e.tween._time===e.tween._dur){var i,r,n,s=e.t,a=s.style,o=e.u,c=s._gsap;if("all"===o||!0===o)a.cssText="",r=1;else for(n=(o=o.split(",")).length;--n>-1;)i=o[n],Vr[i]&&(r=1,i="transformOrigin"===i?pn:fn),Ln(s,i);r&&(Ln(s,fn),c&&(c.svg&&s.removeAttribute("transform"),Gn(s,1),c.uncache=1,vn(a)))}},Fn={clearProps:function(t,e,i,r,n){if("isFromStart"!==n.data){var s=t._pt=new Sr(t._pt,e,i,0,0,Dn);return s.u=r,s.pr=-10,s.tween=n,t._props.push(i),1}}},Nn=[1,0,0,1,0,0],Bn={},Un=function(t){return"matrix(1, 0, 0, 1, 0, 0)"===t||"none"===t||!t},Hn=function(t){var e=bn(t,fn);return Un(e)?Nn:e.substr(7).match($t).map(Ae)},Xn=function(t,e){var i,r,n,s,a=t._gsap||Se(t),o=t.style,c=Hn(t);return a.svg&&t.getAttribute("transform")?"1,0,0,1,0,0"===(c=[(n=t.transform.baseVal.consolidate().matrix).a,n.b,n.c,n.d,n.e,n.f]).join(",")?Nn:c:(c!==Nn||t.offsetParent||t===Ur||a.svg||(n=o.display,o.display="block",(i=t.parentNode)&&t.offsetParent||(s=1,r=t.nextElementSibling,Ur.appendChild(t)),c=Hn(t),n?o.display=n:Ln(t,"display"),s&&(r?i.insertBefore(t,r):i?i.appendChild(t):Ur.removeChild(t))),e&&c.length>6?[c[0],c[1],c[4],c[5],c[12],c[13]]:c)},qn=function(t,e,i,r,n,s){var a,o,c,u=t._gsap,l=n||Xn(t,!0),h=u.xOrigin||0,d=u.yOrigin||0,f=u.xOffset||0,p=u.yOffset||0,_=l[0],v=l[1],m=l[2],g=l[3],y=l[4],b=l[5],w=e.split(" "),T=parseFloat(w[0])||0,E=parseFloat(w[1])||0;i?l!==Nn&&(o=_*g-v*m)&&(c=T*(-v/o)+E*(_/o)-(_*b-v*y)/o,T=T*(g/o)+E*(-m/o)+(m*b-g*y)/o,E=c):(T=(a=jn(t)).x+(~w[0].indexOf("%")?T/100*a.width:T),E=a.y+(~(w[1]||w[0]).indexOf("%")?E/100*a.height:E)),r||!1!==r&&u.smooth?(y=T-h,b=E-d,u.xOffset=f+(y*_+b*m)-y,u.yOffset=p+(y*v+b*g)-b):u.xOffset=u.yOffset=0,u.xOrigin=T,u.yOrigin=E,u.smooth=!!r,u.origin=e,u.originIsAbsolute=!!i,t.style[pn]="0px 0px",s&&(Rn(s,u,"xOrigin",h,T),Rn(s,u,"yOrigin",d,E),Rn(s,u,"xOffset",f,u.xOffset),Rn(s,u,"yOffset",p,u.yOffset)),t.setAttribute("data-svg-origin",T+" "+E)},Gn=function(t,e){var i=t._gsap||new Ki(t);if("x"in i&&!e&&!i.uncache)return i;var r,n,s,a,o,c,u,l,h,d,f,p,_,v,m,g,y,b,w,T,E,S,x,j,A,L,R,P,C,k,I,O,M=t.style,D=i.scaleX<0,F="px",N="deg",B=getComputedStyle(t),U=bn(t,pn)||"0";return r=n=s=c=u=l=h=d=f=0,a=o=1,i.svg=!(!t.getCTM||!An(t)),B.translate&&("none"===B.translate&&"none"===B.scale&&"none"===B.rotate||(M[fn]=("none"!==B.translate?"translate3d("+(B.translate+" 0 0").split(" ").slice(0,3).join(", ")+") ":"")+("none"!==B.rotate?"rotate("+B.rotate+") ":"")+("none"!==B.scale?"scale("+B.scale.split(" ").join(",")+") ":"")+("none"!==B[fn]?B[fn]:"")),M.scale=M.rotate=M.translate="none"),v=Xn(t,i.svg),i.svg&&(i.uncache?(A=t.getBBox(),U=i.xOrigin-A.x+"px "+(i.yOrigin-A.y)+"px",j=""):j=!e&&t.getAttribute("data-svg-origin"),qn(t,j||U,!!j||i.originIsAbsolute,!1!==i.smooth,v)),p=i.xOrigin||0,_=i.yOrigin||0,v!==Nn&&(b=v[0],w=v[1],T=v[2],E=v[3],r=S=v[4],n=x=v[5],6===v.length?(a=Math.sqrt(b*b+w*w),o=Math.sqrt(E*E+T*T),c=b||w?Jr(w,b)*zr:0,(h=T||E?Jr(T,E)*zr+c:0)&&(o*=Math.abs(Math.cos(h*Wr))),i.svg&&(r-=p-(p*b+_*T),n-=_-(p*w+_*E))):(O=v[6],k=v[7],R=v[8],P=v[9],C=v[10],I=v[11],r=v[12],n=v[13],s=v[14],u=(m=Jr(O,C))*zr,m&&(j=S*(g=Math.cos(-m))+R*(y=Math.sin(-m)),A=x*g+P*y,L=O*g+C*y,R=S*-y+R*g,P=x*-y+P*g,C=O*-y+C*g,I=k*-y+I*g,S=j,x=A,O=L),l=(m=Jr(-T,C))*zr,m&&(g=Math.cos(-m),I=E*(y=Math.sin(-m))+I*g,b=j=b*g-R*y,w=A=w*g-P*y,T=L=T*g-C*y),c=(m=Jr(w,b))*zr,m&&(j=b*(g=Math.cos(m))+w*(y=Math.sin(m)),A=S*g+x*y,w=w*g-b*y,x=x*g-S*y,b=j,S=A),u&&Math.abs(u)+Math.abs(c)>359.9&&(u=c=0,l=180-l),a=Ae(Math.sqrt(b*b+w*w+T*T)),o=Ae(Math.sqrt(x*x+O*O)),m=Jr(S,x),h=Math.abs(m)>2e-4?m*zr:0,f=I?1/(I<0?-I:I):0),i.svg&&(j=t.getAttribute("transform"),i.forceCSS=t.setAttribute("transform","")||!Un(bn(t,fn)),j&&t.setAttribute("transform",j))),Math.abs(h)>90&&Math.abs(h)<270&&(D?(a*=-1,h+=c<=0?180:-180,c+=c<=0?180:-180):(o*=-1,h+=h<=0?180:-180)),e=e||i.uncache,i.x=r-((i.xPercent=r&&(!e&&i.xPercent||(Math.round(t.offsetWidth/2)===Math.round(-r)?-50:0)))?t.offsetWidth*i.xPercent/100:0)+F,i.y=n-((i.yPercent=n&&(!e&&i.yPercent||(Math.round(t.offsetHeight/2)===Math.round(-n)?-50:0)))?t.offsetHeight*i.yPercent/100:0)+F,i.z=s+F,i.scaleX=Ae(a),i.scaleY=Ae(o),i.rotation=Ae(c)+N,i.rotationX=Ae(u)+N,i.rotationY=Ae(l)+N,i.skewX=h+N,i.skewY=d+N,i.transformPerspective=f+F,(i.zOrigin=parseFloat(U.split(" ")[2])||!e&&i.zOrigin||0)&&(M[pn]=Yn(U)),i.xOffset=i.yOffset=0,i.force3D=kt.force3D,i.renderTransform=i.svg?Zn:Yr?Kn:zn,i.uncache=0,i},Yn=function(t){return(t=t.split(" "))[0]+" "+t[1]},Vn=function(t,e,i){var r=li(e);return Ae(parseFloat(e)+parseFloat(kn(t,"x",i+"px",r)))+r},zn=function(t,e){e.z="0px",e.rotationY=e.rotationX="0deg",e.force3D=0,Kn(t,e)},Wn="0deg",Jn="0px",Qn=") ",Kn=function(t,e){var i=e||this,r=i.xPercent,n=i.yPercent,s=i.x,a=i.y,o=i.z,c=i.rotation,u=i.rotationY,l=i.rotationX,h=i.skewX,d=i.skewY,f=i.scaleX,p=i.scaleY,_=i.transformPerspective,v=i.force3D,m=i.target,g=i.zOrigin,y="",b="auto"===v&&t&&1!==t||!0===v;if(g&&(l!==Wn||u!==Wn)){var w,T=parseFloat(u)*Wr,E=Math.sin(T),S=Math.cos(T);T=parseFloat(l)*Wr,w=Math.cos(T),s=Vn(m,s,E*w*-g),a=Vn(m,a,-Math.sin(T)*-g),o=Vn(m,o,S*w*-g+g)}_!==Jn&&(y+="perspective("+_+Qn),(r||n)&&(y+="translate("+r+"%, "+n+"%) "),(b||s!==Jn||a!==Jn||o!==Jn)&&(y+=o!==Jn||b?"translate3d("+s+", "+a+", "+o+") ":"translate("+s+", "+a+Qn),c!==Wn&&(y+="rotate("+c+Qn),u!==Wn&&(y+="rotateY("+u+Qn),l!==Wn&&(y+="rotateX("+l+Qn),h===Wn&&d===Wn||(y+="skew("+h+", "+d+Qn),1===f&&1===p||(y+="scale("+f+", "+p+Qn),m.style[fn]=y||"translate(0, 0)"},Zn=function(t,e){var i,r,n,s,a,o=e||this,c=o.xPercent,u=o.yPercent,l=o.x,h=o.y,d=o.rotation,f=o.skewX,p=o.skewY,_=o.scaleX,v=o.scaleY,m=o.target,g=o.xOrigin,y=o.yOrigin,b=o.xOffset,w=o.yOffset,T=o.forceCSS,E=parseFloat(l),S=parseFloat(h);d=parseFloat(d),f=parseFloat(f),(p=parseFloat(p))&&(f+=p=parseFloat(p),d+=p),d||f?(d*=Wr,f*=Wr,i=Math.cos(d)*_,r=Math.sin(d)*_,n=Math.sin(d-f)*-v,s=Math.cos(d-f)*v,f&&(p*=Wr,a=Math.tan(f-p),n*=a=Math.sqrt(1+a*a),s*=a,p&&(a=Math.tan(p),i*=a=Math.sqrt(1+a*a),r*=a)),i=Ae(i),r=Ae(r),n=Ae(n),s=Ae(s)):(i=_,s=v,r=n=0),(E&&!~(l+"").indexOf("px")||S&&!~(h+"").indexOf("px"))&&(E=kn(m,"x",l,"px"),S=kn(m,"y",h,"px")),(g||y||b||w)&&(E=Ae(E+g-(g*i+y*n)+b),S=Ae(S+y-(g*r+y*s)+w)),(c||u)&&(a=m.getBBox(),E=Ae(E+c/100*a.width),S=Ae(S+u/100*a.height)),a="matrix("+i+","+r+","+n+","+s+","+E+","+S+")",m.setAttribute("transform",a),T&&(m.style[fn]=a)},$n=function(t,e,i,r,n){var s,a,o=360,c=Xt(n),u=parseFloat(n)*(c&&~n.indexOf("rad")?zr:1)-r,l=r+u+"deg";return c&&("short"===(s=n.split("_")[1])&&(u%=o)!==u%180&&(u+=u<0?o:-360),"cw"===s&&u<0?u=(u+36e9)%o-~~(u/o)*o:"ccw"===s&&u>0&&(u=(u-36e9)%o-~~(u/o)*o)),t._pt=a=new Sr(t._pt,e,i,r,u,en),a.e=l,a.u="deg",t._props.push(i),a},ts=function(t,e){for(var i in e)t[i]=e[i];return t},es=function(t,e,i){var r,n,s,a,o,c,u,l=ts({},i._gsap),h=i.style;for(n in l.svg?(s=i.getAttribute("transform"),i.setAttribute("transform",""),h[fn]=e,r=Gn(i,1),Ln(i,fn),i.setAttribute("transform",s)):(s=getComputedStyle(i)[fn],h[fn]=e,r=Gn(i,1),h[fn]=s),Vr)(s=l[n])!==(a=r[n])&&"perspective,force3D,transformOrigin,svgOrigin".indexOf(n)<0&&(o=li(s)!==(u=li(a))?kn(i,n,s,u):parseFloat(s),c=parseFloat(a),t._pt=new Sr(t._pt,r,n,o,c-o,tn),t._pt.u=u||0,t._props.push(n));ts(r,l)};je("padding,margin,Width,Radius",(function(t,e){var i="Top",r="Right",n="Bottom",s="Left",a=(e<3?[i,r,n,s]:[i+s,i+r,n+r,n+s]).map((function(i){return e<2?t+i:"border"+i+t}));Fn[e>1?"border"+t:t]=function(t,e,i,r,n){var s,o;if(arguments.length<4)return s=a.map((function(e){return In(t,e,i)})),5===(o=s.join(" ")).split(s[0]).length?s[0]:o;s=(r+"").split(" "),o={},a.forEach((function(t,e){return o[t]=s[e]=s[e]||s[(e-1)/2|0]})),t.init(e,o,n)}}));var is,rs,ns,ss={name:"css",register:En,targetTest:function(t){return t.style&&t.nodeType},init:function(t,e,i,r,n){var s,a,o,c,u,l,h,d,f,p,_,v,m,g,y,b,w,T,E,S,x=this._props,j=t.style,A=i.vars.startAt;for(h in Hr||En(),this.styles=this.styles||gn(t),b=this.styles.props,this.tween=i,e)if("autoRound"!==h&&(a=e[h],!ge[h]||!nr(h,e,i,r,t,n)))if(u=typeof a,l=Fn[h],"function"===u&&(u=typeof(a=a.call(i,r,t,n))),"string"===u&&~a.indexOf("random(")&&(a=Ti(a)),l)l(this,t,h,a,i)&&(y=1);else if("--"===h.substr(0,2))s=(getComputedStyle(t).getPropertyValue(h)+"").trim(),a+="",Mi.lastIndex=0,Mi.test(s)||(d=li(s),f=li(a)),f?d!==f&&(s=kn(t,h,s,f)+f):d&&(a+=d),this.add(j,"setProperty",s,a,r,n,0,0,h),x.push(h),b.push(h,0,j[h]);else if("undefined"!==u){if(A&&h in A?(s="function"==typeof A[h]?A[h].call(i,r,t,n):A[h],Xt(s)&&~s.indexOf("random(")&&(s=Ti(s)),li(s+"")||"auto"===s||(s+=kt.units[h]||li(In(t,h))||""),"="===(s+"").charAt(1)&&(s=In(t,h))):s=In(t,h),c=parseFloat(s),(p="string"===u&&"="===a.charAt(1)&&a.substr(0,2))&&(a=a.substr(2)),o=parseFloat(a),h in $r&&("autoAlpha"===h&&(1===c&&"hidden"===In(t,"visibility")&&o&&(c=0),b.push("visibility",0,j.visibility),Rn(this,j,"visibility",c?"inherit":"hidden",o?"inherit":"hidden",!o)),"scale"!==h&&"transform"!==h&&~(h=$r[h]).indexOf(",")&&(h=h.split(",")[0])),_=h in Vr)if(this.styles.save(h),v||((m=t._gsap).renderTransform&&!e.parseTransform||Gn(t,e.parseTransform),g=!1!==e.smoothOrigin&&m.smooth,(v=this._pt=new Sr(this._pt,j,fn,0,1,m.renderTransform,m,0,-1)).dep=1),"scale"===h)this._pt=new Sr(this._pt,m,"scaleY",m.scaleY,(p?Re(m.scaleY,p+o):o)-m.scaleY||0,tn),this._pt.u=0,x.push("scaleY",h),h+="X";else{if("transformOrigin"===h){b.push(pn,0,j[pn]),T=void 0,E=void 0,S=void 0,T=(w=a).split(" "),E=T[0],S=T[1]||"50%","top"!==E&&"bottom"!==E&&"left"!==S&&"right"!==S||(w=E,E=S,S=w),T[0]=Mn[E]||E,T[1]=Mn[S]||S,a=T.join(" "),m.svg?qn(t,a,0,g,0,this):((f=parseFloat(a.split(" ")[2])||0)!==m.zOrigin&&Rn(this,m,"zOrigin",m.zOrigin,f),Rn(this,j,h,Yn(s),Yn(a)));continue}if("svgOrigin"===h){qn(t,a,1,g,0,this);continue}if(h in Bn){$n(this,m,h,c,p?Re(c,p+a):a);continue}if("smoothOrigin"===h){Rn(this,m,"smooth",m.smooth,a);continue}if("force3D"===h){m[h]=a;continue}if("transform"===h){es(this,a,t);continue}}else h in j||(h=Tn(h)||h);if(_||(o||0===o)&&(c||0===c)&&!Zr.test(a)&&h in j)o||(o=0),(d=(s+"").substr((c+"").length))!==(f=li(a)||(h in kt.units?kt.units[h]:d))&&(c=kn(t,h,s,f)),this._pt=new Sr(this._pt,_?m:j,h,c,(p?Re(c,p+o):o)-c,_||"px"!==f&&"zIndex"!==h||!1===e.autoRound?tn:nn),this._pt.u=f||0,d!==f&&"%"!==f&&(this._pt.b=s,this._pt.r=rn);else if(h in j)On.call(this,t,h,s,p?p+a:a);else if(h in t)this.add(t,h,s||t[h],p?p+a:a,r,n);else if("parseTransform"!==h){ce(h,a);continue}_||(h in j?b.push(h,0,j[h]):b.push(h,1,s||t[h])),x.push(h)}y&&Er(this)},render:function(t,e){if(e.tween._time||!Gr())for(var i=e._pt;i;)i.r(t,i.d),i=i._next;else e.styles.revert()},get:In,aliases:$r,getSetter:function(t,e,i){var r=$r[e];return r&&r.indexOf(",")<0&&(e=r),e in Vr&&e!==pn&&(t._gsap.x||In(t,"x"))?i&&qr===i?"scale"===e?ln:un:(qr=i||{})&&("scale"===e?hn:dn):t.style&&!Yt(t.style[e])?on:~e.indexOf("-")?cn:_r(t,e)},core:{_removeProperty:Ln,_getMatrix:Xn}};Fr.utils.checkPrefix=Tn,Fr.core.getStyleSaver=gn,ns=je((is="x,y,z,scale,scaleX,scaleY,xPercent,yPercent")+","+(rs="rotation,rotationX,rotationY,skewX,skewY")+",transform,transformOrigin,svgOrigin,force3D,smoothOrigin,transformPerspective",(function(t){Vr[t]=1})),je(rs,(function(t){kt.units[t]="deg",Bn[t]=1})),$r[ns[13]]=is+","+rs,je("0:translateX,1:translateY,2:translateZ,8:rotate,8:rotationZ,8:rotateZ,9:rotateX,10:rotateY",(function(t){var e=t.split(":");$r[e[1]]=ns[e[0]]})),je("x,y,z,top,right,bottom,left,width,height,fontSize,padding,margin,perspective",(function(t){kt.units[t]="px"})),Fr.registerPlugin(ss);var as=Fr.registerPlugin(ss)||Fr;as.core.Tween;function os(t,e){for(var i=0;i<e.length;i++){var r=e[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function cs(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}var us=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),cs(this,"lineData",void 0),cs(this,"lines",void 0)}var e,i,r;return e=t,(i=[{key:"load",value:function(t){this.lineData=t,this.lines=t.lines}},{key:"getLineData",value:function(){return this.lineData}},{key:"getLines",value:function(){return this.lines}},{key:"getSceneLineCount",value:function(){return this.lineData.script.sceneLineCount}},{key:"getSceneLineBoxCount",value:function(){return this.lineData.script.sceneLineBoxCount}},{key:"getScriptNames",value:function(){return this.lineData.script.names}},{key:"getScriptAliases",value:function(){return this.lineData.script.aliases}},{key:"getBullet",value:function(){return this.lineData.script.bullet}},{key:"getNameType",value:function(){return this.lineData.script.nameType}},{key:"getNoUseDashline",value:function(){return this.lineData.script.noUseDashline}},{key:"getKorExist",value:function(){var t=this.lineData.script.hasKor;return void 0===t||t}},{key:"getScriptLines",value:function(){var t=this.lineData.script.group;if(t){for(var e,i=function(i){var s=[],a=t.findIndex((function(t){return t.includes(i)}));if(a>-1){for(var o=t[a][0],c=t[a][1],u=o;u<=c;++u)s.push(e.lines[u]);i+=c-o}else s.push(e.lines[i]);r.push(s),n=i},r=[],n=0;n<this.lines.length;++n)e=this,i(n);if(this.lineData.script.title){var s=[];s.push(this.lineData.script.title),r.unshift(s)}return console.log("script lines :: ",r),r}for(var a=[],o=0;o<this.lines.length;++o){var c=[];c.push(this.lines[o]),a.push(c)}if(this.lineData.script.title){var u=[];u.push(this.lineData.script.title),a.unshift(u)}return console.log("script lines :: ",a),a}},{key:"getSubtitleLines",value:function(){var t,e=null===(t=this.lineData.subtitle)||void 0===t?void 0:t.group;if(e){for(var i,r=function(t){var r=e.findIndex((function(e){return t>=e[0]&&t<=e[1]}));if(r>-1){for(var a=e[r],o=a[0],c=a[1],u=i.lines[o],l=i.lines[c],h="",d="",f=[],p=o;p<=c;++p){var _=i.lines[p];p===o?(h=h.concat("",_.eng),d=d.concat("",_.kor)):(h=h.concat(" ",_.eng),d=d.concat(" ",_.kor)),_.animation?_.animation.forEach((function(t){f.push(t)})):f.push({ss:_.ss?_.ss:-1,se:_.se?_.se:-1,eng:_.eng})}var v={s:u.s,e:l.e,character:u.character,characters:u.characters,eng:h,kor:d,fontSize:u.fontSize,appFontSize:u.appFontSize,animation:f};n.push(v),t+=c-o}else{var m=i.setupLineAnimation(i.lines[t]);n.push(m)}s=t},n=[],s=0;s<this.lines.length;++s)i=this,r(s);return n}for(var a=[],o=0;o<this.lines.length;++o){var c=this.setupLineAnimation(this.lines[o]);a.push(c)}return console.log("subtitle lines :: ",a),a}},{key:"setupLineAnimation",value:function(t){var e=[],i=t;return i.animation?i.animation.forEach((function(t){e.push(t)})):e.push({ss:i.ss?i.ss:-1,se:i.se?i.se:-1,eng:i.eng}),{s:i.s,e:i.e,character:i.character,characters:i.characters,eng:i.eng,kor:i.kor,fontSize:i.fontSize,appFontSize:i.appFontSize,animation:e}}}])&&os(e.prototype,i),r&&os(e,r),t}();function ls(t,e){for(var i=0;i<e.length;i++){var r=e[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function hs(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}var ds=function(){function t(e,i,r,n){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),hs(this,"root",void 0),hs(this,"parentEl",void 0),this.parentEl=i,this.root=document.createElement("div"),this.root.classList.add("line-layout");var s=document.createElement("div");if(s.classList.add("name-box"),n&&n.name){var a=n.name.split("/");if(a.length>1){var c=document.createElement("div");c.classList.add("name","multiple");var u=document.createElement("div");u.classList.add("names"),a.forEach((function(t){var e=document.createElement("div");e.innerHTML=t,u.appendChild(e)})),c.appendChild(u);var l=document.createElement("span");l.classList.add("colon"),l.innerHTML=":",c.appendChild(l),s.appendChild(c),n.type&&c.classList.add(n.type)}else{var h=document.createElement("div");h.classList.add("name"),h.innerHTML="".concat(n.name,'<span class="colon">:</span>'),s.appendChild(h),n.type&&h.classList.add(n.type);var d=n.name.split(",");d.length>1&&h.classList.add("person-".concat(d.length)),h.childNodes.forEach((function(t){if(3===t.nodeType){var e,i=null===(e=t.textContent)||void 0===e?void 0:e.trim();i&&/^[a-zA-Z0-9 ,'"()/\-!?:.]+$/.test(i)&&t.parentNode.classList.add("notranslate")}}))}}else s.classList.add("no-name");var f=[],p=[],_=document.createElement("div");_.classList.add("line-box"),r.forEach((function(t,e,i){var r=document.createElement("span");r.classList.add("eng"),e>0?t.lineBreak?(r.innerHTML="</br>".concat(t.eng),r.classList.add("line-break")):r.innerHTML="&nbsp".concat(t.eng):r.innerHTML=t.eng,t.isTitle&&r.classList.add("title"),_.appendChild(r),f.push(r)}));var v=document.createElement("div");v.classList.add("kor-box"),v.classList.add("kor-box-".concat(e)),r.forEach((function(t,i,r){var n=document.createElement("span");n.classList.add("kor","kor-".concat(e,"-").concat(i)),t.lineBreak&&n.classList.add("line-break"),n.innerHTML=t.kor,v.appendChild(n),p.push(n)})),_.appendChild(v),this.root.appendChild(s),this.root.appendChild(_),this.parentEl.appendChild(this.root),setTimeout((function(){p.forEach((function(t,e){var i,r,n=f[e],s=parseInt(o.getStyle(n,"font-size").replace("px",""));if(i=e>0&&!n.classList.contains("line-break")?n.offsetLeft+13:n.offsetLeft,r=n.offsetTop,n.offsetHeight>s){t.classList.add("multiple-lines");var a=_.getBoundingClientRect(),c=n.getBoundingClientRect();i=(c.x-a.x)/window.bound.scale,r=(c.y-a.y+c.height+10)/window.bound.scale}else r=s+s/2;t.style.left="".concat(i,"px"),t.style.top="".concat(r,"px")}))}),1e3)}var e,i,r;return e=t,(i=[{key:"addBullet",value:function(t,e){var i=this.root.querySelector(".name-box"),r=document.createElement("div");t.split(" ").forEach((function(t){r.classList.add(t)})),e&&(r.innerHTML=e),i.insertBefore(r,i.firstChild)}}])&&ls(e.prototype,i),r&&ls(e,r),t}();function fs(t,e){for(var i=0;i<e.length;i++){var r=e[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function ps(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function _s(t,e){return null!=e&&"undefined"!=typeof Symbol&&e[Symbol.hasInstance]?!!e[Symbol.hasInstance](t):t instanceof e}var vs=function(){function t(e,i,r){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),ps(this,"root",void 0),ps(this,"openButton",void 0),ps(this,"closeButton",void 0),ps(this,"focusElements",void 0),"string"==typeof e?this.root=document.querySelector(e):_s(e,HTMLDivElement)&&(this.root=e),"string"==typeof i?this.openButton=document.querySelector(i):_s(i,HTMLButtonElement)&&(this.openButton=i),"string"==typeof r?this.closeButton=document.querySelector(r):_s(r,HTMLButtonElement)&&(this.closeButton=r),this.root.role="dialog",this.root.ariaModal="true",this.openButton.ariaHasPopup="true",this.openButton.ariaExpanded="false",this.init()}var e,i,r;return e=t,(i=[{key:"init",value:function(){var t=this;this.focusElements=Array.from(this.root.querySelectorAll('video, a[href], button:not([disabled]), input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])')),console.log("focus elements :: ",this.focusElements),this.root.addEventListener("keydown",(function(e){var i=t.focusElements[0],r=t.focusElements[t.focusElements.length-1];e.shiftKey?"Tab"===e.key&&e.target===i&&(e.preventDefault(),r.focus()):"Tab"===e.key&&e.target===r&&(e.preventDefault(),i.focus())}))}},{key:"enableFocus",value:function(){this.openButton.setAttribute("aria-expanded","true"),this.focusElements.forEach((function(t){t.setAttribute("tabindex","0")})),this.focusElements[0].focus()}},{key:"disableFocus",value:function(){this.openButton.setAttribute("aria-expanded","false"),this.focusElements.forEach((function(t){t.removeAttribute("tabindex")})),this.openButton.focus()}}])&&fs(e.prototype,i),r&&fs(e,r),t}();function ms(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function gs(t,e){for(var i=0;i<e.length;i++){var r=e[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function ys(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function bs(t){return bs=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},bs(t)}function ws(t,e){return!e||"object"!=((i=e)&&"undefined"!=typeof Symbol&&i.constructor===Symbol?"symbol":typeof i)&&"function"!=typeof e?ms(t):e;var i}function Ts(t,e){return Ts=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},Ts(t,e)}function Es(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var i,r=bs(t);if(e){var n=bs(this).constructor;i=Reflect.construct(r,arguments,n)}else i=r.apply(this,arguments);return ws(this,i)}}var Ss=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&Ts(t,e)}(s,t);var e,i,r,n=Es(s);function s(t){var e;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,s),ys(ms(e=n.call(this)),"options",void 0),ys(ms(e),"root",void 0),ys(ms(e),"scrollInner",void 0),ys(ms(e),"playCheckButton",void 0),ys(ms(e),"transCheckButton",void 0),ys(ms(e),"closeButton",void 0),ys(ms(e),"openButton",void 0),ys(ms(e),"engs",void 0),ys(ms(e),"kors",void 0),ys(ms(e),"popupAccessibility",void 0),ys(ms(e),"parseLine",void 0),ys(ms(e),"tio",void 0),ys(ms(e),"soundIds",[]),ys(ms(e),"scriptIndex",-1),e.options=t,e.id=t.id,e.root=document.querySelector(t.root),t.sounds.list.forEach((function(t){f.add(t),e.soundIds.push(t.id)})),e}return e=s,(i=[{key:"init",value:function(){var t=this;this.options.static?this.initScript():this.options.line&&(this.parseLine=new us,this.parseLine.load(this.options.line),this.setupScript()),document.body.addEventListener("keydown",(function(e){if("Enter"===e.key||" "===e.key||"Spacebar"===e.key){var i=e.target;i&&t.engs.includes(i)&&(e.preventDefault(),i.click())}}))}},{key:"show",value:function(){var t=this;this.root.classList.remove("hide"),as.killTweensOf(this.root),as.set(this.root,{opacity:1,y:0}),as.from(this.root,{opacity:0,y:720,duration:.5,ease:"Cubic.eraseOut",onComplete:function(){t.popupAccessibility.enableFocus()}})}},{key:"hide",value:function(){this.root.classList.add("hide"),as.killTweensOf(this.root),as.set(this.root,{opacity:1,y:0}),this.popupAccessibility.disableFocus()}},{key:"reset",value:function(){this.scriptIndex=-1,this.hideKors(),this.resetTexts(),this.autoScrollTop("instant"),this.playCheckButton.checked=!1,this.transCheckButton.checked=!1,f.stopAll()}},{key:"setupFocus",value:function(t){this.openButton=t,this.popupAccessibility||(this.popupAccessibility=new vs(this.root,this.openButton,this.root.querySelector(".btn-close")))}},{key:"setupScript",value:function(){var t=document.createElement("div");t.classList.add("title"),this.root.classList.contains("lyric")?t.innerText="노랫말":t.innerText="대본";var e=document.createElement("div");e.classList.add("scroll");var i=document.createElement("div");i.classList.add("scroll__inner"),i.classList.add("scroll-style"),e.appendChild(i);var r=document.createElement("div");r.classList.add("trans-button-wrap");var n=document.createElement("div");n.classList.add("checkbox"),n.classList.add("btn-trans"),n.innerHTML='\n<input\n  class="btn-trans__check"\n  type="checkbox"\n  id="trans-check"\n  data-hover="true"\n/>\n<label\n  class="btn-trans__label"\n  for="trans-check"\n>\n  <span class="off">해석 보기</span>\n  <span class="on">해석 감추기</span>\n</label>\n',r.appendChild(n);var s=document.createElement("div");s.classList.add("control-wrap");var a=document.createElement("div");a.classList.add("checkbox"),a.classList.add("btn-play"),a.innerHTML='\n<input\n  class="btn-play__check"\n  type="checkbox"\n  aria-label="전체 듣기"\n  data-hover="true"\n/>\n',s.appendChild(a);var o=document.createElement("button");o.classList.add("btn-stop"),o.dataset.hover="true",s.appendChild(o);var c=document.createElement("button");c.classList.add("btn-close"),c.dataset.hover="true",this.root.appendChild(t),this.root.appendChild(e),this.root.appendChild(s),this.root.appendChild(r),this.root.appendChild(c);var u=this.parseLine.getScriptNames(),l=this.parseLine.getScriptAliases(),h=this.parseLine.getNameType(),d=this.parseLine.getSceneLineBoxCount(),f=this.parseLine.getBullet(),p=this.parseLine.getNoUseDashline(),_=this.parseLine.getScriptLines();this.parseLine.getKorExist()||(n.style.pointerEvents="none",n.style.opacity="0.3");var v=0,m=0;_.forEach((function(t,e){var r;t[0].useAlias&&l?u&&(r=l[t[0].character]):u&&(r=u[t[0].character]);var n=new ds(e,i,t,{name:r,type:h});if(d&&(++v,f&&"number"===f.type&&1===v&&n.addBullet(f.style,(m+1).toString()),m<d.length-1&&v===d[m]))if(p)++m,v=0;else{var s=document.createElement("div");s.classList.add("dash-line-box");var a=document.createElement("div");a.classList.add("dash-line"),s.appendChild(a),i.appendChild(s),++m,v=0}})),this.initScript()}},{key:"initScript",value:function(){var t=this;this.options.static&&this.root.classList.add("static-script"),this.scrollInner=this.root.querySelector(".scroll__inner"),this.scrollInner.tabIndex=0,this.engs=Array.from(this.root.querySelectorAll(".eng")),console.log("영어 문장 갯수 :: ",this.engs.length),this.kors=Array.from(this.root.querySelectorAll(".kor")),this.hideKors(),this.engs.forEach((function(e){e.style.cursor="pointer",e.addEventListener("pointerover",(function(t){"mouse"===t.pointerType&&(e.classList.contains("on")||e.classList.add("over"))})),e.addEventListener("pointerout",(function(t){"mouse"===t.pointerType&&(e.classList.contains("on")||e.classList.remove("over"))})),e.addEventListener("click",(function(){t.resetTexts(),t.textSound(e)})),e.classList.add("notranslate"),e.tabIndex=0})),this.playCheckButton=this.root.querySelector(".btn-play__check"),this.playCheckButton.addEventListener("change",(function(){t.playEffectButton(),t.playCheckButton.checked?-1===t.scriptIndex?t.playAllSound():t.resumeSound():t.pauseSound()})),this.root.querySelector(".btn-stop").addEventListener("click",(function(){t.playEffectButton(),t.stopAllSound(),t.autoScrollTop("smooth")})),this.transCheckButton=this.root.querySelector(".btn-trans__check"),this.transCheckButton.addEventListener("change",(function(){t.playEffectButton(),t.transCheckButton.checked?t.showKors():t.hideKors()})),this.closeButton=this.root.querySelector(".btn-close"),this.closeButton.addEventListener("click",(function(){t.playEffectButton(),t.reset(),t.hide()}))}},{key:"hideKors",value:function(){this.kors.forEach((function(t){return t.classList.add("hide")}))}},{key:"showKors",value:function(){this.kors.forEach((function(t){return t.classList.remove("hide")}))}},{key:"playAllSound",value:function(){this.scriptIndex=0,this.playSound()}},{key:"stopAllSound",value:function(){this.tio&&clearTimeout(this.tio),f.stopAll(),this.scriptIndex=-1,this.resetTexts(),this.playCheckButton.checked=!1}},{key:"playSound",value:function(){var t=this;this.autoScroll(this.scriptIndex),this.resetTexts();var e=this.engs[this.scriptIndex];e.classList.add("on"),f.stopAll(),f.play(this.getSoundId(this.scriptIndex),(function(){e.classList.remove("on"),++t.scriptIndex,t.scriptIndex<t.engs.length?t.tio=setTimeout((function(){t.playSound()}),10):(console.log("ended"),t.stopAllSound(),t.autoScrollTop("smooth"))}))}},{key:"pauseSound",value:function(){this.tio&&clearTimeout(this.tio),f.pause(this.getSoundId(this.scriptIndex))}},{key:"resumeSound",value:function(){f.getPaused(this.getSoundId(this.scriptIndex))?f.resume(this.getSoundId(this.scriptIndex)):this.playSound()}},{key:"textSound",value:function(t){this.stopAllSound(),t.classList.add("on");var e=this.engs.indexOf(t);console.log("클릭 문장 인덱스 :: ",e),f.stopAll(),f.play(this.getSoundId(e),(function(){t.classList.remove("on")}))}},{key:"resetTexts",value:function(){this.engs.forEach((function(t){t.classList.remove("over","on")}))}},{key:"autoScroll",value:function(t){var e=window.bound.scale,i=this.engs[t],r=this.kors[t],n=this.scrollInner.getBoundingClientRect(),s=i.getBoundingClientRect(),a=r.getBoundingClientRect(),o=s.top/e,c=(s.bottom,a.bottom/e),u=n.top/e;(c>=n.bottom/e||o<=u)&&this.scrollInner.scrollBy({top:o-(u+10),behavior:"smooth"})}},{key:"autoScrollTop",value:function(t){this.scrollInner.scrollTo({top:0,behavior:t})}},{key:"getSoundId",value:function(t){return this.soundIds[t]}}])&&gs(e.prototype,i),r&&gs(e,r),s}(v),xs={script:{names:["보라","Sam"],sceneLineCount:[2,2,2,2,2,2,2,2,2],sceneLineBoxCount:[2,2,2,2,2,2,2,2,2],bullet:{type:"number",style:"bullet-num blue"}},subtitle:{},lines:[{s:-1,e:-1,character:0,eng:"Are you happy? ",kor:"기쁘니?"},{s:-1,e:-1,character:1,eng:"Yes, I am.",kor:"응, 그래."},{s:-1,e:-1,character:0,eng:"Are you happy? ",kor:"기쁘니?"},{s:-1,e:-1,character:1,eng:"No, I'm not.",kor:"아니, 그렇지 않아."},{s:-1,e:-1,character:0,eng:"Are you angry? ",kor:"화났니?"},{s:-1,e:-1,character:1,eng:"Yes, I am. ",kor:"응, 그래."},{s:-1,e:-1,character:0,eng:"Are you sleepy?",kor:"졸리니?"},{s:-1,e:-1,character:1,eng:"Yes, I am.",kor:"응, 그래."},{s:-1,e:-1,character:0,eng:"Are you sleepy?",kor:"졸리니?"},{s:-1,e:-1,character:1,eng:"No, I’m not.",kor:"아니, 그렇지 않아."},{s:-1,e:-1,character:0,eng:"Are you thirsty?",kor:"목마르니?    "},{s:-1,e:-1,character:1,eng:"Yes, I am.",kor:"응, 그래."},{s:-1,e:-1,character:0,eng:"Are you sad?",kor:"슬프니? "},{s:-1,e:-1,character:1,eng:"Yes, I am.",kor:"응, 그래."},{s:-1,e:-1,character:0,eng:"Are you thirsty?",kor:"목마르니?"},{s:-1,e:-1,character:1,eng:"No, I’m not.",kor:"아니, 그렇지 않아."},{s:-1,e:-1,character:0,eng:"Are you tired?",kor:"피곤하니?    "},{s:-1,e:-1,character:1,eng:"Yes, I am.",kor:"응, 그래."}]};window.addEventListener("load",(function(){new q({pages:[new dt({root:".root",directions:{list:[{id:"dir_0",src:"./media/audio/content02/dir_0.mp3"}]},scriptIDs:["script"],modules:[new Ss({id:"script",root:".popup-script",line:xs,sounds:{list:[{id:"script_0",src:"./media/audio/e4_02_04_01_01_script_01.mp3"},{id:"script_1",src:"./media/audio/e4_02_04_01_01_script_02.mp3"},{id:"script_2",src:"./media/audio/e4_02_04_01_01_script_03.mp3"},{id:"script_3",src:"./media/audio/e4_02_04_01_01_script_04.mp3"},{id:"script_4",src:"./media/audio/e4_02_04_01_01_script_05.mp3"},{id:"script_5",src:"./media/audio/e4_02_04_01_01_script_06.mp3"},{id:"script_6",src:"./media/audio/e4_02_04_01_01_script_07.mp3"},{id:"script_7",src:"./media/audio/e4_02_04_01_01_script_08.mp3"},{id:"script_8",src:"./media/audio/e4_02_04_01_01_script_09.mp3"},{id:"script_9",src:"./media/audio/e4_02_04_01_01_script_10.mp3"},{id:"script_10",src:"./media/audio/e4_02_04_01_01_script_11.mp3"},{id:"script_11",src:"./media/audio/e4_02_04_01_01_script_12.mp3"},{id:"script_12",src:"./media/audio/e4_02_04_01_01_script_13.mp3"},{id:"script_13",src:"./media/audio/e4_02_04_01_01_script_14.mp3"},{id:"script_14",src:"./media/audio/e4_02_04_01_01_script_15.mp3"},{id:"script_15",src:"./media/audio/e4_02_04_01_01_script_16.mp3"},{id:"script_16",src:"./media/audio/e4_02_04_01_01_script_17.mp3"},{id:"script_17",src:"./media/audio/e4_02_04_01_01_script_18.mp3"}]}})]})]})}))})()})();