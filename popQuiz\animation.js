(function (cjs, an) {

var p; // shortcut to reference prototypes
var lib={};var ss={};var img={};
lib.ssMetadata = [
		{name:"animation_atlas_1", frames: [[0,0,317,292]]},
		{name:"animation_atlas_2", frames: [[0,0,317,292]]},
		{name:"animation_atlas_3", frames: [[0,0,317,292]]},
		{name:"animation_atlas_4", frames: [[0,0,317,292]]},
		{name:"animation_atlas_5", frames: [[0,0,310,292]]},
		{name:"animation_atlas_6", frames: [[0,0,310,292]]},
		{name:"animation_atlas_7", frames: [[306,105,142,114],[331,221,166,91],[0,237,166,103],[306,0,166,103],[143,342,166,89],[311,407,166,89],[331,314,166,91],[0,0,304,235],[0,342,141,119],[168,237,161,94]]},
		{name:"animation_atlas_8", frames: [[188,197,45,50],[235,197,45,50],[318,262,45,38],[273,423,45,38],[320,423,45,38],[273,463,45,38],[320,463,45,38],[367,423,45,38],[186,249,34,34],[349,326,34,34],[414,394,34,34],[414,430,34,34],[414,466,34,34],[450,394,34,34],[192,475,42,37],[414,253,42,37],[458,253,42,37],[414,292,42,37],[458,292,42,37],[414,331,42,37],[300,378,43,43],[345,378,43,43],[262,127,43,43],[307,127,43,43],[282,172,43,43],[450,466,50,18],[450,486,50,18],[450,430,34,34],[327,172,43,43],[367,463,45,38],[458,331,42,37],[318,217,43,43],[372,167,25,73],[399,178,25,73],[426,178,25,73],[453,178,25,73],[480,178,25,73],[192,400,25,73],[219,400,25,73],[246,421,25,73],[209,343,40,55],[54,398,40,55],[59,455,40,55],[96,398,40,55],[101,455,40,55],[227,249,40,55],[278,64,42,61],[322,61,42,61],[366,41,42,61],[410,52,42,61],[454,52,42,61],[366,104,42,61],[410,115,42,61],[454,115,42,61],[138,399,47,41],[143,442,47,41],[269,249,47,41],[269,292,47,41],[251,335,47,41],[251,378,47,41],[300,335,47,41],[111,342,47,54],[160,343,47,54],[176,68,49,53],[227,64,49,53],[137,173,49,57],[135,232,49,57],[174,291,51,50],[58,338,51,50],[0,397,52,56],[81,229,52,56],[0,341,54,54],[81,173,54,54],[58,287,56,49],[116,291,56,49],[0,280,56,59],[288,0,56,59],[0,455,57,50],[432,0,57,50],[318,302,68,22],[414,370,68,22],[176,123,72,35],[188,160,72,35],[0,68,79,104],[0,174,79,104],[81,132,84,39],[346,0,84,39],[193,0,93,62],[81,68,93,62],[0,0,191,66]]},
		{name:"animation_atlas_9", frames: [[83,0,23,23],[62,62,26,26],[83,25,23,23],[0,80,29,29],[31,80,29,29],[52,0,29,29],[0,0,50,18],[0,20,50,18],[0,40,50,18],[52,31,29,29],[0,60,50,18]]}
];


(lib.AnMovieClip = function(){
	this.actionFrames = [];
	this.ignorePause = false;
	this.gotoAndPlay = function(positionOrLabel){
		cjs.MovieClip.prototype.gotoAndPlay.call(this,positionOrLabel);
	}
	this.play = function(){
		cjs.MovieClip.prototype.play.call(this);
	}
	this.gotoAndStop = function(positionOrLabel){
		cjs.MovieClip.prototype.gotoAndStop.call(this,positionOrLabel);
	}
	this.stop = function(){
		cjs.MovieClip.prototype.stop.call(this);
	}
}).prototype = p = new cjs.MovieClip();
// symbols:



(lib.CachedBmp_5 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(0);
}).prototype = p = new cjs.Sprite();



(lib.CachedBmp_4 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(1);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap10 = function() {
	this.initialize(img.Bitmap10);
}).prototype = p = new cjs.Bitmap();
p.nominalBounds = new cjs.Rectangle(0,0,797,460);


(lib.Bitmap117 = function() {
	this.initialize(ss["animation_atlas_9"]);
	this.gotoAndStop(0);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap118 = function() {
	this.initialize(ss["animation_atlas_9"]);
	this.gotoAndStop(1);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap119 = function() {
	this.initialize(ss["animation_atlas_9"]);
	this.gotoAndStop(2);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap120 = function() {
	this.initialize(ss["animation_atlas_9"]);
	this.gotoAndStop(3);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap121 = function() {
	this.initialize(ss["animation_atlas_9"]);
	this.gotoAndStop(4);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap122 = function() {
	this.initialize(ss["animation_atlas_9"]);
	this.gotoAndStop(5);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap124 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(2);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap125 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(3);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap127 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(4);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap128 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(5);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap131 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(6);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap132 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(7);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap133 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(8);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap135 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(9);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap136 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(10);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap137 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(11);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap138 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(12);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap140 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(13);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap141 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(14);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap143 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(15);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap144 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(16);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap145 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(17);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap146 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(18);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap149 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(19);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap150 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(20);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap153 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(21);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap154 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(22);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap155 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(23);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap157 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(24);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap158 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(25);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap159 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(26);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap160 = function() {
	this.initialize(ss["animation_atlas_9"]);
	this.gotoAndStop(6);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap161 = function() {
	this.initialize(ss["animation_atlas_9"]);
	this.gotoAndStop(7);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap162 = function() {
	this.initialize(ss["animation_atlas_9"]);
	this.gotoAndStop(8);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap164 = function() {
	this.initialize(ss["animation_atlas_7"]);
	this.gotoAndStop(0);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap338n_260 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(27);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap339n_260 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(28);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap340n_260 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(29);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap341n_260 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(30);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap342n_260 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(31);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap343n_260 = function() {
	this.initialize(ss["animation_atlas_9"]);
	this.gotoAndStop(9);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap4ccf = function() {
	this.initialize(ss["animation_atlas_9"]);
	this.gotoAndStop(10);
}).prototype = p = new cjs.Sprite();



(lib.Bitmap92_sdrf_n_1020 = function() {
	this.initialize(ss["animation_atlas_7"]);
	this.gotoAndStop(1);
}).prototype = p = new cjs.Sprite();



(lib.bitmap166x103_1_n_15 = function() {
	this.initialize(ss["animation_atlas_7"]);
	this.gotoAndStop(2);
}).prototype = p = new cjs.Sprite();



(lib.bitmap166x103_5_n_1028 = function() {
	this.initialize(ss["animation_atlas_7"]);
	this.gotoAndStop(3);
}).prototype = p = new cjs.Sprite();



(lib.bitmap166x89_2_n_16 = function() {
	this.initialize(ss["animation_atlas_7"]);
	this.gotoAndStop(4);
}).prototype = p = new cjs.Sprite();



(lib.bitmap166x89_4_n_1029 = function() {
	this.initialize(ss["animation_atlas_7"]);
	this.gotoAndStop(5);
}).prototype = p = new cjs.Sprite();



(lib.bitmap166x91_0_n_17 = function() {
	this.initialize(ss["animation_atlas_7"]);
	this.gotoAndStop(6);
}).prototype = p = new cjs.Sprite();



(lib.bitmap25x73_19_n_1039 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(32);
}).prototype = p = new cjs.Sprite();



(lib.bitmap25x73_21_n_1040 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(33);
}).prototype = p = new cjs.Sprite();



(lib.bitmap25x73_23_n_1041 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(34);
}).prototype = p = new cjs.Sprite();



(lib.bitmap25x73_25_n_1042 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(35);
}).prototype = p = new cjs.Sprite();



(lib.bitmap25x73_3_n_18 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(36);
}).prototype = p = new cjs.Sprite();



(lib.bitmap25x73_4_n_19 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(37);
}).prototype = p = new cjs.Sprite();



(lib.bitmap25x73_5_n_20 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(38);
}).prototype = p = new cjs.Sprite();



(lib.bitmap25x73_6_n_21 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(39);
}).prototype = p = new cjs.Sprite();



(lib.bitmap310x292_2_n_1050 = function() {
	this.initialize(ss["animation_atlas_5"]);
	this.gotoAndStop(0);
}).prototype = p = new cjs.Sprite();



(lib.bitmap310x292_7_n_22 = function() {
	this.initialize(ss["animation_atlas_6"]);
	this.gotoAndStop(0);
}).prototype = p = new cjs.Sprite();



(lib.bitmap317x292_0_n_1051 = function() {
	this.initialize(ss["animation_atlas_1"]);
	this.gotoAndStop(0);
}).prototype = p = new cjs.Sprite();



(lib.bitmap317x292_1_n_1052 = function() {
	this.initialize(ss["animation_atlas_2"]);
	this.gotoAndStop(0);
}).prototype = p = new cjs.Sprite();



(lib.bitmap317x292_8_n_23 = function() {
	this.initialize(ss["animation_atlas_3"]);
	this.gotoAndStop(0);
}).prototype = p = new cjs.Sprite();



(lib.bitmap317x292_9_n_24 = function() {
	this.initialize(ss["animation_atlas_4"]);
	this.gotoAndStop(0);
}).prototype = p = new cjs.Sprite();



(lib.bitmap40x55_10_n_25 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(40);
}).prototype = p = new cjs.Sprite();



(lib.bitmap40x55_11_n_26 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(41);
}).prototype = p = new cjs.Sprite();



(lib.bitmap40x55_12_n_27 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(42);
}).prototype = p = new cjs.Sprite();



(lib.bitmap40x55_27_n_1070 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(43);
}).prototype = p = new cjs.Sprite();



(lib.bitmap40x55_28_n_1071 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(44);
}).prototype = p = new cjs.Sprite();



(lib.bitmap40x55_29_n_1072 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(45);
}).prototype = p = new cjs.Sprite();



(lib.bitmap42x61_13_n_28 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(46);
}).prototype = p = new cjs.Sprite();



(lib.bitmap42x61_14_n_29 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(47);
}).prototype = p = new cjs.Sprite();



(lib.bitmap42x61_15_n_30 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(48);
}).prototype = p = new cjs.Sprite();



(lib.bitmap42x61_16_n_31 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(49);
}).prototype = p = new cjs.Sprite();



(lib.bitmap42x61_20_n_1073 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(50);
}).prototype = p = new cjs.Sprite();



(lib.bitmap42x61_22_n_1074 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(51);
}).prototype = p = new cjs.Sprite();



(lib.bitmap42x61_24_n_1075 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(52);
}).prototype = p = new cjs.Sprite();



(lib.bitmap42x61_26_n_1076 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(53);
}).prototype = p = new cjs.Sprite();



(lib.bitmap47x41_72_n_260 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(54);
}).prototype = p = new cjs.Sprite();



(lib.bitmap47x41_73_n_261 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(55);
}).prototype = p = new cjs.Sprite();



(lib.bitmap47x41_74_n_262 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(56);
}).prototype = p = new cjs.Sprite();



(lib.bitmap47x41_75_n_263 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(57);
}).prototype = p = new cjs.Sprite();



(lib.bitmap47x41_76_n_264 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(58);
}).prototype = p = new cjs.Sprite();



(lib.bitmap47x41_77_n_265 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(59);
}).prototype = p = new cjs.Sprite();



(lib.bitmap47x41_78_n_266 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(60);
}).prototype = p = new cjs.Sprite();



(lib.bitmap47x54_10_n_1085 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(61);
}).prototype = p = new cjs.Sprite();



(lib.bitmap47x54_17_n_32 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(62);
}).prototype = p = new cjs.Sprite();



(lib.bitmap49x53_13_n_1087 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(63);
}).prototype = p = new cjs.Sprite();



(lib.bitmap49x53_18_n_33 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(64);
}).prototype = p = new cjs.Sprite();



(lib.bitmap49x57_16_n_1088 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(65);
}).prototype = p = new cjs.Sprite();



(lib.bitmap49x57_19_n_34 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(66);
}).prototype = p = new cjs.Sprite();



(lib.bitmap51x50_14_n_1090 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(67);
}).prototype = p = new cjs.Sprite();



(lib.bitmap51x50_20_n_35 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(68);
}).prototype = p = new cjs.Sprite();



(lib.bitmap52x56_12_n_1091 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(69);
}).prototype = p = new cjs.Sprite();



(lib.bitmap52x56_21_n_36 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(70);
}).prototype = p = new cjs.Sprite();



(lib.bitmap54x54_15_n_1093 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(71);
}).prototype = p = new cjs.Sprite();



(lib.bitmap54x54_22_n_37 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(72);
}).prototype = p = new cjs.Sprite();



(lib.bitmap56x49_18_n_1095 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(73);
}).prototype = p = new cjs.Sprite();



(lib.bitmap56x49_23_n_38 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(74);
}).prototype = p = new cjs.Sprite();



(lib.bitmap56x59_11_n_1096 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(75);
}).prototype = p = new cjs.Sprite();



(lib.bitmap56x59_24_n_39 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(76);
}).prototype = p = new cjs.Sprite();



(lib.bitmap57x50_17_n_1097 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(77);
}).prototype = p = new cjs.Sprite();



(lib.bitmap57x50_25_n_40 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(78);
}).prototype = p = new cjs.Sprite();



(lib.bitmap68x22_26_n_41 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(79);
}).prototype = p = new cjs.Sprite();



(lib.bitmap68x22_6_n_1099 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(80);
}).prototype = p = new cjs.Sprite();



(lib.bitmap72x35_27_n_42 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(81);
}).prototype = p = new cjs.Sprite();



(lib.bitmap72x35_9_n_1100 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(82);
}).prototype = p = new cjs.Sprite();



(lib.bitmap79x104_28_n_43 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(83);
}).prototype = p = new cjs.Sprite();



(lib.bitmap79x104_30_n_1106 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(84);
}).prototype = p = new cjs.Sprite();



(lib.bitmap84x39_29_n_44 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(85);
}).prototype = p = new cjs.Sprite();



(lib.bitmap84x39_8_n_1107 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(86);
}).prototype = p = new cjs.Sprite();



(lib.bitmap93x62_30_n_45 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(87);
}).prototype = p = new cjs.Sprite();



(lib.bitmap93x62_7_n_1108 = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(88);
}).prototype = p = new cjs.Sprite();



(lib.비트맵15vfrrr = function() {
	this.initialize(ss["animation_atlas_8"]);
	this.gotoAndStop(89);
}).prototype = p = new cjs.Sprite();



(lib.비트맵16vfrrr = function() {
	this.initialize(ss["animation_atlas_7"]);
	this.gotoAndStop(7);
}).prototype = p = new cjs.Sprite();



(lib.비트맵17vfrrr = function() {
	this.initialize(ss["animation_atlas_7"]);
	this.gotoAndStop(8);
}).prototype = p = new cjs.Sprite();



(lib.비트맵18vfrrr = function() {
	this.initialize(ss["animation_atlas_7"]);
	this.gotoAndStop(9);
}).prototype = p = new cjs.Sprite();
// helper functions:

function mc_symbol_clone() {
	var clone = this._cloneProps(new this.constructor(this.mode, this.startPosition, this.loop, this.reversed));
	clone.gotoAndStop(this.currentFrame);
	clone.paused = this.paused;
	clone.framerate = this.framerate;
	return clone;
}

function getMCSymbolPrototype(symbol, nominalBounds, frameBounds) {
	var prototype = cjs.extend(symbol, cjs.MovieClip);
	prototype.clone = mc_symbol_clone;
	prototype.nominalBounds = nominalBounds;
	prototype.frameBounds = frameBounds;
	return prototype;
	}


(lib.캐릭터_모모_입정면_n_4_n_6_n_720 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_2
	this.instance = new lib.bitmap68x22_6_n_1099();
	this.instance.setTransform(-3,1);

	this.instance_1 = new lib.bitmap93x62_7_n_1108();
	this.instance_1.setTransform(-15,-10);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.instance}]}).to({state:[]},1).to({state:[{t:this.instance_1}]},3).to({state:[{t:this.instance_1}]},1).to({state:[]},1).wait(14));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-15,-10,93,62);


(lib.캐릭터_모모_입정면_n_4_n_6_n_5 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_2
	this.instance = new lib.bitmap68x22_26_n_41();
	this.instance.setTransform(-3,1);

	this.instance_1 = new lib.bitmap93x62_30_n_45();
	this.instance_1.setTransform(-15,-10);

	this.instance_2 = new lib.bitmap84x39_29_n_44();
	this.instance_2.setTransform(-11,-6);

	this.instance_3 = new lib.bitmap72x35_27_n_42();
	this.instance_3.setTransform(-5,-1);

	this.instance_4 = new lib.bitmap47x54_17_n_32();
	this.instance_4.setTransform(7,-7);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.instance}]}).to({state:[{t:this.instance_1}]},4).to({state:[{t:this.instance_2}]},4).to({state:[{t:this.instance_3}]},4).to({state:[{t:this.instance_4}]},4).wait(4));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-15,-10,93,62);


(lib.캐릭터_모모_몸통_n_6_n_9_n_717 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.bitmap317x292_0_n_1051();
	this.instance.setTransform(-53,-49);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({_off:true},1).wait(2));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-53,-49,317,292);


(lib.캐릭터_모모_몸통_n_6_n_9_n_9 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.bitmap317x292_8_n_23();
	this.instance.setTransform(-53,-49);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({_off:true},1).wait(2));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-53,-49,317,292);


(lib.캐릭터_모모_다리_n_7_n_10_n_714 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.bitmap25x73_19_n_1039();
	this.instance.setTransform(9,-20);

	this.instance_1 = new lib.bitmap42x61_20_n_1073();
	this.instance_1.setTransform(-7,40);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.instance}]}).to({state:[{t:this.instance_1}]},1).to({state:[]},1).wait(9));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-7,-20,42,121);


(lib.캐릭터_모모_다리_n_7_n_10_n_10 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.bitmap25x73_3_n_18();
	this.instance.setTransform(9,-20);

	this.instance_1 = new lib.bitmap42x61_13_n_28();
	this.instance_1.setTransform(-7,40);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.instance}]}).to({state:[{t:this.instance_1}]},1).to({state:[]},1).wait(9));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-7,-20,42,121);


(lib.캐릭터_모모_팔_n_2_n_4_n_697 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.bitmap56x59_11_n_1096();
	this.instance.setTransform(-16,-16);

	this.instance_1 = new lib.bitmap52x56_12_n_1091();
	this.instance_1.setTransform(21,24);

	this.instance_2 = new lib.bitmap49x57_16_n_1088();
	this.instance_2.setTransform(21,24);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.instance}]}).to({state:[{t:this.instance_1}]},1).to({state:[]},1).to({state:[{t:this.instance_2}]},3).to({state:[]},1).wait(4));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-16,-16,89,97);


(lib.캐릭터_모모_팔_n_2_n_4_n_4 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.bitmap56x59_24_n_39();
	this.instance.setTransform(-16,-16);

	this.instance_1 = new lib.bitmap52x56_21_n_36();
	this.instance_1.setTransform(21,24);

	this.instance_2 = new lib.bitmap51x50_20_n_35();
	this.instance_2.setTransform(21,24);

	this.instance_3 = new lib.bitmap49x57_19_n_34();
	this.instance_3.setTransform(21,24);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.instance}]}).to({state:[{t:this.instance_1}]},1).to({state:[]},1).to({state:[{t:this.instance_2}]},1).to({state:[]},1).to({state:[{t:this.instance_3}]},1).to({state:[]},1).wait(4));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-16,-16,89,97);


(lib.캐릭터_모모_눈정면_n_5_n_7_n_690 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// vvwd
	this.instance = new lib.bitmap166x103_5_n_1028();
	this.instance.setTransform(-28,-17);

	this.instance_1 = new lib.Bitmap92_sdrf_n_1020();
	this.instance_1.setTransform(-27.7,-11.9);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.instance}]}).to({state:[{t:this.instance}]},39).to({state:[]},1).to({state:[{t:this.instance_1}]},13).to({state:[]},1).wait(46));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-28,-17,166.3,103);


(lib.캐릭터_모모_눈정면_n_5_n_7_n_6 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// vvwd
	this.instance = new lib.bitmap166x103_1_n_15();
	this.instance.setTransform(-28,-17);

	this.instance_1 = new lib.bitmap166x89_2_n_16();
	this.instance_1.setTransform(-28,-14);

	this.instance_2 = new lib.bitmap166x91_0_n_17();
	this.instance_2.setTransform(-27.7,-11.9);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.instance}]}).to({state:[{t:this.instance_1}]},50).to({state:[{t:this.instance_2}]},2).to({state:[{t:this.instance_1}]},3).to({state:[{t:this.instance}]},2).wait(43));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-28,-17,166.3,103);


(lib.캐릭터_모모_대롱_n_8_n_11_n_687 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.bitmap79x104_30_n_1106();
	this.instance.setTransform(-2,-41);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({_off:true},1).wait(49));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-2,-41,79,104);


(lib.캐릭터_모모_대롱_n_8_n_11_n_11 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.bitmap79x104_28_n_43();
	this.instance.setTransform(-2,-41);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({_off:true},1).wait(49));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-2,-41,79,104);


(lib.심볼5 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// 레이어_3
	this.instance = new lib.비트맵16vfrrr();
	this.instance.setTransform(-6.3,-112.2);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-6.3,-112.2,304,235);


(lib.VRV_n_2 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_2
	this.instance = new lib.Bitmap10();
	this.instance.setTransform(-8,-1.55);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-8,-1.5,797,460);


(lib.Symbol23vcf = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// 레이어_2
	this.instance = new lib.비트맵15vfrrr();
	this.instance.setTransform(-1.95,0);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(1));

	this._renderFirstFrame();

}).prototype = getMCSymbolPrototype(lib.Symbol23vcf, new cjs.Rectangle(-1.9,0,191,66), null);


(lib.Symbol19vsddd = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.비트맵17vfrrr();

	this.instance_1 = new lib.비트맵18vfrrr();
	this.instance_1.setTransform(135.65,13.75);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.instance}]}).to({state:[{t:this.instance_1}]},1).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(0,0,296.7,119);


(lib.Symbol7_n_1 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.shape = new cjs.Shape();
	this.shape.graphics.f().s("#FFFFFF").ss(7,1,1).p("AhVhVIBVBVIBXBWABWhVIhWBVIhVBW");
	this.shape.setTransform(22.5343,22.5107,0.765,0.765);

	this.timeline.addTween(cjs.Tween.get(this.shape).wait(3));

	// Layer_2
	this.instance = new lib.CachedBmp_4();

	this.instance_1 = new lib.CachedBmp_5();

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.instance}]}).to({state:[{t:this.instance_1}]},1).wait(2));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(0,0,45,50);


(lib.P7_n_176_n_15 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.bitmap47x41_72_n_260();

	this.instance_1 = new lib.bitmap47x41_73_n_261();

	this.instance_2 = new lib.bitmap47x41_74_n_262();

	this.instance_3 = new lib.bitmap47x41_75_n_263();

	this.instance_4 = new lib.bitmap47x41_76_n_264();

	this.instance_5 = new lib.bitmap47x41_77_n_265();

	this.instance_6 = new lib.bitmap47x41_78_n_266();

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.instance}]}).to({state:[{t:this.instance_1}]},1).to({state:[{t:this.instance_2}]},1).to({state:[{t:this.instance_3}]},1).to({state:[{t:this.instance_4}]},1).to({state:[{t:this.instance_5}]},1).to({state:[{t:this.instance_6}]},1).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(0,0,47,41);


(lib.P6_n_175_n_13 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.Bitmap4ccf();

	this.instance_1 = new lib.Bitmap158();

	this.instance_2 = new lib.Bitmap159();

	this.instance_3 = new lib.Bitmap160();

	this.instance_4 = new lib.Bitmap161();

	this.instance_5 = new lib.Bitmap162();

	this.instance_6 = new lib.Bitmap164();

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.instance}]}).to({state:[{t:this.instance_1}]},1).to({state:[{t:this.instance_2}]},1).to({state:[{t:this.instance_3}]},1).to({state:[{t:this.instance_4}]},1).to({state:[{t:this.instance_5}]},1).to({state:[{t:this.instance_6}]},1).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(0,0,142,114);


(lib.P5_n_174_n_9 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.Bitmap150();

	this.instance_1 = new lib.Bitmap339n_260();

	this.instance_2 = new lib.Bitmap342n_260();

	this.instance_3 = new lib.Bitmap154();

	this.instance_4 = new lib.Bitmap155();

	this.instance_5 = new lib.Bitmap157();

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.instance}]}).to({state:[{t:this.instance_1}]},1).to({state:[{t:this.instance_2}]},1).to({state:[]},1).to({state:[{t:this.instance_3}]},1).to({state:[{t:this.instance_4}]},1).to({state:[{t:this.instance_5}]},1).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(0,0,43,43);


(lib.P4_n_173_n_7 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.Bitmap341n_260();

	this.instance_1 = new lib.Bitmap143();

	this.instance_2 = new lib.Bitmap144();

	this.instance_3 = new lib.Bitmap145();

	this.instance_4 = new lib.Bitmap146();

	this.instance_5 = new lib.Bitmap149();

	this.timeline.addTween(cjs.Tween.get({}).to({state:[]}).to({state:[{t:this.instance}]},1).to({state:[{t:this.instance_1}]},1).to({state:[{t:this.instance_2}]},1).to({state:[{t:this.instance_3}]},1).to({state:[{t:this.instance_4}]},1).to({state:[{t:this.instance_5}]},1).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(0,0,42,37);


(lib.P3_n_172_n_11 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.Bitmap133();

	this.instance_1 = new lib.Bitmap338n_260();

	this.instance_2 = new lib.Bitmap135();

	this.instance_3 = new lib.Bitmap136();

	this.instance_4 = new lib.Bitmap137();

	this.instance_5 = new lib.Bitmap138();

	this.instance_6 = new lib.Bitmap140();

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.instance}]}).to({state:[{t:this.instance_1}]},1).to({state:[{t:this.instance_2}]},1).to({state:[{t:this.instance_3}]},1).to({state:[{t:this.instance_4}]},1).to({state:[{t:this.instance_5}]},1).to({state:[{t:this.instance_6}]},1).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(0,0,34,34);


(lib.P2_n_171_n_5 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.Bitmap124();
	this.instance.setTransform(1,7);

	this.instance_1 = new lib.Bitmap125();
	this.instance_1.setTransform(1,7);

	this.instance_2 = new lib.Bitmap340n_260();
	this.instance_2.setTransform(1,7);

	this.instance_3 = new lib.Bitmap127();
	this.instance_3.setTransform(1,7);

	this.instance_4 = new lib.Bitmap128();
	this.instance_4.setTransform(1,7);

	this.instance_5 = new lib.Bitmap131();
	this.instance_5.setTransform(1,7);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.instance}]}).to({state:[{t:this.instance_1}]},1).to({state:[{t:this.instance_2}]},1).to({state:[{t:this.instance_3}]},1).to({state:[{t:this.instance_4}]},1).to({state:[{t:this.instance_5}]},1).to({state:[]},1).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(0,0,46,45);


(lib.P1_n_170_n_17 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.Bitmap117();
	this.instance.setTransform(7,7);

	this.instance_1 = new lib.Bitmap118();
	this.instance_1.setTransform(6,6);

	this.instance_2 = new lib.Bitmap119();
	this.instance_2.setTransform(8,8);

	this.instance_3 = new lib.Bitmap120();
	this.instance_3.setTransform(5,5);

	this.instance_4 = new lib.Bitmap121();
	this.instance_4.setTransform(5,5);

	this.instance_5 = new lib.Bitmap122();
	this.instance_5.setTransform(5,5);

	this.instance_6 = new lib.Bitmap343n_260();
	this.instance_6.setTransform(5,5);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.instance}]}).to({state:[{t:this.instance_1}]},1).to({state:[{t:this.instance_2}]},1).to({state:[{t:this.instance_3}]},1).to({state:[{t:this.instance_4}]},1).to({state:[{t:this.instance_5}]},1).to({state:[{t:this.instance_6}]},1).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(5,5,29,29);


(lib.캐릭터_모모_머리정면m_n_3_n_5_n_701 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// 캐릭터_모모_몸통
	this.instance = new lib.캐릭터_모모_몸통_n_6_n_9_n_717("single",0);
	this.instance.setTransform(105.6,97.4,1,1,0,0,0,105.6,97.4);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(86).to({startPosition:0},0).to({_off:true},1).wait(13));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-53,-49,317,292);


(lib.캐릭터_모모_머리정면m_n_3_n_5_n_8 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// 캐릭터_모모_몸통
	this.instance = new lib.캐릭터_모모_몸통_n_6_n_9_n_9("single",0);
	this.instance.setTransform(105.6,97.4,1,1,0,0,0,105.6,97.4);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(100));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-53,-49,317,292);


(lib.심볼2 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Symbol_19vsddd
	this.instance = new lib.Symbol19vsddd("single",1);
	this.instance.setTransform(172.9,60.45,0.5,0.5,0,0,0,211.3,55.8);
	this.instance._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(13).to({_off:false},0).to({regX:211.2,regY:55.7,scaleX:1.1,scaleY:1.1,x:172.85},8,cjs.Ease.quadInOut).to({scaleX:0.9,scaleY:0.9,x:172.9},6,cjs.Ease.quadInOut).to({scaleX:1,scaleY:1,x:172.85},6,cjs.Ease.quadInOut).wait(2));

	// Symbol_19vsddd
	this.instance_1 = new lib.Symbol19vsddd("single",0);
	this.instance_1.setTransform(91.05,96.4,0.2,0.2,-29.9981,0,0,129.3,92);

	this.timeline.addTween(cjs.Tween.get(this.instance_1).to({regY:91.5,scaleX:1,scaleY:1,rotation:7.201,y:96.3},7,cjs.Ease.quadInOut).to({regY:91.7,rotation:-4.4772,x:91,y:96.45},6,cjs.Ease.quadInOut).to({regX:129.4,regY:91.6,rotation:0,x:91.05,y:96.35},6,cjs.Ease.quadInOut).wait(16));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-45,-10.7,311.9,144.5);


(lib.VSDFCSASSDASDcopy6_n_198_n_55 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P5_n_174_n_9("single",6);
	this.instance.setTransform(23.6,22.6,1,1,0,0,0,23.6,22.6);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-9,-10,65.2,65.2);


(lib.VSDFCSASSDASDcopy5_n_197_n_48 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P5_n_174_n_9("single",5);
	this.instance.setTransform(23.6,22.6,1,1,0,0,0,23.6,22.6);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-9,-10,65.2,65.2);


(lib.VSDFCSASSDASDcopy4_n_196_n_41 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P5_n_174_n_9("single",4);
	this.instance.setTransform(23.6,22.6,1,1,0,0,0,23.6,22.6);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-9,-10,65.2,65.2);


(lib.VSDFCSASSDASDcopy2_n_194_n_27 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P5_n_174_n_9("single",2);
	this.instance.setTransform(23.6,22.6,1,1,0,0,0,23.6,22.6);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-9,-10,65.2,65.2);


(lib.VSDFCSASSDASDcopy_n_193_n_20 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P5_n_174_n_9("single",1);
	this.instance.setTransform(23.6,22.6,1,1,0,0,0,23.6,22.6);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-9,-10,65.2,65.2);


(lib.VSDFCSASSDASD_n_192_n_8 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P5_n_174_n_9("single",0);
	this.instance.setTransform(23.6,22.6,1,1,0,0,0,23.6,22.6);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-9,-10,65.2,65.2);


(lib.SDFCASDDFAScopy6_n_185_n_59 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P1_n_170_n_17("single",6);
	this.instance.setTransform(19,19,1,1,0,0,0,19,19);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(70));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(5,5,29,29);


(lib.SDFCASDDFAScopy5_n_184_n_52 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P1_n_170_n_17("single",5);
	this.instance.setTransform(19,19,1,1,0,0,0,19,19);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(70));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(5,5,29,29);


(lib.SDFCASDDFAScopy4_n_183_n_45 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P1_n_170_n_17("single",4);
	this.instance.setTransform(19,19,1,1,0,0,0,19,19);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(70));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(5,5,29,29);


(lib.SDFCASDDFAScopy3_n_182_n_38 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P1_n_170_n_17("single",3);
	this.instance.setTransform(19,19,1,1,0,0,0,19,19);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(70));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(5,5,29,29);


(lib.SDFCASDDFAScopy2_n_181_n_31 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P1_n_170_n_17("single",2);
	this.instance.setTransform(19,19,1,1,0,0,0,19,19);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(70));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(8,8,23,23);


(lib.SDFCASDDFAScopy_n_180_n_24 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P1_n_170_n_17("single",1);
	this.instance.setTransform(19,19,1,1,0,0,0,19,19);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(70));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(6,6,26,26);


(lib.SDFCASDDFAS_n_179_n_16 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P1_n_170_n_17("single",0);
	this.instance.setTransform(19,19,1,1,0,0,0,19,19);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(70));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(7,7,23,23);


(lib.NDFGBSFDSFDScopy6_n_169_n_47 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P4_n_173_n_7("single",6);
	this.instance.setTransform(24.5,25,1,1,0,0,0,19,19);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-5.4,-4.8,59.8,59.599999999999994);


(lib.NDFGBSFDSFDScopy5_n_168_n_40 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P4_n_173_n_7("single",5);
	this.instance.setTransform(24.5,25,1,1,0,0,0,19,19);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-5.4,-4.8,59.8,59.599999999999994);


(lib.NDFGBSFDSFDScopy4_n_167_n_33 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P4_n_173_n_7("single",4);
	this.instance.setTransform(24.5,25,1,1,0,0,0,19,19);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-5.4,-4.8,59.8,59.599999999999994);


(lib.NDFGBSFDSFDScopy3_n_166_n_26 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P4_n_173_n_7("single",3);
	this.instance.setTransform(24.5,25,1,1,0,0,0,19,19);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-5.4,-4.8,59.8,59.599999999999994);


(lib.NDFGBSFDSFDScopy2_n_165_n_19 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P4_n_173_n_7("single",2);
	this.instance.setTransform(24.5,25,1,1,0,0,0,19,19);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-5.4,-4.8,59.8,59.599999999999994);


(lib.NDFGBSFDSFDScopy_n_164_n_6 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P4_n_173_n_7("single",1);
	this.instance.setTransform(24.5,25,1,1,0,0,0,19,19);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-5.4,-4.8,59.8,59.599999999999994);


(lib.MFBDVSDFCSDSDFCScopy6_n_155_n_56 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P3_n_172_n_11("single",6);
	this.instance.setTransform(17.2,17.2,1,1,0,0,0,17.2,17.2);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-7.1,-7,48.7,48.6);


(lib.MFBDVSDFCSDSDFCScopy5_n_154_n_49 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P3_n_172_n_11("single",5);
	this.instance.setTransform(17.2,17.2,1,1,0,0,0,17.2,17.2);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-7.1,-7,48.7,48.6);


(lib.MFBDVSDFCSDSDFCScopy4_n_153_n_42 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P3_n_172_n_11("single",4);
	this.instance.setTransform(17.2,17.2,1,1,0,0,0,17.2,17.2);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-7.1,-7,48.7,48.6);


(lib.MFBDVSDFCSDSDFCScopy3_n_152_n_35 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P3_n_172_n_11("single",3);
	this.instance.setTransform(17.2,17.2,1,1,0,0,0,17.2,17.2);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-7.1,-7,48.7,48.6);


(lib.MFBDVSDFCSDSDFCScopy2_n_151_n_28 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P3_n_172_n_11("single",2);
	this.instance.setTransform(17.2,17.2,1,1,0,0,0,17.2,17.2);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-7.1,-7,48.7,48.6);


(lib.MFBDVSDFCSDSDFCScopy_n_150_n_21 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P3_n_172_n_11("single",1);
	this.instance.setTransform(17.2,17.2,1,1,0,0,0,17.2,17.2);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-7.1,-7,48.7,48.6);


(lib.MFBDVSDFCSDSDFCS_n_149_n_10 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P3_n_172_n_11("single",0);
	this.instance.setTransform(17.2,17.2,1,1,0,0,0,17.2,17.2);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-7.1,-7,48.7,48.6);


(lib.FDSGDSSFSSDFSAcopy6_n_124_n_57 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P6_n_175_n_13("single",6);
	this.instance.setTransform(31.5,9,1,1,0,0,0,31.5,9);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-120.9,-143.3,304.8,304.8);


(lib.FDSGDSSFSSDFSAcopy5_n_123_n_50 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P6_n_175_n_13("single",5);
	this.instance.setTransform(31.5,9,1,1,0,0,0,31.5,9);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-1.2,-23.7,65.4,65.5);


(lib.FDSGDSSFSSDFSAcopy4_n_122_n_43 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P6_n_175_n_13("single",4);
	this.instance.setTransform(31.5,9,1,1,0,0,0,31.5,9);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-1.2,-23.7,65.4,65.5);


(lib.FDSGDSSFSSDFSAcopy3_n_121_n_36 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P6_n_175_n_13("single",3);
	this.instance.setTransform(31.5,9,1,1,0,0,0,31.5,9);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-1.2,-23.7,65.4,65.5);


(lib.FDSGDSSFSSDFSAcopy2_n_120_n_29 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P6_n_175_n_13("single",2);
	this.instance.setTransform(31.5,9,1,1,0,0,0,31.5,9);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-1.2,-23.7,65.4,65.5);


(lib.FDSGDSSFSSDFSAcopy_n_119_n_22 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P6_n_175_n_13("single",1);
	this.instance.setTransform(31.5,9,1,1,0,0,0,31.5,9);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-1.2,-23.7,65.4,65.5);


(lib.FDSGDSSFSSDFSA_n_118_n_12 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P6_n_175_n_13("single",0);
	this.instance.setTransform(31.5,9,1,1,0,0,0,31.5,9);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-1.2,-23.7,65.4,65.5);


(lib.BFGVFSDDSSFCSScopy5_n_42_n_46 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P2_n_171_n_5("single",5);
	this.instance.setTransform(25.2,33.6,1,1,0,0,0,25.2,33.6);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-10.7,-2.3,71.8,71.8);


(lib.BFGVFSDDSSFCSScopy4_n_41_n_39 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P2_n_171_n_5("single",4);
	this.instance.setTransform(25.2,33.6,1,1,0,0,0,25.2,33.6);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-10.7,-2.3,71.8,71.8);


(lib.BFGVFSDDSSFCSScopy3_n_40_n_32 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P2_n_171_n_5("single",3);
	this.instance.setTransform(25.2,33.6,1,1,0,0,0,25.2,33.6);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-10.7,-2.3,71.8,71.8);


(lib.BFGVFSDDSSFCSScopy2_n_39_n_25 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P2_n_171_n_5("single",2);
	this.instance.setTransform(25.2,33.6,1,1,0,0,0,25.2,33.6);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-10.7,-2.3,71.8,71.8);


(lib.BFGVFSDDSSFCSScopy_n_38_n_18 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P2_n_171_n_5("single",1);
	this.instance.setTransform(25.2,33.6,1,1,0,0,0,25.2,33.6);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-10.7,-2.3,71.8,71.8);


(lib.BFGVFSDDSSFCSS_n_37_n_4 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P2_n_171_n_5("single",0);
	this.instance.setTransform(25.2,33.6,1,1,0,0,0,25.2,33.6);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-10.7,-2.3,71.8,71.8);


(lib.ASDFASDFAScopy6_n_29_n_58 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P7_n_176_n_15("single",6);
	this.instance.setTransform(23.4,20.7,1,1,0,0,0,23.4,20.7);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-8,-10.7,62.8,62.900000000000006);


(lib.ASDFASDFAScopy5_n_28_n_51 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P7_n_176_n_15("single",5);
	this.instance.setTransform(23.4,20.7,1,1,0,0,0,23.4,20.7);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-8,-10.7,62.8,62.900000000000006);


(lib.ASDFASDFAScopy4_n_27_n_44 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P7_n_176_n_15("single",4);
	this.instance.setTransform(23.4,20.7,1,1,0,0,0,23.4,20.7);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-8,-10.7,62.8,62.900000000000006);


(lib.ASDFASDFAScopy3_n_26_n_37 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P7_n_176_n_15("single",3);
	this.instance.setTransform(23.4,20.7,1,1,0,0,0,23.4,20.7);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-8,-10.7,62.8,62.900000000000006);


(lib.ASDFASDFAScopy2_n_25_n_30 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P7_n_176_n_15("single",2);
	this.instance.setTransform(23.4,20.7,1,1,0,0,0,23.4,20.7);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-8,-10.7,62.8,62.900000000000006);


(lib.ASDFASDFAScopy_n_24_n_23 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P7_n_176_n_15("single",1);
	this.instance.setTransform(23.4,20.7,1,1,0,0,0,23.4,20.7);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-8,-10.7,62.8,62.900000000000006);


(lib.ASDFASDFAS_n_23_n_14 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.instance = new lib.P7_n_176_n_15("single",0);
	this.instance.setTransform(23.4,20.7,1,1,0,0,0,23.4,20.7);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({rotation:360},69).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-8,-10.7,62.8,62.900000000000006);


(lib.ㄴㅁㅇㅁㄴㅇㄻㄴㅇㅁㄴcopy_n_19_n_3 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_77
	this.instance = new lib.BFGVFSDDSSFCSScopy3_n_40_n_32("synched",0);
	this.instance.setTransform(994.35,621.8,0.7,0.7,0,0,0,25.2,33.6);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({x:718.85,y:-291.1,startPosition:16},16,cjs.Ease.get(0.7)).to({x:671.75,y:742.05,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_76
	this.instance_1 = new lib.SDFCASDDFAScopy5_n_184_n_52("synched",0);
	this.instance_1.setTransform(-296.45,614.45,0.5,0.5,0,0,0,19.1,19);

	this.timeline.addTween(cjs.Tween.get(this.instance_1).to({x:-281.25,y:-367.3,startPosition:16},16,cjs.Ease.get(0.7)).to({x:-51.35,y:465.5,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_75
	this.instance_2 = new lib.VSDFCSASSDASD_n_192_n_8("synched",0);
	this.instance_2.setTransform(885.9,619.8,0.5,0.5,0,0,180,23.6,22.6);

	this.timeline.addTween(cjs.Tween.get(this.instance_2).to({x:587.45,y:-326.25,startPosition:16},16,cjs.Ease.get(0.7)).to({x:187.65,y:730.8,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_74
	this.instance_3 = new lib.SDFCASDDFAScopy6_n_185_n_59("synched",0);
	this.instance_3.setTransform(-185.85,625.45,0.5,0.5,0,0,0,19.1,19);

	this.timeline.addTween(cjs.Tween.get(this.instance_3).to({x:-28.15,y:-367.3,startPosition:16},16,cjs.Ease.get(0.7)).to({x:243.45,y:710.25,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_73
	this.instance_4 = new lib.MFBDVSDFCSDSDFCScopy6_n_155_n_56("synched",0);
	this.instance_4.setTransform(1058.85,661.9,0.5,0.5,0,0,0,17.3,17.3);

	this.timeline.addTween(cjs.Tween.get(this.instance_4).to({x:853.55,y:-264.85,startPosition:16},16,cjs.Ease.get(0.7)).to({regX:17.2,x:879.1,y:621.8,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_72
	this.instance_5 = new lib.FDSGDSSFSSDFSAcopy5_n_123_n_50("synched",29);
	this.instance_5.setTransform(996.85,620.65,0.7,0.7,0,0,0,31.5,9);

	this.timeline.addTween(cjs.Tween.get(this.instance_5).to({x:842.9,y:-350.4,startPosition:45},16,cjs.Ease.get(0.7)).to({regY:9.1,x:800.9,y:601.05,startPosition:37},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_71
	this.instance_6 = new lib.MFBDVSDFCSDSDFCScopy_n_150_n_21("synched",0);
	this.instance_6.setTransform(915.55,610.45,0.8,0.8,0,0,180,17.3,17.2);

	this.timeline.addTween(cjs.Tween.get(this.instance_6).to({x:704.15,y:-372.85,startPosition:16},16,cjs.Ease.get(0.7)).to({x:531.7,y:553.1,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_70
	this.instance_7 = new lib.NDFGBSFDSFDScopy5_n_168_n_40("synched",0);
	this.instance_7.setTransform(-290.25,649.15,1,1,0,0,0,19.9,24.4);

	this.timeline.addTween(cjs.Tween.get(this.instance_7).to({x:156.5,y:-255.1,startPosition:16},16,cjs.Ease.get(0.7)).to({x:294.1,y:567.7,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_69
	this.instance_8 = new lib.ASDFASDFAScopy_n_24_n_23("synched",0);
	this.instance_8.setTransform(-442.95,625.8,0.8,0.8,0,0,0,23.3,20.7);

	this.timeline.addTween(cjs.Tween.get(this.instance_8).to({x:-219.6,y:-297.95,startPosition:16},16,cjs.Ease.get(0.7)).to({x:-98.3,y:684.45,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_68
	this.instance_9 = new lib.VSDFCSASSDASD_n_192_n_8("synched",0);
	this.instance_9.setTransform(-460.2,624.8,0.64,0.64,0,0,180,23.7,22.5);

	this.timeline.addTween(cjs.Tween.get(this.instance_9).to({x:-308.15,y:-241.9,startPosition:16},16,cjs.Ease.get(0.7)).to({x:-159.9,y:567.05,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_67
	this.instance_10 = new lib.FDSGDSSFSSDFSAcopy4_n_122_n_43("synched",0);
	this.instance_10.setTransform(-247.35,628.9,0.8,0.8,0,0,0,31.5,9);

	this.timeline.addTween(cjs.Tween.get(this.instance_10).to({x:-120.05,y:-301.6,startPosition:16},16,cjs.Ease.get(0.7)).to({x:137.2,y:737.55,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_66
	this.instance_11 = new lib.FDSGDSSFSSDFSAcopy_n_119_n_22("synched",0);
	this.instance_11.setTransform(880.45,638.9,0.7,0.7,0,0,180,31.4,9);

	this.timeline.addTween(cjs.Tween.get(this.instance_11).to({x:292.55,y:-243.65,startPosition:16},16,cjs.Ease.get(0.7)).to({regX:31.5,regY:9.1,x:185.3,y:758.55,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_65
	this.instance_12 = new lib.VSDFCSASSDASDcopy2_n_194_n_27("synched",0);
	this.instance_12.setTransform(943.35,619.8,0.5,0.5,0,0,0,23.6,22.6);

	this.timeline.addTween(cjs.Tween.get(this.instance_12).to({x:638.5,y:-265.4,startPosition:16},16,cjs.Ease.get(0.7)).to({x:394.95,y:674.95,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_64
	this.instance_13 = new lib.NDFGBSFDSFDScopy6_n_169_n_47("synched",0);
	this.instance_13.setTransform(-398,617.95,1,1,0,0,0,19.9,24.4);

	this.timeline.addTween(cjs.Tween.get(this.instance_13).to({x:-221.45,y:-99.15,startPosition:16},16,cjs.Ease.get(0.7)).to({x:-1.1,y:734.9,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_63
	this.instance_14 = new lib.VSDFCSASSDASD_n_192_n_8("synched",0);
	this.instance_14.setTransform(972.9,644.15,1,1,0,0,180,23.6,22.6);

	this.timeline.addTween(cjs.Tween.get(this.instance_14).to({x:664.5,y:-217.85,startPosition:16},16,cjs.Ease.get(0.7)).to({x:536.8,y:973,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_62
	this.instance_15 = new lib.SDFCASDDFAScopy2_n_181_n_31("synched",0);
	this.instance_15.setTransform(991.3,644,0.5,0.5,0,0,0,19,19);

	this.timeline.addTween(cjs.Tween.get(this.instance_15).to({x:553.25,y:-31.55,startPosition:16},16,cjs.Ease.get(0.7)).to({regY:19.1,x:613.9,y:910.8,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_61
	this.instance_16 = new lib.SDFCASDDFAScopy_n_180_n_24("synched",0);
	this.instance_16.setTransform(1000.65,657.6,1,1,0,0,0,19,19);

	this.timeline.addTween(cjs.Tween.get(this.instance_16).to({x:729.2,y:-146.85,startPosition:16},16,cjs.Ease.get(0.7)).to({x:734.65,y:726,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_60
	this.instance_17 = new lib.ASDFASDFAScopy2_n_25_n_30("synched",0);
	this.instance_17.setTransform(-353.25,613.75,0.7,0.7,0,0,0,23.4,20.7);

	this.timeline.addTween(cjs.Tween.get(this.instance_17).to({x:-94.85,y:-228.25,startPosition:16},16,cjs.Ease.get(0.7)).to({x:64.95,y:669.6,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_58
	this.instance_18 = new lib.MFBDVSDFCSDSDFCS_n_149_n_10("synched",0);
	this.instance_18.setTransform(-228.9,691,0.7,0.7,0,0,0,17.2,17.2);

	this.timeline.addTween(cjs.Tween.get(this.instance_18).to({x:-4.95,y:-253.85,startPosition:16},16,cjs.Ease.get(0.7)).to({regY:17.3,x:74.85,y:970.4,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_57
	this.instance_19 = new lib.BFGVFSDDSSFCSScopy2_n_39_n_25("synched",19);
	this.instance_19.setTransform(-339.7,635.55,0.8,0.8,0,0,0,25.2,33.5);

	this.timeline.addTween(cjs.Tween.get(this.instance_19).to({x:-76.5,y:-139.65,startPosition:35},16,cjs.Ease.get(0.7)).to({x:-53.25,y:922.25,startPosition:27},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_56
	this.instance_20 = new lib.MFBDVSDFCSDSDFCScopy5_n_154_n_49("synched",0);
	this.instance_20.setTransform(-434.55,643.35,0.7,0.7,0,0,0,17.2,17.2);

	this.timeline.addTween(cjs.Tween.get(this.instance_20).to({x:-377.1,y:-137.4,startPosition:16},16,cjs.Ease.get(0.7)).to({regY:17.3,x:-295.75,y:732,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_55
	this.instance_21 = new lib.SDFCASDDFAScopy2_n_181_n_31("synched",0);
	this.instance_21.setTransform(1009.4,690.6,0.7,0.7,0,0,0,19.1,18.9);

	this.timeline.addTween(cjs.Tween.get(this.instance_21).to({x:865,y:-184.75,startPosition:16},16,cjs.Ease.get(0.7)).to({x:834.9,y:716.95,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_54
	this.instance_22 = new lib.SDFCASDDFAScopy4_n_183_n_45("synched",0);
	this.instance_22.setTransform(947.55,622.15,0.8,0.8,0,0,0,19.1,19.1);

	this.timeline.addTween(cjs.Tween.get(this.instance_22).to({x:561.25,y:-161.95,startPosition:16},16,cjs.Ease.get(0.7)).to({x:258.8,y:829.75,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_53
	this.instance_23 = new lib.NDFGBSFDSFDScopy_n_164_n_6("synched",0);
	this.instance_23.setTransform(877.7,662.2,1,1,0,0,180,19.9,24.4);

	this.timeline.addTween(cjs.Tween.get(this.instance_23).to({x:499.75,y:-91.15,startPosition:16},16,cjs.Ease.get(0.7)).to({x:355.4,y:751.8,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_52
	this.instance_24 = new lib.BFGVFSDDSSFCSScopy5_n_42_n_46("synched",9);
	this.instance_24.setTransform(908.05,638.6,0.77,0.77,0,0,0,25.2,33.5);

	this.timeline.addTween(cjs.Tween.get(this.instance_24).to({x:427.95,y:-281.6,startPosition:25},16,cjs.Ease.get(0.7)).to({x:417.95,y:831.15,startPosition:17},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_51
	this.instance_25 = new lib.SDFCASDDFAScopy6_n_185_n_59("synched",0);
	this.instance_25.setTransform(-164.4,699.65,0.5,0.5,0,0,0,19,19);

	this.timeline.addTween(cjs.Tween.get(this.instance_25).to({x:90.9,y:-156.35,startPosition:16},16,cjs.Ease.get(0.7)).to({regX:19.1,regY:19.1,x:264.45,y:897.65,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_50
	this.instance_26 = new lib.ASDFASDFAS_n_23_n_14("synched",0);
	this.instance_26.setTransform(-435.2,659.25,0.8,0.8,0,0,180,23.4,20.8);

	this.timeline.addTween(cjs.Tween.get(this.instance_26).to({x:-253.15,y:-34.9,startPosition:16},16,cjs.Ease.get(0.7)).to({x:-170.3,y:836.35,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_49
	this.instance_27 = new lib.ASDFASDFAScopy4_n_27_n_44("synched",0);
	this.instance_27.setTransform(1027.5,735.4,1,1,0,0,0,23.4,20.7);

	this.timeline.addTween(cjs.Tween.get(this.instance_27).to({x:925.8,y:-1.8,startPosition:16},16,cjs.Ease.get(0.7)).to({x:879.3,y:853.55,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_48
	this.instance_28 = new lib.NDFGBSFDSFDScopy_n_164_n_6("synched",0);
	this.instance_28.setTransform(1046.45,661.3,0.7,0.7,0,0,0,19.9,24.4);

	this.timeline.addTween(cjs.Tween.get(this.instance_28).to({x:739.4,y:-26.25,startPosition:16},16,cjs.Ease.get(0.7)).to({x:749.65,y:870.15,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_47
	this.instance_29 = new lib.BFGVFSDDSSFCSScopy4_n_41_n_39("synched",0);
	this.instance_29.setTransform(984.45,705.85,1,1,0,0,180,25.2,33.6);

	this.timeline.addTween(cjs.Tween.get(this.instance_29).to({x:653.45,y:-14.7,startPosition:16},16,cjs.Ease.get(0.7)).to({x:596.35,y:675.95,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_46
	this.instance_30 = new lib.FDSGDSSFSSDFSA_n_118_n_12("synched",0);
	this.instance_30.setTransform(883.95,689.85,1,1,0,0,0,31.5,9);

	this.timeline.addTween(cjs.Tween.get(this.instance_30).to({x:622.1,y:-73.4,startPosition:16},16,cjs.Ease.get(0.7)).to({x:362.1,y:855.4,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_45
	this.instance_31 = new lib.BFGVFSDDSSFCSS_n_37_n_4("synched",49);
	this.instance_31.setTransform(882.35,707.1,0.8,0.8,0,0,0,25.2,33.6);

	this.timeline.addTween(cjs.Tween.get(this.instance_31).to({x:453.1,y:5.85,startPosition:65},16,cjs.Ease.get(0.7)).to({x:501.6,y:811.5,startPosition:57},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_44
	this.instance_32 = new lib.MFBDVSDFCSDSDFCScopy3_n_152_n_35("synched",0);
	this.instance_32.setTransform(-246.9,674.5,0.7,0.7,0,0,0,17.2,17.3);

	this.timeline.addTween(cjs.Tween.get(this.instance_32).to({x:-156.65,y:-185.8,startPosition:16},16,cjs.Ease.get(0.7)).to({x:212.35,y:825.6,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_43
	this.instance_33 = new lib.VSDFCSASSDASDcopy5_n_197_n_48("synched",0);
	this.instance_33.setTransform(-323.8,681.8,1,1,0,0,0,23.6,22.6);

	this.timeline.addTween(cjs.Tween.get(this.instance_33).to({x:-175.95,y:139.3,startPosition:16},16,cjs.Ease.get(0.7)).to({x:-47.35,y:817.7,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_42
	this.instance_34 = new lib.ASDFASDFAS_n_23_n_14("synched",0);
	this.instance_34.setTransform(-253.25,711.25,0.5,0.5,0,0,0,23.4,20.7);

	this.timeline.addTween(cjs.Tween.get(this.instance_34).to({regX:23.3,x:5.35,y:-96.15,startPosition:16},16,cjs.Ease.get(0.7)).to({regX:23.4,regY:20.8,x:25.9,y:956.05,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_41
	this.instance_35 = new lib.NDFGBSFDSFDScopy4_n_167_n_33("synched",0);
	this.instance_35.setTransform(-224.25,716.2,0.7,0.7,0,0,0,19.9,24.4);

	this.timeline.addTween(cjs.Tween.get(this.instance_35).to({x:106.85,y:-41.2,startPosition:16},16,cjs.Ease.get(0.7)).to({x:176.6,y:992.35,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_40
	this.instance_36 = new lib.ASDFASDFAScopy3_n_26_n_37("synched",0);
	this.instance_36.setTransform(-485,703,1,1,0,0,0,23.4,20.7);

	this.timeline.addTween(cjs.Tween.get(this.instance_36).to({x:-368.25,y:-13.45,startPosition:16},16,cjs.Ease.get(0.7)).to({x:-335.8,y:936.3,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_39
	this.instance_37 = new lib.MFBDVSDFCSDSDFCScopy_n_150_n_21("synched",0);
	this.instance_37.setTransform(-390.2,653.95,0.8,0.8,0,0,180,17.3,17.2);

	this.timeline.addTween(cjs.Tween.get(this.instance_37).to({x:-252.1,y:48.4,startPosition:16},16,cjs.Ease.get(0.7)).to({x:-193.4,y:962.8,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_36
	this.instance_38 = new lib.BFGVFSDDSSFCSScopy3_n_40_n_32("synched",0);
	this.instance_38.setTransform(-147.05,723.65,0.7,0.7,0,0,180,25.2,33.6);

	this.timeline.addTween(cjs.Tween.get(this.instance_38).to({x:213.35,y:-94.05,startPosition:16},16,cjs.Ease.get(0.7)).to({x:356.3,y:945.4,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_35
	this.instance_39 = new lib.VSDFCSASSDASDcopy_n_193_n_20("synched",0);
	this.instance_39.setTransform(978.9,698.5,1,1,0,0,0,23.6,22.6);

	this.timeline.addTween(cjs.Tween.get(this.instance_39).to({x:610.05,y:146.1,startPosition:16},16,cjs.Ease.get(0.7)).to({x:670,y:959.15,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_34
	this.instance_40 = new lib.MFBDVSDFCSDSDFCScopy_n_150_n_21("synched",0);
	this.instance_40.setTransform(828.25,724.25,0.7,0.7,0,0,0,17.3,17.3);

	this.timeline.addTween(cjs.Tween.get(this.instance_40).to({x:576.05,y:66.05,startPosition:16},16,cjs.Ease.get(0.7)).to({x:568.9,y:1048.65,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_33
	this.instance_41 = new lib.SDFCASDDFAScopy2_n_181_n_31("synched",0);
	this.instance_41.setTransform(922.8,726.8,0.5,0.5,0,0,0,19.1,19);

	this.timeline.addTween(cjs.Tween.get(this.instance_41).to({x:680.05,y:-70.9,startPosition:16},16,cjs.Ease.get(0.7)).to({x:632.95,y:851.9,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_32
	this.instance_42 = new lib.FDSGDSSFSSDFSAcopy2_n_120_n_29("synched",0);
	this.instance_42.setTransform(-406.8,701.3,0.7,0.7,0,0,180,31.6,9);

	this.timeline.addTween(cjs.Tween.get(this.instance_42).to({regX:31.5,x:-290.6,y:93.15,startPosition:16},16,cjs.Ease.get(0.7)).to({regX:31.6,regY:9.1,x:-227,y:862.35,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_31
	this.instance_43 = new lib.SDFCASDDFAScopy5_n_184_n_52("synched",0);
	this.instance_43.setTransform(-243.1,729.2,0.7,0.7,0,0,0,19.1,19);

	this.timeline.addTween(cjs.Tween.get(this.instance_43).to({x:-19.3,y:-23.65,startPosition:16},16,cjs.Ease.get(0.7)).to({x:226.75,y:964.25,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_30
	this.instance_44 = new lib.ASDFASDFAScopy_n_24_n_23("synched",0);
	this.instance_44.setTransform(-235.45,782.8,0.7,0.7,0,0,0,23.3,20.7);

	this.timeline.addTween(cjs.Tween.get(this.instance_44).to({regX:23.4,x:124.1,y:30.75,startPosition:16},16,cjs.Ease.get(0.7)).to({regY:20.8,x:343.55,y:1102.9,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_29
	this.instance_45 = new lib.NDFGBSFDSFDScopy2_n_165_n_19("synched",0);
	this.instance_45.setTransform(826.4,729.05,1,1,0,0,0,19.9,24.4);

	this.timeline.addTween(cjs.Tween.get(this.instance_45).to({x:314.1,y:48.1,startPosition:16},16,cjs.Ease.get(0.7)).to({x:420.6,y:1013.85,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_28
	this.instance_46 = new lib.BFGVFSDDSSFCSScopy4_n_41_n_39("synched",0);
	this.instance_46.setTransform(-374.85,674.7,0.8,0.8,0,0,0,25.2,33.7);

	this.timeline.addTween(cjs.Tween.get(this.instance_46).to({x:-258.55,y:94.1,startPosition:16},16,cjs.Ease.get(0.7)).to({x:107.75,y:1059.4,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_27
	this.instance_47 = new lib.NDFGBSFDSFDScopy_n_164_n_6("synched",0);
	this.instance_47.setTransform(-250,765.6,0.7,0.7,0,0,180,19.9,24.4);

	this.timeline.addTween(cjs.Tween.get(this.instance_47).to({x:-82.9,y:77.05,startPosition:16},16,cjs.Ease.get(0.7)).to({x:22,y:1080.2,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_26
	this.instance_48 = new lib.VSDFCSASSDASDcopy4_n_196_n_41("synched",0);
	this.instance_48.setTransform(-250.35,761.25,0.7,0.7,0,0,0,23.6,22.6);

	this.timeline.addTween(cjs.Tween.get(this.instance_48).to({x:82.4,y:96.6,startPosition:16},16,cjs.Ease.get(0.7)).to({x:355.65,y:967.55,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_24
	this.instance_49 = new lib.ASDFASDFAScopy5_n_28_n_51("synched",0);
	this.instance_49.setTransform(807.3,763.4,0.7,0.7,0,0,0,23.4,20.7);

	this.timeline.addTween(cjs.Tween.get(this.instance_49).to({x:509.45,y:224.3,startPosition:16},16,cjs.Ease.get(0.7)).to({x:487.2,y:1061.45,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_23
	this.instance_50 = new lib.SDFCASDDFAScopy3_n_182_n_38("synched",0);
	this.instance_50.setTransform(864.3,763.85,0.5,0.5,0,0,0,19,19);

	this.timeline.addTween(cjs.Tween.get(this.instance_50).to({regX:19.1,x:541.05,y:167.8,startPosition:16},16,cjs.Ease.get(0.7)).to({x:374.65,y:1110.8,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_22
	this.instance_51 = new lib.SDFCASDDFAScopy6_n_185_n_59("synched",0);
	this.instance_51.setTransform(970,781.9,0.5,0.5,0,0,0,19.1,19);

	this.timeline.addTween(cjs.Tween.get(this.instance_51).to({x:847.6,y:215.3,startPosition:16},16,cjs.Ease.get(0.7)).to({x:822.1,y:1099.15,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_21
	this.instance_52 = new lib.SDFCASDDFAS_n_179_n_16("synched",0);
	this.instance_52.setTransform(1018.7,763.15,1,1,0,0,0,19,19);

	this.timeline.addTween(cjs.Tween.get(this.instance_52).to({x:879.6,y:62.55,startPosition:16},16,cjs.Ease.get(0.7)).to({x:753.85,y:1043.25,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_20
	this.instance_53 = new lib.NDFGBSFDSFDScopy3_n_166_n_26("synched",0);
	this.instance_53.setTransform(-234.3,747.75,0.63,0.63,0,0,0,19.9,24.4);

	this.timeline.addTween(cjs.Tween.get(this.instance_53).to({regX:20,regY:24.5,x:104.8,y:192.15,startPosition:16},16,cjs.Ease.get(0.7)).to({regX:19.9,x:225.1,y:1148.2,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_19
	this.instance_54 = new lib.SDFCASDDFAScopy2_n_181_n_31("synched",0);
	this.instance_54.setTransform(-469.35,773.2,0.5,0.5,0,0,0,19.1,19);

	this.timeline.addTween(cjs.Tween.get(this.instance_54).to({x:-379.6,y:196.3,startPosition:16},16,cjs.Ease.get(0.7)).to({x:-397.45,y:1084.95,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_18
	this.instance_55 = new lib.VSDFCSASSDASDcopy6_n_198_n_55("synched",0);
	this.instance_55.setTransform(-454.75,747.35,1,1,0,0,0,23.6,22.6);

	this.timeline.addTween(cjs.Tween.get(this.instance_55).to({x:-329.15,y:132.5,startPosition:16},16,cjs.Ease.get(0.7)).to({x:-311.45,y:1056.25,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_17
	this.instance_56 = new lib.ASDFASDFAScopy6_n_29_n_58("synched",0);
	this.instance_56.setTransform(-358.9,739.15,0.7,0.7,0,0,0,23.3,20.7);

	this.timeline.addTween(cjs.Tween.get(this.instance_56).to({x:-104.5,y:157.75,startPosition:16},16,cjs.Ease.get(0.7)).to({x:-64.45,y:1068.4,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_16
	this.instance_57 = new lib.ASDFASDFAScopy_n_24_n_23("synched",0);
	this.instance_57.setTransform(1083,792.9,0.8,0.8,0,0,180,23.4,20.8);

	this.timeline.addTween(cjs.Tween.get(this.instance_57).to({x:923.75,y:155.25,startPosition:16},16,cjs.Ease.get(0.7)).to({x:873.5,y:1038.2,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_15
	this.instance_58 = new lib.MFBDVSDFCSDSDFCScopy2_n_151_n_28("synched",0);
	this.instance_58.setTransform(1070.4,752.1,1,1,0,0,0,17.2,17.2);

	this.timeline.addTween(cjs.Tween.get(this.instance_58).to({x:826.95,y:46.2,startPosition:16},16,cjs.Ease.get(0.7)).to({x:759.6,y:965.35,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_14
	this.instance_59 = new lib.VSDFCSASSDASDcopy2_n_194_n_27("synched",0);
	this.instance_59.setTransform(915.35,798.85,0.5,0.5,0,0,0,23.6,22.6);

	this.timeline.addTween(cjs.Tween.get(this.instance_59).to({x:560.05,y:295.65,startPosition:16},16,cjs.Ease.get(0.7)).to({x:453.9,y:1185.9,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_12
	this.instance_60 = new lib.MFBDVSDFCSDSDFCScopy4_n_153_n_42("synched",0);
	this.instance_60.setTransform(-354.2,809.45,0.5,0.5,0,0,0,17.2,17.2);

	this.timeline.addTween(cjs.Tween.get(this.instance_60).to({regY:17.3,x:-45,y:208.6,startPosition:16},16,cjs.Ease.get(0.7)).to({x:47.9,y:1147.05,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_11
	this.instance_61 = new lib.BFGVFSDDSSFCSScopy_n_38_n_18("synched",0);
	this.instance_61.setTransform(941.9,764.75,1,1,0,0,0,25.2,33.6);

	this.timeline.addTween(cjs.Tween.get(this.instance_61).to({x:781.35,y:184.85,startPosition:16},16,cjs.Ease.get(0.7)).to({x:792.95,y:1136.9,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_10
	this.instance_62 = new lib.VSDFCSASSDASDcopy2_n_194_n_27("synched",0);
	this.instance_62.setTransform(888.75,743.45,0.7,0.7,0,0,0,23.6,22.6);

	this.timeline.addTween(cjs.Tween.get(this.instance_62).to({regX:23.7,x:639.4,y:209.85,startPosition:16},16,cjs.Ease.get(0.7)).to({x:436.45,y:1123.35,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_9
	this.instance_63 = new lib.FDSGDSSFSSDFSAcopy3_n_121_n_36("synched",0);
	this.instance_63.setTransform(-412.4,780.2,0.7,0.7,0,0,0,31.4,9.1);

	this.timeline.addTween(cjs.Tween.get(this.instance_63).to({x:-201.05,y:179.7,startPosition:16},16,cjs.Ease.get(0.7)).to({x:-162,y:1173.4,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_8
	this.instance_64 = new lib.BFGVFSDDSSFCSScopy2_n_39_n_25("synched",0);
	this.instance_64.setTransform(-307.2,796.55,0.8,0.8,0,0,0,25.2,33.5);

	this.timeline.addTween(cjs.Tween.get(this.instance_64).to({x:-144.45,y:209.8,startPosition:16},16,cjs.Ease.get(0.7)).to({x:-70.75,y:1183.9,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_7
	this.instance_65 = new lib.FDSGDSSFSSDFSAcopy6_n_124_n_57("synched",0);
	this.instance_65.setTransform(996.6,829.9,0.7,0.7,0,0,0,31.6,9.1);

	this.timeline.addTween(cjs.Tween.get(this.instance_65).to({x:701.15,y:302,startPosition:16},16,cjs.Ease.get(0.7)).to({x:700.65,y:1190.5,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_6
	this.instance_66 = new lib.SDFCASDDFAScopy5_n_184_n_52("synched",0);
	this.instance_66.setTransform(992.75,806.15,0.5,0.5,0,0,0,19.1,19.1);

	this.timeline.addTween(cjs.Tween.get(this.instance_66).to({x:882.1,y:318.1,startPosition:16},16,cjs.Ease.get(0.7)).to({x:821.65,y:1177.75,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_5
	this.instance_67 = new lib.SDFCASDDFAScopy_n_180_n_24("synched",0);
	this.instance_67.setTransform(-407.4,809.9,0.7,0.7,0,0,0,19,19.1);

	this.timeline.addTween(cjs.Tween.get(this.instance_67).to({x:-323.55,y:285.05,startPosition:16},16,cjs.Ease.get(0.7)).to({x:-297.1,y:1194.95,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_4
	this.instance_68 = new lib.SDFCASDDFAScopy6_n_185_n_59("synched",0);
	this.instance_68.setTransform(-393.2,801.4,0.5,0.5,0,0,0,19.1,19);

	this.timeline.addTween(cjs.Tween.get(this.instance_68).to({x:-210.4,y:318.05,startPosition:16},16,cjs.Ease.get(0.7)).to({x:-97.25,y:1171.45,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_3
	this.instance_69 = new lib.SDFCASDDFAScopy_n_180_n_24("synched",0);
	this.instance_69.setTransform(975.95,806.1,0.7,0.7,0,0,0,19,19.1);

	this.timeline.addTween(cjs.Tween.get(this.instance_69).to({x:748.7,y:404.55,startPosition:16},16,cjs.Ease.get(0.7)).to({x:752.05,y:1280.6,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	// Layer_1
	this.instance_70 = new lib.VSDFCSASSDASD_n_192_n_8("synched",0);
	this.instance_70.setTransform(-353.7,806.6,0.5,0.5,0,0,180,23.6,22.6);

	this.timeline.addTween(cjs.Tween.get(this.instance_70).to({x:-289.7,y:384.45,startPosition:16},16,cjs.Ease.get(0.7)).to({x:-209.3,y:1285.55,startPosition:8},132,cjs.Ease.get(-0.7)).to({_off:true},1).wait(1));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-508.4,-387.9,1610.1,1687.5);


(lib.캐릭터_모모_깜짝퀴즈3_n_14 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// 캐릭터_모모_팔
	this.instance = new lib.캐릭터_모모_팔_n_2_n_4_n_4("single",1);
	this.instance.setTransform(288.2,240.75,1,1,0,0,0,29.7,31.9);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(4).to({startPosition:1},0).to({regX:29.8,regY:32,rotation:-96.473,x:267,y:7.35,startPosition:3},9,cjs.Ease.get(1)).to({regX:29.7,regY:31.9,rotation:-66.7395,x:308.3,y:119.95},8,cjs.Ease.get(1)).to({regX:29.8,regY:32,rotation:-74.9998,x:307.5,y:93.95},6,cjs.Ease.get(1)).wait(9).to({startPosition:3},0).to({rotation:11.4013,x:278.65,y:254.35,startPosition:1},8).to({rotation:1.6282,x:286.9,y:242.75},6).to({_off:true},1).wait(15));

	// 캐릭터_모모_팔
	this.instance_1 = new lib.캐릭터_모모_팔_n_2_n_4_n_4("single",1);
	this.instance_1.setTransform(-26.1,239.25,1,1,0,0,180,29.7,31.9);

	this.timeline.addTween(cjs.Tween.get(this.instance_1).wait(4).to({startPosition:1},0).to({regX:29.8,regY:32,skewX:-21.474,skewY:158.526,x:-16.05,y:216.6},9,cjs.Ease.get(1)).to({regX:29.7,skewX:-7.4478,skewY:172.5522,x:-20.9,y:249.7},8,cjs.Ease.get(1)).to({regY:31.9,skewX:0,skewY:180,x:-26.1,y:239.25},6,cjs.Ease.get(1)).wait(9).to({startPosition:1},0).to({regY:32,skewX:-6.7008,skewY:173.2992,x:-20.2,y:250.05},8).to({skewX:-0.9565,skewY:179.0435,x:-25.35,y:240.85},6).to({_off:true},1).wait(15));

	// 캐릭터_모모_입__정면__n_4_n_6
	this.instance_2 = new lib.캐릭터_모모_입정면_n_4_n_6_n_5("single",0);
	this.instance_2.setTransform(127.75,175.85,1,1,0,0,0,30.9,20.8);

	this.timeline.addTween(cjs.Tween.get(this.instance_2).wait(4).to({mode:"synched",startPosition:4},0).to({regY:20.9,rotation:-6.4746,x:114.4,y:116,startPosition:10},9,cjs.Ease.get(1)).to({regY:20.8,rotation:0,x:127.75,y:182.15,mode:"single",startPosition:0},8,cjs.Ease.get(1)).to({y:175.85},6,cjs.Ease.get(1)).wait(9).to({startPosition:0},0).to({y:189.55},8).to({y:177.8},6).to({_off:true},1).wait(15));

	// 캐릭터_모모_눈__정면__n_5_n_7
	this.instance_3 = new lib.캐릭터_모모_눈정면_n_5_n_7_n_6("synched",0);
	this.instance_3.setTransform(128.9,89.7,1,1,0,0,0,55.4,34.5);

	this.timeline.addTween(cjs.Tween.get(this.instance_3).wait(4).to({mode:"single",startPosition:54},0).to({rotation:-6.4746,x:105.85,y:30.25},9,cjs.Ease.get(1)).to({rotation:0,x:128.9,y:101.7,mode:"synched",startPosition:0},8,cjs.Ease.get(1)).to({y:89.7,startPosition:6},6,cjs.Ease.get(1)).wait(9).to({startPosition:15},0).to({y:109.1,startPosition:0},8).to({y:92.45,startPosition:6},6).to({_off:true},1).wait(15));

	// 캐릭터_모모_머리__정면_
	this.instance_4 = new lib.캐릭터_모모_머리정면m_n_3_n_5_n_8("synched",0);
	this.instance_4.setTransform(129.9,222.1,1,1,0,0,0,105.6,161.5);

	this.timeline.addTween(cjs.Tween.get(this.instance_4).wait(4).to({startPosition:12},0).to({regX:105.5,rotation:-6.4746,x:121.7,y:171.55,startPosition:18},9,cjs.Ease.get(1)).to({regX:105.6,rotation:0,x:129.9,y:227.8,startPosition:0},8,cjs.Ease.get(1)).to({y:222.1,startPosition:6},6,cjs.Ease.get(1)).wait(9).to({startPosition:15},0).to({y:227.8,startPosition:0},8).to({y:222.9,startPosition:6},6).to({_off:true},1).wait(15));

	// 캐릭터_모모_팔
	this.instance_5 = new lib.캐릭터_모모_팔_n_2_n_4_n_4("single",0);
	this.instance_5.setTransform(266,216.35,1,1,0,0,0,7.5,7.5);

	this.timeline.addTween(cjs.Tween.get(this.instance_5).wait(4).to({startPosition:0},0).to({rotation:-96.473,x:245.15,y:32.25},9,cjs.Ease.get(1)).to({regX:7.4,rotation:-66.7395,x:277.05,y:130.8},8,cjs.Ease.get(1)).to({regX:7.5,rotation:-74.9998,x:278.1,y:109.15},6,cjs.Ease.get(1)).wait(9).to({startPosition:0},0).to({rotation:11.4013,x:261.65,y:225.95},8).to({rotation:1.6282,x:265.35,y:217.7},6).to({_off:true},1).wait(15));

	// 캐릭터_모모_팔
	this.instance_6 = new lib.캐릭터_모모_팔_n_2_n_4_n_4("single",0);
	this.instance_6.setTransform(-3.9,214.85,1,1,0,0,180,7.5,7.5);

	this.timeline.addTween(cjs.Tween.get(this.instance_6).wait(4).to({startPosition:0},0).to({regX:7.6,skewX:-21.474,skewY:158.526,x:-4.3,y:185.7},9,cjs.Ease.get(1)).to({regX:7.5,skewX:-7.4478,skewY:172.5522,x:-2.1,y:222.5},8,cjs.Ease.get(1)).to({skewX:0,skewY:180,x:-3.9,y:214.85},6,cjs.Ease.get(1)).wait(9).to({startPosition:0},0).to({regX:7.4,skewX:-6.7008,skewY:173.2992,x:-0.9,y:223.1},8).to({skewX:-0.9565,skewY:179.0435,x:-3.5,y:216},6).to({_off:true},1).wait(15));

	// 캐릭터_모모_다리
	this.instance_7 = new lib.캐릭터_모모_다리_n_7_n_10_n_10("single",1);
	this.instance_7.setTransform(163.5,320.4,1,1,0,0,180,19.1,40.5);

	this.timeline.addTween(cjs.Tween.get(this.instance_7).wait(4).to({startPosition:1},0).to({regX:19,skewX:-14.9983,skewY:165.0017,x:179.2,y:264.55},9,cjs.Ease.get(1)).to({regX:19.1,skewX:0,skewY:180,x:163.5,y:320.4},8,cjs.Ease.get(1)).to({startPosition:1},6,cjs.Ease.get(1)).wait(9).to({startPosition:1},0).to({startPosition:1},8).to({startPosition:1},6).to({_off:true},1).wait(15));

	// 캐릭터_모모_다리
	this.instance_8 = new lib.캐릭터_모모_다리_n_7_n_10_n_10("single",0);
	this.instance_8.setTransform(163.4,289.1,1,1,0,0,180,19.2,9.2);

	this.timeline.addTween(cjs.Tween.get(this.instance_8).wait(4).to({startPosition:0},0).to({regX:19.1,skewX:-14.9983,skewY:165.0017,x:171,y:234.4},9,cjs.Ease.get(1)).to({regX:19.2,skewX:0,skewY:180,x:163.4,y:289.1},8,cjs.Ease.get(1)).to({startPosition:0},6,cjs.Ease.get(1)).wait(9).to({startPosition:0},0).to({startPosition:0},8).to({startPosition:0},6).to({_off:true},1).wait(15));

	// 캐릭터_모모_다리
	this.instance_9 = new lib.캐릭터_모모_다리_n_7_n_10_n_10("single",1);
	this.instance_9.setTransform(92.15,320.4,1,1,0,0,0,19.1,40.5);

	this.timeline.addTween(cjs.Tween.get(this.instance_9).wait(4).to({startPosition:1},0).to({rotation:14.9983,x:76.55,y:264.55},9,cjs.Ease.get(1)).to({rotation:0,x:92.15,y:320.4},8,cjs.Ease.get(1)).to({startPosition:1},6,cjs.Ease.get(1)).wait(9).to({startPosition:1},0).to({startPosition:1},8).to({startPosition:1},6).to({_off:true},1).wait(15));

	// 캐릭터_모모_다리
	this.instance_10 = new lib.캐릭터_모모_다리_n_7_n_10_n_10("single",0);
	this.instance_10.setTransform(92.25,289.1,1,1,0,0,0,19.2,9.2);

	this.timeline.addTween(cjs.Tween.get(this.instance_10).wait(4).to({startPosition:0},0).to({rotation:14.9983,x:84.75,y:234.35},9,cjs.Ease.get(1)).to({rotation:0,x:92.25,y:289.1},8,cjs.Ease.get(1)).to({startPosition:0},6,cjs.Ease.get(1)).wait(9).to({startPosition:0},0).to({startPosition:0},8).to({startPosition:0},6).to({_off:true},1).wait(15));

	// 캐릭터_모모_대롱
	this.instance_11 = new lib.캐릭터_모모_대롱_n_8_n_11_n_11("single",0);
	this.instance_11.setTransform(131.25,18.5,1,1,0,0,0,10.8,63);

	this.timeline.addTween(cjs.Tween.get(this.instance_11).wait(4).to({startPosition:0},0).to({regY:62.9,rotation:8.5242,x:100.2,y:-30.9},9,cjs.Ease.get(1)).to({regY:63,rotation:14.9983,x:131.25,y:24.25},8,cjs.Ease.get(1)).to({rotation:0,y:18.5},6,cjs.Ease.get(1)).wait(9).to({startPosition:0},0).to({rotation:14.9983,y:24.25},8).to({regX:10.9,rotation:2.1425,y:19.3},6).to({_off:true},1).wait(15));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-69.4,-135.5,433.1,516.4);


(lib.캐릭터_모모_깜짝퀴즈2_n_13 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// 캐릭터_모모_팔
	this.instance = new lib.캐릭터_모모_팔_n_2_n_4_n_4("single",1);
	this.instance.setTransform(288.2,240.75,1,1,0,0,0,29.7,31.9);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(14).to({startPosition:1},0).to({regX:29.6,scaleX:0.9999,scaleY:0.9999,rotation:-118.0558,x:281.6,y:217.85,startPosition:5},6).to({scaleX:1,scaleY:1,rotation:-102.5672,x:289,y:247.5},6).wait(46).to({startPosition:5},0).to({regX:29.7,regY:32,rotation:5.6903,x:283.25,y:250.35,startPosition:1},8).to({regY:31.9,rotation:0,x:288.2,y:240.75},8).wait(81).to({startPosition:1},0).to({_off:true},1).wait(1).to({_off:false},0).wait(9));

	// 캐릭터_모모_팔
	this.instance_1 = new lib.캐릭터_모모_팔_n_2_n_4_n_4("single",1);
	this.instance_1.setTransform(-26.1,239.25,1,1,0,0,180,29.7,31.9);

	this.timeline.addTween(cjs.Tween.get(this.instance_1).wait(14).to({startPosition:1},0).to({regX:29.8,regY:32,scaleX:0.9999,scaleY:0.9999,skewX:7.1733,skewY:187.1733,x:-40.45,y:245.65},6).to({regX:29.7,scaleX:1,scaleY:1,skewX:7.9323,skewY:187.9323,x:-26.8,y:229.15},6).wait(46).to({startPosition:1},0).to({skewX:-8.4532,skewY:171.5467,x:-20.9,y:249.05},8).to({regY:31.9,skewX:0,skewY:180,x:-26.1,y:239.25},8).wait(81).to({startPosition:1},0).to({_off:true},1).wait(1).to({_off:false},0).wait(9));

	// 캐릭터_모모_입__정면__n_4_n_6
	this.instance_2 = new lib.캐릭터_모모_입정면_n_4_n_6_n_5("single",0);
	this.instance_2.setTransform(127.75,175.85,1,1,0,0,0,30.9,20.8);

	this.timeline.addTween(cjs.Tween.get(this.instance_2).wait(14).to({mode:"synched"},0).to({regX:31,rotation:-4.5415,x:116.3,y:178,startPosition:6},6).to({regX:30.9,rotation:2.4312,x:133.1,y:175.9,startPosition:12},6).wait(43).to({rotation:2.4312,mode:"single",startPosition:0},0).wait(3).to({startPosition:0},0).to({rotation:0,x:127.75,y:181.05},8).to({y:175.85},8).wait(81).to({startPosition:0},0).to({_off:true},1).wait(1).to({_off:false},0).wait(9));

	// 캐릭터_모모_눈__정면__n_5_n_7
	this.instance_3 = new lib.캐릭터_모모_눈정면_n_5_n_7_n_6("synched",0);
	this.instance_3.setTransform(128.9,89.7,1,1,0,0,0,55.4,34.5);

	this.timeline.addTween(cjs.Tween.get(this.instance_3).wait(14).to({startPosition:11},0).to({regX:55.5,regY:34.4,rotation:-4.5415,x:110.6,y:91.95,startPosition:17},6).to({regX:55.4,regY:34.5,rotation:2.4312,x:137.95,y:89.85,startPosition:23},6).wait(46).to({rotation:2.4312,startPosition:64},0).to({rotation:0,x:128.9,y:94.9,startPosition:0},8).to({y:89.7,startPosition:8},8).wait(18).to({startPosition:26,loop:false},0).wait(63).to({startPosition:89},0).to({_off:true},1).wait(1).to({_off:false,startPosition:91},0).wait(9));

	// 캐릭터_모모_머리__정면_
	this.instance_4 = new lib.캐릭터_모모_머리정면m_n_3_n_5_n_8("synched",0);
	this.instance_4.setTransform(129.9,222.1,1,1,0,0,0,105.6,161.5);

	this.timeline.addTween(cjs.Tween.get(this.instance_4).wait(14).to({startPosition:11},0).to({regX:105.8,regY:161.6,rotation:-4.5415,x:122.2,y:224,startPosition:17},6).to({regX:105.7,regY:161.5,rotation:2.4312,x:133.45,y:222.2,startPosition:23},6).wait(46).to({rotation:2.4312,startPosition:64},0).to({regX:105.6,rotation:0,x:129.9,y:227.3,startPosition:0},8).to({y:222.1,startPosition:8},8).wait(81).to({startPosition:89},0).to({_off:true},1).wait(1).to({_off:false,startPosition:91},0).wait(9));

	// 캐릭터_모모_팔
	this.instance_5 = new lib.캐릭터_모모_팔_n_2_n_4_n_4("single",0);
	this.instance_5.setTransform(266,216.35,1,1,0,0,0,7.5,7.5);

	this.timeline.addTween(cjs.Tween.get(this.instance_5).wait(14).to({startPosition:0},0).to({regX:7.7,regY:7.6,rotation:-19.5401,x:252.45,y:202},6).to({regX:7.5,regY:7.5,rotation:2.4312,x:269.6,y:222.2},6).wait(46).to({rotation:2.4312},0).to({rotation:5.6903,x:263.55,y:223.75},8).to({rotation:0,x:266,y:216.35},8).wait(81).to({startPosition:0},0).to({_off:true},1).wait(1).to({_off:false},0).wait(9));

	// 캐릭터_모모_팔
	this.instance_6 = new lib.캐릭터_모모_팔_n_2_n_4_n_4("single",0);
	this.instance_6.setTransform(-3.9,214.85,1,1,0,0,180,7.5,7.5);

	this.timeline.addTween(cjs.Tween.get(this.instance_6).wait(14).to({startPosition:0},0).to({regX:7.4,scaleX:0.9999,scaleY:0.9999,skewX:7.1733,skewY:187.1733,x:-15.2,y:224.15},6).to({regX:7.5,scaleX:1,scaleY:1,skewX:7.9323,skewY:187.9323,x:-1.5,y:207.95},6).wait(46).to({startPosition:0},0).to({skewX:-8.4532,skewY:171.5467,x:-2.5,y:221.55},8).to({skewX:0,skewY:180,x:-3.9,y:214.85},8).wait(81).to({startPosition:0},0).to({_off:true},1).wait(1).to({_off:false},0).wait(9));

	// 캐릭터_모모_다리
	this.instance_7 = new lib.캐릭터_모모_다리_n_7_n_10_n_10("single",1);
	this.instance_7.setTransform(163.5,320.4,1,1,0,0,180,19.1,40.5);

	this.timeline.addTween(cjs.Tween.get(this.instance_7).wait(14).to({startPosition:1},0).to({startPosition:1},6).to({startPosition:1},6).wait(46).to({startPosition:1},0).to({startPosition:1},8).to({startPosition:1},8).wait(81).to({startPosition:1},0).to({_off:true},1).wait(1).to({_off:false},0).wait(9));

	// 캐릭터_모모_다리
	this.instance_8 = new lib.캐릭터_모모_다리_n_7_n_10_n_10("single",0);
	this.instance_8.setTransform(163.4,289.1,1,1,0,0,180,19.2,9.2);

	this.timeline.addTween(cjs.Tween.get(this.instance_8).wait(14).to({startPosition:0},0).to({startPosition:0},6).to({startPosition:0},6).wait(46).to({startPosition:0},0).to({startPosition:0},8).to({startPosition:0},8).wait(81).to({startPosition:0},0).to({_off:true},1).wait(1).to({_off:false},0).wait(9));

	// 캐릭터_모모_다리
	this.instance_9 = new lib.캐릭터_모모_다리_n_7_n_10_n_10("single",1);
	this.instance_9.setTransform(92.15,320.4,1,1,0,0,0,19.1,40.5);

	this.timeline.addTween(cjs.Tween.get(this.instance_9).wait(14).to({startPosition:1},0).to({startPosition:1},6).to({startPosition:1},6).wait(46).to({startPosition:1},0).to({startPosition:1},8).to({startPosition:1},8).wait(81).to({startPosition:1},0).to({_off:true},1).wait(1).to({_off:false},0).wait(9));

	// 캐릭터_모모_다리
	this.instance_10 = new lib.캐릭터_모모_다리_n_7_n_10_n_10("single",0);
	this.instance_10.setTransform(92.25,289.1,1,1,0,0,0,19.2,9.2);

	this.timeline.addTween(cjs.Tween.get(this.instance_10).wait(14).to({startPosition:0},0).to({startPosition:0},6).to({startPosition:0},6).wait(46).to({startPosition:0},0).to({startPosition:0},8).to({startPosition:0},8).wait(81).to({startPosition:0},0).to({_off:true},1).wait(1).to({_off:false},0).wait(9));

	// 캐릭터_모모_대롱
	this.instance_11 = new lib.캐릭터_모모_대롱_n_8_n_11_n_11("single",0);
	this.instance_11.setTransform(131.25,18.5,1,1,0,0,0,10.8,63);

	this.timeline.addTween(cjs.Tween.get(this.instance_11).wait(14).to({startPosition:0},0).to({regX:11,regY:62.9,rotation:3.4276,x:107.45,y:20.9},6).to({regX:10.8,rotation:10.3996,x:143.3,y:18.75},6).wait(46).to({startPosition:0},0).to({regX:10.9,rotation:5.9909,x:131.35,y:23.65},8).to({regX:10.8,regY:63,rotation:0,x:131.25,y:18.5},8).wait(81).to({startPosition:0},0).to({_off:true},1).wait(1).to({_off:false},0).wait(9));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-89.3,-85.7,439.3,466.59999999999997);


(lib.캐릭터_모모_깜짝퀴즈_등장대기 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// 캐릭터_모모_팔
	this.instance = new lib.캐릭터_모모_팔_n_2_n_4_n_697("single",5);
	this.instance.setTransform(300.7,161,1,1,-74.9998,0,0,29.8,32);

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(4).to({startPosition:5},0).to({rotation:-69.7981,x:303.1,y:171},12,cjs.Ease.quadInOut).to({rotation:-74.9998,x:300.7,y:161},12,cjs.Ease.quadInOut).wait(14).to({startPosition:5},0).to({rotation:-69.7981,x:303.1,y:171},12,cjs.Ease.quadInOut).to({rotation:-74.9998,x:300.7,y:161},12,cjs.Ease.quadInOut).to({rotation:-69.7981,x:303.1,y:171},12,cjs.Ease.quadInOut).to({rotation:-74.9998,x:300.7,y:161},12,cjs.Ease.quadInOut).wait(10));

	// 캐릭터_모모_팔
	this.instance_1 = new lib.캐릭터_모모_팔_n_2_n_4_n_697("single",5);
	this.instance_1.setTransform(-47.95,158,1,1,0,70.2401,-109.7599,29.6,31.9);

	this.timeline.addTween(cjs.Tween.get(this.instance_1).wait(4).to({regY:32,x:-48.05},0).to({skewX:61.7518,skewY:-118.2482,x:-49.55,y:168.1},12,cjs.Ease.quadInOut).to({skewX:70.2401,skewY:-109.7599,x:-48.05,y:158},12,cjs.Ease.quadInOut).wait(14).to({startPosition:5},0).to({skewX:61.7518,skewY:-118.2482,x:-49.55,y:168.1},12,cjs.Ease.quadInOut).to({skewX:70.2401,skewY:-109.7599,x:-48.05,y:158},12,cjs.Ease.quadInOut).to({skewX:61.7518,skewY:-118.2482,x:-49.55,y:168.1},12,cjs.Ease.quadInOut).to({skewX:70.2401,skewY:-109.7599,x:-48.05,y:158},12,cjs.Ease.quadInOut).wait(10));

	// 캐릭터_모모_입__정면__n_4_n_6
	this.instance_2 = new lib.캐릭터_모모_입정면_n_4_n_6_n_720("single",0);
	this.instance_2.setTransform(127.75,175.85,1,1,0,0,0,30.9,20.8);

	this.timeline.addTween(cjs.Tween.get(this.instance_2).wait(4).to({startPosition:0},0).to({y:184.85},12,cjs.Ease.quadInOut).to({y:175.85},12,cjs.Ease.quadInOut).wait(14).to({startPosition:0},0).to({y:184.85},12,cjs.Ease.quadInOut).to({y:175.85},12,cjs.Ease.quadInOut).to({y:184.85},12,cjs.Ease.quadInOut).to({y:175.85},12,cjs.Ease.quadInOut).wait(10));

	// 캐릭터_모모_눈__정면__n_5_n_7
	this.instance_3 = new lib.캐릭터_모모_눈정면_n_5_n_7_n_690("single",53);
	this.instance_3.setTransform(128.9,89.7,1,1,0,0,0,55.4,34.5);

	this.timeline.addTween(cjs.Tween.get(this.instance_3).wait(4).to({startPosition:53},0).to({y:98.7},12,cjs.Ease.quadInOut).to({y:89.7},12,cjs.Ease.quadInOut).wait(14).to({startPosition:53},0).to({y:98.7},12,cjs.Ease.quadInOut).to({y:89.7},12,cjs.Ease.quadInOut).to({y:98.7},12,cjs.Ease.quadInOut).to({y:89.7},12,cjs.Ease.quadInOut).wait(10));

	// 캐릭터_모모_머리__정면_
	this.instance_4 = new lib.캐릭터_모모_머리정면m_n_3_n_5_n_701("synched",40);
	this.instance_4.setTransform(129.9,222.1,1,1,0,0,0,105.6,161.5);

	this.timeline.addTween(cjs.Tween.get(this.instance_4).wait(4).to({startPosition:44},0).to({y:227.5,startPosition:54},12,cjs.Ease.quadInOut).to({y:222.1,startPosition:66},12,cjs.Ease.quadInOut).wait(14).to({startPosition:44},0).to({y:227.5,startPosition:54},12,cjs.Ease.quadInOut).to({y:222.1,startPosition:44},12,cjs.Ease.quadInOut).to({y:227.5,startPosition:54},12,cjs.Ease.quadInOut).to({y:222.1,startPosition:66},12,cjs.Ease.quadInOut).wait(10));

	// 캐릭터_모모_팔
	this.instance_5 = new lib.캐릭터_모모_팔_n_2_n_4_n_697("single",0);
	this.instance_5.setTransform(271.3,176.2,1,1,-74.9998,0,0,7.5,7.5);

	this.timeline.addTween(cjs.Tween.get(this.instance_5).wait(4).to({startPosition:0},0).to({regY:7.6,rotation:-69.7981,x:272.5,y:183.45},12,cjs.Ease.quadInOut).to({regY:7.5,rotation:-74.9998,x:271.3,y:176.2},12,cjs.Ease.quadInOut).wait(14).to({startPosition:0},0).to({regY:7.6,rotation:-69.7981,x:272.5,y:183.45},12,cjs.Ease.quadInOut).to({regY:7.5,rotation:-74.9998,x:271.3,y:176.2},12,cjs.Ease.quadInOut).to({regY:7.6,rotation:-69.7981,x:272.5,y:183.45},12,cjs.Ease.quadInOut).to({regY:7.5,rotation:-74.9998,x:271.3,y:176.2},12,cjs.Ease.quadInOut).wait(10));

	// 캐릭터_모모_팔
	this.instance_6 = new lib.캐릭터_모모_팔_n_2_n_4_n_697("single",0);
	this.instance_6.setTransform(-17.5,170.65,1,1,0,70.2401,-109.7599,7.4,7.5);

	this.timeline.addTween(cjs.Tween.get(this.instance_6).wait(4).to({startPosition:0},0).to({skewX:61.7518,skewY:-118.2482,x:-17.45,y:176.05},12,cjs.Ease.quadInOut).to({skewX:70.2401,skewY:-109.7599,x:-17.5,y:170.65},12,cjs.Ease.quadInOut).wait(14).to({startPosition:0},0).to({skewX:61.7518,skewY:-118.2482,x:-17.45,y:176.05},12,cjs.Ease.quadInOut).to({skewX:70.2401,skewY:-109.7599,x:-17.5,y:170.65},12,cjs.Ease.quadInOut).to({skewX:61.7518,skewY:-118.2482,x:-17.45,y:176.05},12,cjs.Ease.quadInOut).to({skewX:70.2401,skewY:-109.7599,x:-17.5,y:170.65},12,cjs.Ease.quadInOut).wait(10));

	// 캐릭터_모모_다리
	this.instance_7 = new lib.캐릭터_모모_다리_n_7_n_10_n_714("single",1);
	this.instance_7.setTransform(163.5,320.4,1,1,0,0,180,19.1,40.5);

	this.timeline.addTween(cjs.Tween.get(this.instance_7).wait(4).to({startPosition:1},0).to({startPosition:1},12,cjs.Ease.quadInOut).to({startPosition:1},12,cjs.Ease.quadInOut).wait(14).to({startPosition:1},0).to({startPosition:1},12,cjs.Ease.quadInOut).to({startPosition:1},12,cjs.Ease.quadInOut).to({startPosition:1},12,cjs.Ease.quadInOut).to({startPosition:1},12,cjs.Ease.quadInOut).wait(10));

	// 캐릭터_모모_다리
	this.instance_8 = new lib.캐릭터_모모_다리_n_7_n_10_n_714("single",0);
	this.instance_8.setTransform(163.4,289.1,1,1,0,0,180,19.2,9.2);

	this.timeline.addTween(cjs.Tween.get(this.instance_8).wait(4).to({startPosition:0},0).to({startPosition:0},12,cjs.Ease.quadInOut).to({startPosition:0},12,cjs.Ease.quadInOut).wait(14).to({startPosition:0},0).to({startPosition:0},12,cjs.Ease.quadInOut).to({startPosition:0},12,cjs.Ease.quadInOut).to({startPosition:0},12,cjs.Ease.quadInOut).to({startPosition:0},12,cjs.Ease.quadInOut).wait(10));

	// 캐릭터_모모_다리
	this.instance_9 = new lib.캐릭터_모모_다리_n_7_n_10_n_714("single",1);
	this.instance_9.setTransform(92.15,320.4,1,1,0,0,0,19.1,40.5);

	this.timeline.addTween(cjs.Tween.get(this.instance_9).wait(4).to({startPosition:1},0).to({startPosition:1},12,cjs.Ease.quadInOut).to({startPosition:1},12,cjs.Ease.quadInOut).wait(14).to({startPosition:1},0).to({startPosition:1},12,cjs.Ease.quadInOut).to({startPosition:1},12,cjs.Ease.quadInOut).to({startPosition:1},12,cjs.Ease.quadInOut).to({startPosition:1},12,cjs.Ease.quadInOut).wait(10));

	// 캐릭터_모모_다리
	this.instance_10 = new lib.캐릭터_모모_다리_n_7_n_10_n_714("single",0);
	this.instance_10.setTransform(92.25,289.1,1,1,0,0,0,19.2,9.2);

	this.timeline.addTween(cjs.Tween.get(this.instance_10).wait(4).to({startPosition:0},0).to({startPosition:0},12,cjs.Ease.quadInOut).to({startPosition:0},12,cjs.Ease.quadInOut).wait(14).to({startPosition:0},0).to({startPosition:0},12,cjs.Ease.quadInOut).to({startPosition:0},12,cjs.Ease.quadInOut).to({startPosition:0},12,cjs.Ease.quadInOut).to({startPosition:0},12,cjs.Ease.quadInOut).wait(10));

	// 캐릭터_모모_대롱
	this.instance_11 = new lib.캐릭터_모모_대롱_n_8_n_11_n_687("single",0);
	this.instance_11.setTransform(131.25,18.5,1,1,0,0,0,10.8,63);

	this.timeline.addTween(cjs.Tween.get(this.instance_11).wait(4).to({startPosition:0},0).to({rotation:7.2152,y:23.95},12,cjs.Ease.quadInOut).to({rotation:0,y:18.5},12,cjs.Ease.quadInOut).wait(14).to({startPosition:0},0).to({rotation:7.2152,y:23.95},12,cjs.Ease.quadInOut).to({rotation:0,y:18.5},12,cjs.Ease.quadInOut).to({rotation:7.2152,y:23.95},12,cjs.Ease.quadInOut).to({rotation:0,y:18.5},12,cjs.Ease.quadInOut).wait(10));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-111.8,-85.5,474.7,466.4);


(lib.캐릭터_모모_깜짝퀴즈_등장 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// 캐릭터_모모_팔
	this.instance = new lib.캐릭터_모모_팔_n_2_n_4_n_697("single",1);
	this.instance.setTransform(288.2,809.25,1,1,0,0,0,29.7,31.9);

	this.timeline.addTween(cjs.Tween.get(this.instance).to({regX:29.8,regY:32,rotation:-29.9996,x:306.4,y:96.9},10,cjs.Ease.quadInOut).to({rotation:7.4752,x:281.65,y:255.65},10,cjs.Ease.quadInOut).to({regX:29.7,regY:31.9,rotation:-7.9631,x:294.35,y:224.1},7,cjs.Ease.quadInOut).to({rotation:0,x:288.2,y:240.75},6,cjs.Ease.quadInOut).wait(9).to({startPosition:1},0).to({regX:29.8,regY:32,rotation:-74.9998,x:300.7,y:161,startPosition:5},4).wait(5).to({startPosition:5},0).to({_off:true},1).wait(41));

	// 캐릭터_모모_팔
	this.instance_1 = new lib.캐릭터_모모_팔_n_2_n_4_n_697("single",1);
	this.instance_1.setTransform(-26.1,807.75,1,1,0,0,180,29.7,31.9);

	this.timeline.addTween(cjs.Tween.get(this.instance_1).to({regY:32,skewX:23.9888,skewY:203.9888,x:-56.25,y:108.85},10,cjs.Ease.quadInOut).to({skewX:-6.7474,skewY:173.2526,x:-20.15,y:253.6},10,cjs.Ease.quadInOut).to({skewX:13.9357,skewY:193.9357,x:-37.15,y:217.05},7,cjs.Ease.quadInOut).to({regY:31.9,skewX:0,skewY:180,x:-26.1,y:239.25},6,cjs.Ease.quadInOut).wait(9).to({startPosition:1},0).to({regX:29.6,skewX:70.2401,skewY:250.2401,x:-47.95,y:158,startPosition:5},4).wait(5).to({regY:32,x:-48.05},0).to({_off:true},1).wait(41));

	// 캐릭터_모모_입__정면__n_4_n_6
	this.instance_2 = new lib.캐릭터_모모_입정면_n_4_n_6_n_720("single",4);
	this.instance_2.setTransform(127.75,744.35,1,1,0,0,0,30.9,20.8);

	this.timeline.addTween(cjs.Tween.get(this.instance_2).to({y:94.3,startPosition:5},10,cjs.Ease.quadInOut).to({y:194.25,startPosition:4},10,cjs.Ease.quadInOut).to({y:164.15,startPosition:0},7,cjs.Ease.quadInOut).to({y:175.85},6,cjs.Ease.quadInOut).wait(9).to({startPosition:0},0).to({startPosition:0},4).wait(5).to({startPosition:0},0).to({_off:true},1).wait(41));

	// 캐릭터_모모_눈__정면__n_5_n_7
	this.instance_3 = new lib.캐릭터_모모_눈정면_n_5_n_7_n_690("synched",0);
	this.instance_3.setTransform(128.9,658.2,1,1,0,0,0,55.4,34.5);

	this.timeline.addTween(cjs.Tween.get(this.instance_3).to({y:8.15,startPosition:7},10,cjs.Ease.quadInOut).to({y:108.1,startPosition:15},10,cjs.Ease.quadInOut).to({y:78,startPosition:22},7,cjs.Ease.quadInOut).to({y:89.7,startPosition:27},6,cjs.Ease.quadInOut).wait(9).to({startPosition:36},0).to({mode:"single",startPosition:53},4).wait(5).to({startPosition:53},0).to({_off:true},1).wait(41));

	// 캐릭터_모모_머리__정면_
	this.instance_4 = new lib.캐릭터_모모_머리정면m_n_3_n_5_n_701("synched",0);
	this.instance_4.setTransform(129.9,790.6,1,1,0,0,0,105.6,161.5);

	this.timeline.addTween(cjs.Tween.get(this.instance_4).to({y:140.55,startPosition:7},10,cjs.Ease.quadInOut).to({y:231.3,startPosition:15},10,cjs.Ease.quadInOut).to({y:217.6,startPosition:22},7,cjs.Ease.quadInOut).to({y:222.1,startPosition:27},6,cjs.Ease.quadInOut).wait(9).to({startPosition:36},0).to({startPosition:40},4).wait(5).to({startPosition:45},0).to({_off:true},1).wait(41));

	// 캐릭터_모모_팔
	this.instance_5 = new lib.캐릭터_모모_팔_n_2_n_4_n_697("single",0);
	this.instance_5.setTransform(266,784.85,1,1,0,0,0,7.5,7.5);

	this.timeline.addTween(cjs.Tween.get(this.instance_5).to({rotation:-29.9996,x:274.85,y:86.85},10,cjs.Ease.quadInOut).to({rotation:7.4752,x:262.7,y:228.45},10,cjs.Ease.quadInOut).to({rotation:-7.9631,x:269.05,y:203},7,cjs.Ease.quadInOut).to({rotation:0,x:266,y:216.35},6,cjs.Ease.quadInOut).wait(9).to({startPosition:0},0).to({rotation:-74.9998,x:271.3,y:176.2},4).wait(5).to({startPosition:0},0).to({_off:true},1).wait(41));

	// 캐릭터_모모_팔
	this.instance_6 = new lib.캐릭터_모모_팔_n_2_n_4_n_697("single",0);
	this.instance_6.setTransform(-3.9,783.35,1,1,0,0,180,7.5,7.5);

	this.timeline.addTween(cjs.Tween.get(this.instance_6).to({skewX:23.9888,skewY:203.9888,x:-26,y:95.45},10,cjs.Ease.quadInOut).to({skewX:-6.7474,skewY:173.2526,x:-0.95,y:226.65},10,cjs.Ease.quadInOut).to({regY:7.4,skewX:13.9357,skewY:193.9357,x:-9.7,y:198.55},7,cjs.Ease.quadInOut).to({regY:7.5,skewX:0,skewY:180,x:-3.9,y:214.85},6,cjs.Ease.quadInOut).wait(9).to({startPosition:0},0).to({regX:7.4,skewX:70.2401,skewY:250.2401,x:-17.5,y:170.65},4).wait(5).to({startPosition:0},0).to({_off:true},1).wait(41));

	// 캐릭터_모모_다리
	this.instance_7 = new lib.캐릭터_모모_다리_n_7_n_10_n_714("single",1);
	this.instance_7.setTransform(163.5,888.9,1,1,0,0,180,19.1,40.5);

	this.timeline.addTween(cjs.Tween.get(this.instance_7).to({skewX:-29.9992,skewY:150.0008,x:195.1,y:235.75},10,cjs.Ease.quadInOut).to({skewX:0,skewY:180,x:163.5,y:320.4},10,cjs.Ease.quadInOut).to({startPosition:1},7,cjs.Ease.quadInOut).to({startPosition:1},6,cjs.Ease.quadInOut).wait(9).to({startPosition:1},0).to({startPosition:1},4).wait(5).to({startPosition:1},0).to({_off:true},1).wait(41));

	// 캐릭터_모모_다리
	this.instance_8 = new lib.캐릭터_모모_다리_n_7_n_10_n_714("single",0);
	this.instance_8.setTransform(163.4,857.6,1,1,0,0,180,19.2,9.2);

	this.timeline.addTween(cjs.Tween.get(this.instance_8).to({regY:9.3,skewX:-29.9992,skewY:150.0008,x:179.4,y:208.8},10,cjs.Ease.quadInOut).to({regY:9.2,skewX:0,skewY:180,x:163.4,y:289.1},10,cjs.Ease.quadInOut).to({startPosition:0},7,cjs.Ease.quadInOut).to({startPosition:0},6,cjs.Ease.quadInOut).wait(9).to({startPosition:0},0).to({startPosition:0},4).wait(5).to({startPosition:0},0).to({_off:true},1).wait(41));

	// 캐릭터_모모_다리
	this.instance_9 = new lib.캐릭터_모모_다리_n_7_n_10_n_714("single",1);
	this.instance_9.setTransform(92.15,888.9,1,1,0,0,0,19.1,40.5);

	this.timeline.addTween(cjs.Tween.get(this.instance_9).to({rotation:25.7095,x:66.1,y:231.85},10,cjs.Ease.quadInOut).to({rotation:0,x:92.15,y:320.4},10,cjs.Ease.quadInOut).to({startPosition:1},7,cjs.Ease.quadInOut).to({startPosition:1},6,cjs.Ease.quadInOut).wait(9).to({startPosition:1},0).to({startPosition:1},4).wait(5).to({startPosition:1},0).to({_off:true},1).wait(41));

	// 캐릭터_모모_다리
	this.instance_10 = new lib.캐릭터_모모_다리_n_7_n_10_n_714("single",0);
	this.instance_10.setTransform(92.25,857.6,1,1,0,0,0,19.2,9.2);

	this.timeline.addTween(cjs.Tween.get(this.instance_10).to({regY:9.3,rotation:25.7095,x:79.7,y:203.8},10,cjs.Ease.quadInOut).to({regY:9.2,rotation:0,x:92.25,y:289.1},10,cjs.Ease.quadInOut).to({startPosition:0},7,cjs.Ease.quadInOut).to({startPosition:0},6,cjs.Ease.quadInOut).wait(9).to({startPosition:0},0).to({startPosition:0},4).wait(5).to({startPosition:0},0).to({_off:true},1).wait(41));

	// 캐릭터_모모_대롱
	this.instance_11 = new lib.캐릭터_모모_대롱_n_8_n_11_n_687("single",0);
	this.instance_11.setTransform(131.25,587,1,1,0,0,0,10.8,63);

	this.timeline.addTween(cjs.Tween.get(this.instance_11).to({regY:62.9,rotation:14.9983,y:-63.15},10,cjs.Ease.quadInOut).to({regX:10.9,rotation:-7.2654,x:131.35,y:27.6},10,cjs.Ease.quadInOut).to({regX:10.8,rotation:14.9983,x:131.25,y:13.95},7,cjs.Ease.quadInOut).to({rotation:-6.7255,y:18.45},6,cjs.Ease.quadInOut).to({regY:63,rotation:0,y:18.5},6).wait(3).to({startPosition:0},0).to({startPosition:0},4).wait(5).to({startPosition:0},0).to({_off:true},1).wait(41));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-115.2,-166.8,483,1116.2);


(lib.QUIZ_n_0 = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {s_intro:0,e_intro:64,s_ending:65,e_ending:280};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// timeline functions:
	this.frame_65 = function() {
		playSound("마법_버블_우웅");
	}
	this.frame_72 = function() {
		playSound("_4_깜짝퀴즈_2단원_1");
	}
	this.frame_211 = function() {
		playSound("정답사운드1");
	}
	this.frame_236 = function() {
		playSound("공통_깜짝퀴즈_잘했어mp3copy");
	}
	this.frame_246 = function() {
		playSound("뽀옹");
	}

	// actions tween:
	this.timeline.addTween(cjs.Tween.get(this).wait(65).call(this.frame_65).wait(7).call(this.frame_72).wait(139).call(this.frame_211).wait(25).call(this.frame_236).wait(10).call(this.frame_246).wait(40));

	// Layer_5
	this.btn_close = new lib.Symbol7_n_1();
	this.btn_close.name = "btn_close";
	this.btn_close.setTransform(1202.65,119.5,1,1,0,0,0,22.5,22.5);

	this.timeline.addTween(cjs.Tween.get(this.btn_close).wait(286));

	// Layer_10
	this.instance = new lib.심볼2("synched",0,false);
	this.instance.setTransform(467.25,152.8,1,1,0,0,0,105,59.4);
	this.instance._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance).wait(6).to({_off:false},0).wait(59).to({startPosition:34},0).to({scaleX:0.7,scaleY:0.7,x:143.25,y:106.8},13,cjs.Ease.quadInOut).wait(208));

	// Layer_7
	this.instance_1 = new lib.Symbol23vcf();
	this.instance_1.setTransform(895.2,324.55,0.55,0.55,0,0,0,92.2,29.2);
	this.instance_1._off = true;

	this.btn_start = new lib.Symbol23vcf();
	this.btn_start.name = "btn_start";
	this.btn_start.setTransform(968.75,294.55,1,1,0,0,0,92.2,29.1);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[]}).to({state:[{t:this.instance_1}]},37).to({state:[{t:this.instance_1}]},11).to({state:[{t:this.instance_1}]},8).to({state:[{t:this.btn_start}]},1).to({state:[]},8).wait(221));
	this.timeline.addTween(cjs.Tween.get(this.instance_1).wait(37).to({_off:false},0).to({scaleX:1.1,scaleY:1.1,x:984.7,y:292.6},11,cjs.Ease.quadInOut).to({regY:29.1,scaleX:1,scaleY:1,x:968.75,y:294.55},8,cjs.Ease.quadInOut).to({_off:true},1).wait(229));

	// Layer_3
	this.instance_2 = new lib.심볼5("synched",0);
	this.instance_2.setTransform(823.7,349.6,0.5,0.5,0,0,0,0,104.6);
	this.instance_2._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_2).wait(37).to({_off:false},0).to({regY:104.7,scaleX:1.1,scaleY:1.1,y:349.65},11,cjs.Ease.quadInOut).to({regY:104.6,scaleX:1,scaleY:1,y:349.6},8,cjs.Ease.quadInOut).to({_off:true},9).wait(221));

	// Layer_8
	this.instance_3 = new lib.VRV_n_2("synched",0);
	this.instance_3.setTransform(405.6,362.5,0.5,0.5,0,0,0,0,230);
	this.instance_3._off = true;

	this.timeline.addTween(cjs.Tween.get(this.instance_3).wait(79).to({_off:false},0).to({scaleX:1,scaleY:1},8,cjs.Ease.get(1)).wait(199));

	// Layer_1
	this.instance_4 = new lib.캐릭터_모모_깜짝퀴즈_등장("synched",0,false);
	this.instance_4.setTransform(639.45,366.85,1.0302,1.0302,0,0,0,131.4,153.6);

	this.instance_5 = new lib.캐릭터_모모_깜짝퀴즈_등장대기();
	this.instance_5.setTransform(639.45,366.85,1.0302,1.0302,0,0,0,131.4,153.6);

	this.instance_6 = new lib.캐릭터_모모_깜짝퀴즈2_n_13("synched",0);
	this.instance_6.setTransform(639.45,366.85,1.0302,1.0302,0,0,0,131.4,153.6);
	this.instance_6._off = true;

	this.instance_7 = new lib.캐릭터_모모_깜짝퀴즈3_n_14("synched",0,false);
	this.instance_7.setTransform(238.5,387.8,0.8807,0.8807,0,0,0,131.5,153.6);

	this.timeline.addTween(cjs.Tween.get({}).to({state:[{t:this.instance_4}]}).to({state:[{t:this.instance_5}]},52).to({state:[{t:this.instance_6}]},13).to({state:[{t:this.instance_6}]},1).to({state:[{t:this.instance_6}]},1).to({state:[{t:this.instance_6}]},1).to({state:[{t:this.instance_6}]},1).to({state:[{t:this.instance_6}]},1).to({state:[{t:this.instance_6}]},1).to({state:[{t:this.instance_6}]},1).to({state:[{t:this.instance_6}]},1).to({state:[{t:this.instance_6}]},1).to({state:[{t:this.instance_6}]},1).to({state:[{t:this.instance_6}]},1).to({state:[{t:this.instance_6}]},1).to({state:[{t:this.instance_6}]},1).to({state:[{t:this.instance_6}]},1).to({state:[{t:this.instance_7}]},156).wait(51));
	this.timeline.addTween(cjs.Tween.get(this.instance_6).wait(65).to({_off:false},0).wait(1).to({regX:128.7,regY:147.6,scaleX:1.0109,scaleY:1.0109,x:585,y:363.45,startPosition:1},0).wait(1).to({scaleX:0.993,scaleY:0.993,x:536.95,y:366.05,startPosition:2},0).wait(1).to({scaleX:0.9763,scaleY:0.9763,x:492.35,y:368.5,startPosition:3},0).wait(1).to({scaleX:0.961,scaleY:0.961,x:451.35,y:370.75,startPosition:4},0).wait(1).to({scaleX:0.9471,scaleY:0.9471,x:413.9,y:372.8,startPosition:5},0).wait(1).to({scaleX:0.9344,scaleY:0.9344,x:379.95,y:374.65,startPosition:6},0).wait(1).to({scaleX:0.9231,scaleY:0.9231,x:349.55,y:376.3,startPosition:7},0).wait(1).to({scaleX:0.9131,scaleY:0.9131,x:322.7,y:377.75,startPosition:8},0).wait(1).to({scaleX:0.9044,scaleY:0.9044,x:299.45,y:379.05,startPosition:9},0).wait(1).to({scaleX:0.897,scaleY:0.897,x:279.7,y:380.15,startPosition:10},0).wait(1).to({scaleX:0.891,scaleY:0.891,x:263.45,y:381,startPosition:11},0).wait(1).to({scaleX:0.8862,scaleY:0.8862,x:250.8,y:381.7,startPosition:12},0).wait(1).to({scaleX:0.8828,scaleY:0.8828,x:241.65,y:382.2,startPosition:13},0).wait(1).to({regX:131.5,regY:153.6,scaleX:0.8807,scaleY:0.8807,x:238.5,y:387.8,startPosition:14,loop:false},0).to({_off:true},156).wait(51));

	// Layer_9
	this.instance_8 = new lib.ㄴㅁㅇㅁㄴㅇㄻㄴㅇㅁㄴcopy_n_19_n_3();
	this.instance_8.setTransform(605.7,82.45,1,1,0,0,0,243.8,33.6);

	this.timeline.addTween(cjs.Tween.get(this.instance_8).to({_off:true},65).wait(221));

	// Layer_6
	this.shape = new cjs.Shape();
	this.shape.graphics.f("rgba(0,0,0,0.698)").s().p("EhuzA7FMAAAh2JMDdnAAAMAAAB2Jg");
	this.shape.setTransform(651.175,368.125);

	this.timeline.addTween(cjs.Tween.get(this.shape).wait(286));

	// Layer_2
	this.shape_1 = new cjs.Shape();
	this.shape_1.graphics.rf(["rgba(0,0,0,0.498)","rgba(0,0,0,0)"],[0.502,1],0,0,0,0,0,615).s().p("Ehj/AxnMAAAhjNMDH/AAAMAAABjNg");
	this.shape_1.setTransform(640,362.5);

	this.timeline.addTween(cjs.Tween.get(this.shape_1).wait(286));

	this._renderFirstFrame();

}).prototype = p = new cjs.MovieClip();
p.nominalBounds = new cjs.Rectangle(-146.5,-9.9,1610.1,1196.5);


// stage content:
(lib.animation = function(mode,startPosition,loop,reversed) {
if (loop == null) { loop = true; }
if (reversed == null) { reversed = false; }
	var props = new Object();
	props.mode = mode;
	props.startPosition = startPosition;
	props.labels = {};
	props.loop = loop;
	props.reversed = reversed;
	cjs.MovieClip.apply(this,[props]);

	// Layer_1
	this.mc_ani = new lib.QUIZ_n_0();
	this.mc_ani.name = "mc_ani";
	this.mc_ani.setTransform(43,43,1,1,0,0,0,43,43);

	this.timeline.addTween(cjs.Tween.get(this.mc_ani).wait(1));

	this._renderFirstFrame();

}).prototype = p = new lib.AnMovieClip();
p.nominalBounds = new cjs.Rectangle(493.5,350.1,970.0999999999999,836.4999999999999);
// library properties:
lib.properties = {
	id: '554F53FD8DB39B44B0F09FA5FC0170F2',
	width: 1280,
	height: 720,
	fps: 30,
	color: "#999999",
	opacity: 1.00,
	manifest: [
		{src:"images/Bitmap10.png", id:"Bitmap10"},
		{src:"images/animation_atlas_1.png", id:"animation_atlas_1"},
		{src:"images/animation_atlas_2.png", id:"animation_atlas_2"},
		{src:"images/animation_atlas_3.png", id:"animation_atlas_3"},
		{src:"images/animation_atlas_4.png", id:"animation_atlas_4"},
		{src:"images/animation_atlas_5.png", id:"animation_atlas_5"},
		{src:"images/animation_atlas_6.png", id:"animation_atlas_6"},
		{src:"images/animation_atlas_7.png", id:"animation_atlas_7"},
		{src:"images/animation_atlas_8.png", id:"animation_atlas_8"},
		{src:"images/animation_atlas_9.png", id:"animation_atlas_9"},
		{src:"sounds/정답사운드1.mp3", id:"정답사운드1"},
		{src:"sounds/_4_깜짝퀴즈_2단원_1.mp3", id:"_4_깜짝퀴즈_2단원_1"},
		{src:"sounds/뽀옹_.mp3", id:"뽀옹"},
		{src:"sounds/공통_깜짝퀴즈_잘했어mp3copy.mp3", id:"공통_깜짝퀴즈_잘했어mp3copy"},
		{src:"sounds/마법_버블_우웅.mp3", id:"마법_버블_우웅"}
	],
	preloads: []
};



// bootstrap callback support:

(lib.Stage = function(canvas) {
	createjs.Stage.call(this, canvas);
}).prototype = p = new createjs.Stage();

p.setAutoPlay = function(autoPlay) {
	this.tickEnabled = autoPlay;
}
p.play = function() { this.tickEnabled = true; this.getChildAt(0).gotoAndPlay(this.getTimelinePosition()) }
p.stop = function(ms) { if(ms) this.seek(ms); this.tickEnabled = false; }
p.seek = function(ms) { this.tickEnabled = true; this.getChildAt(0).gotoAndStop(lib.properties.fps * ms / 1000); }
p.getDuration = function() { return this.getChildAt(0).totalFrames / lib.properties.fps * 1000; }

p.getTimelinePosition = function() { return this.getChildAt(0).currentFrame / lib.properties.fps * 1000; }

an.bootcompsLoaded = an.bootcompsLoaded || [];
if(!an.bootstrapListeners) {
	an.bootstrapListeners=[];
}

an.bootstrapCallback=function(fnCallback) {
	an.bootstrapListeners.push(fnCallback);
	if(an.bootcompsLoaded.length > 0) {
		for(var i=0; i<an.bootcompsLoaded.length; ++i) {
			fnCallback(an.bootcompsLoaded[i]);
		}
	}
};

an.compositions = an.compositions || {};
an.compositions['554F53FD8DB39B44B0F09FA5FC0170F2'] = {
	getStage: function() { return exportRoot.stage; },
	getLibrary: function() { return lib; },
	getSpriteSheet: function() { return ss; },
	getImages: function() { return img; }
};

an.compositionLoaded = function(id) {
	an.bootcompsLoaded.push(id);
	for(var j=0; j<an.bootstrapListeners.length; j++) {
		an.bootstrapListeners[j](id);
	}
}

an.getComposition = function(id) {
	return an.compositions[id];
}


an.makeResponsive = function(isResp, respDim, isScale, scaleType, domContainers) {		
	var lastW, lastH, lastS=1;		
	window.addEventListener('resize', resizeCanvas);		
	resizeCanvas();		
	function resizeCanvas() {			
		var w = lib.properties.width, h = lib.properties.height;			
		var iw = window.innerWidth, ih=window.innerHeight;			
		var pRatio = window.devicePixelRatio || 1, xRatio=iw/w, yRatio=ih/h, sRatio=1;			
		if(isResp) {                
			if((respDim=='width'&&lastW==iw) || (respDim=='height'&&lastH==ih)) {                    
				sRatio = lastS;                
			}				
			else if(!isScale) {					
				if(iw<w || ih<h)						
					sRatio = Math.min(xRatio, yRatio);				
			}				
			else if(scaleType==1) {					
				sRatio = Math.min(xRatio, yRatio);				
			}				
			else if(scaleType==2) {					
				sRatio = Math.max(xRatio, yRatio);				
			}			
		}
		domContainers[0].width = w * pRatio * sRatio;			
		domContainers[0].height = h * pRatio * sRatio;
		domContainers.forEach(function(container) {				
			container.style.width = w * sRatio + 'px';				
			container.style.height = h * sRatio + 'px';			
		});
		stage.scaleX = pRatio*sRatio;			
		stage.scaleY = pRatio*sRatio;
		lastW = iw; lastH = ih; lastS = sRatio;            
		stage.tickOnUpdate = false;            
		stage.update();            
		stage.tickOnUpdate = true;		
	}
}
an.handleSoundStreamOnTick = function(event) {
	if(!event.paused){
		var stageChild = stage.getChildAt(0);
		if(!stageChild.paused || stageChild.ignorePause){
			stageChild.syncStreamSounds();
		}
	}
}
an.handleFilterCache = function(event) {
	if(!event.paused){
		var target = event.target;
		if(target){
			if(target.filterCacheList){
				for(var index = 0; index < target.filterCacheList.length ; index++){
					var cacheInst = target.filterCacheList[index];
					if((cacheInst.startFrame <= target.currentFrame) && (target.currentFrame <= cacheInst.endFrame)){
						cacheInst.instance.cache(cacheInst.x, cacheInst.y, cacheInst.w, cacheInst.h);
					}
				}
			}
		}
	}
}


})(createjs = createjs||{}, AdobeAn = AdobeAn||{});
var createjs, AdobeAn;