# Gemini 프로젝트 분석

## 개요

이 프로젝트는 대화형 웹 기반 교육 애플리케이션으로 보입니다. 파일 및 디렉토리 이름(예: `popQuiz`, `content01.html`, `activity`, 교육 스타일 이름의 다양한 `.mp3` 사운드 파일)으로 미루어 보아 학생들을 위한 이러닝을 대상으로 할 가능성이 높습니다.

## 주요 기술

*   **프론트엔드:** HTML, CSS, JavaScript.
*   **빌드 프로세스:** `.bundle.js` 및 `.bundle.css` 파일(예: `content01.bundle.js`)의 존재는 Webpack, Rollup 또는 Parcel과 같은 JavaScript 모듈 번들러를 사용했음을 강력하게 시사합니다. 원본 소스 코드는 생성된 번들 파일과 별개일 가능성이 높습니다.
*   **라이브러리:** `dtext_api_wrapper_ldev.js`의 목적은 내용을 검사하지 않고는 불분명하지만, 학습 플랫폼이나 디지털 교과서 서비스를 위한 특정 API 래퍼일 수 있습니다.

## 프로젝트 구조

프로젝트는 기능 및 파일 유형별로 구성되어 있습니다.

*   **HTML 진입점:** `index.html`, `content01.html`, `content02.html`이 메인 페이지입니다.
*   **번들된 자산:**
    *   `js/`: 각 페이지에 대해 컴파일/번들된 JavaScript를 포함합니다.
    *   `css/`: 번들된 CSS를 포함합니다.
*   **정적 자산:**
    *   `images/`: 일반 및 UI 이미지를 포함합니다.
    *   `media/`: 오디오 파일을 포함합니다.
    *   `common/`: 공통 이미지 및 오디오 클립(UI 사운드 등)과 같은 공유 자산을 포함합니다.
*   **컴포넌트/모듈:**
    *   `cc/`: 자체 애니메이션, 이미지, 사운드를 갖춘 독립적인 컴포넌트로 보입니다. `repeat` 하위 디렉토리는 특정 유형의 활동을 나타냅니다.
    *   `popQuiz/`: 팝업 퀴즈 기능에 대한 자산을 포함합니다.

## 규칙

*   **자산 이름 지정:** 오디오 파일은 교육과정 구조와 관련된 특정 명명 규칙을 따르는 것으로 보입니다(예: `_4_2단원_01표현익히기_11.mp3`).
*   **컴포넌트 구조:** `cc` 및 `popQuiz`와 같은 기능은 자체 디렉토리로 구성되어 특정 리소스(JavaScript, 이미지, 사운드)를 캡슐화합니다.
*   **빌드 결과물:** 모든 실행 가능한 코드는 번들로 제공되므로 `.bundle.js` 파일을 직접 편집하는 것은 권장되지 않습니다. 원본 소스 파일은 이 디렉토리 목록에 없습니다.

## 이 프로젝트 작업 방법

*   **프로젝트 실행:** 웹 브라우저에서 `index.html` 파일을 엽니다.
*   **변경 사항 적용:** 일반적으로 원본 소스 코드와 빌드 스크립트에 접근해야 합니다. 소스 파일을 수정한 다음 프로젝트를 다시 빌드하여 업데이트된 `bundle` 파일을 생성해야 합니다.